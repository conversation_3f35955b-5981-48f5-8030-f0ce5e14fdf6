[{"E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\index.tsx": "1", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\reportWebVitals.ts": "2", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\App.tsx": "3", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\store\\UserInfoStore.ts": "4", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\Drawer.tsx": "5", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\AuthProvider.tsx": "6", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\login\\AccountContext.tsx": "7", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\SnackbarContext.tsx": "8", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\store\\drawerStore.ts": "9", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\store\\historyStore.ts": "10", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\store\\userSession.ts": "11", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\GuidePopUp.tsx": "12", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\GuideSettings.tsx": "13", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\LogoutPopup.tsx": "14", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\login\\ExtensionLogin.tsx": "15", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\GuideMenuOptions.tsx": "16", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\ScrapingService.ts": "17", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\UserService.ts": "18", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\SaveGuideService.tsx": "19", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\GuideListServices.tsx": "20", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\assets\\icons\\icons.ts": "21", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\UseAuth.tsx": "22", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistLauncherPreview.tsx": "23", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistPopup.tsx": "24", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\UndoRedoButtons.tsx": "25", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\TrainingField.tsx": "26", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Design.tsx": "27", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\Banners.tsx": "28", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Bannerspreview\\Banner.tsx": "29", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\AnnouncementPreview.tsx": "30", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\HotspotPreview.tsx": "31", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\tourTemplate.tsx": "32", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\Tooltip.tsx": "33", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\stepPopup.tsx": "34", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\tourPreview.tsx": "35", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\tooltippreview\\Tooltips\\Tooltipuserview.tsx": "36", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\OrganizationService.ts": "37", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\FileService.tsx": "38", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\LoginService.tsx": "39", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\utils\\historyUtils.ts": "40", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\ElementRules.tsx": "41", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PageTrigger.tsx": "42", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\AlertPopup.tsx": "43", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\StopScrapingButton.tsx": "44", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\PopupList.tsx": "45", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\OidcConfig.ts": "46", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\Imagesection.tsx": "47", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\APIService.tsx": "48", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\RTEsection.tsx": "49", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\VideoSection.tsx": "50", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\Button.tsx": "51", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistPreview.tsx": "52", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\HtmlSection.tsx": "53", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AIAgent\\CreateWithAIButton.tsx": "54", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AIAgent\\ModernChatWindow.tsx": "55", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistCheckIcon.tsx": "56", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\LauncherSettings.tsx": "57", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ImageCarousel.tsx": "58", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\VideoPlayer.tsx": "59", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\Chekpoints.tsx": "60", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\CanvasSettings.tsx": "61", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Overlay.tsx": "62", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\ElementsSettings.tsx": "63", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Animation.tsx": "64", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\CustomCss.tsx": "65", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistCanvasSettings.tsx": "66", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\TitleSubTitle.tsx": "67", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\PageInteraction.tsx": "68", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ButtonSettings.tsx": "69", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageSectionField.tsx": "70", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\hotspot\\HotspotSettings.tsx": "71", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\BannerStepPreview.tsx": "72", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\TooltipBody.tsx": "73", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\tooltippreview\\Tooltips\\Tooltips.tsx": "74", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\designFields\\TooltipCanvasSettings.tsx": "75", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\CloneGuidePopUp.tsx": "76", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\TimeZoneConversion.tsx": "77", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\SelectImageFromApplication.tsx": "78", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\SpeechRecognitionService.ts": "79", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\AIService.ts": "80", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\DraggableCheckpoint.tsx": "81", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\CheckpointEditPopup.tsx": "82", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\CheckpointAddPopup.tsx": "83", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageProperties.tsx": "84", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageGalleryPopup.tsx": "85", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\Buttons.tsx": "86", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\ImageSection.tsx": "87", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\RTE\\RTESection.tsx": "88", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\ButtonSetting.tsx": "89", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\UserRoleService.ts": "90", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\multilinguial\\i18n.ts": "91", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\contexts\\TranslationContext.tsx": "92", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\LanguageSelector.tsx": "93", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\multilinguial\\LanguageService.ts": "94", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\AgentTraining.tsx": "95", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\AgentAdditionalContextPopup.tsx": "96"}, {"size": 604, "mtime": 1748929949904, "results": "97", "hashOfConfig": "98"}, {"size": 440, "mtime": 1748929949904, "results": "99", "hashOfConfig": "98"}, {"size": 2871, "mtime": 1753273113372, "results": "100", "hashOfConfig": "98"}, {"size": 3344, "mtime": 1753158986544, "results": "101", "hashOfConfig": "98"}, {"size": 248304, "mtime": 1753765553712, "results": "102", "hashOfConfig": "98"}, {"size": 6890, "mtime": 1753072098544, "results": "103", "hashOfConfig": "98"}, {"size": 3102, "mtime": 1753681081054, "results": "104", "hashOfConfig": "98"}, {"size": 3112, "mtime": 1753072087888, "results": "105", "hashOfConfig": "98"}, {"size": 397058, "mtime": 1753706598709, "results": "106", "hashOfConfig": "98"}, {"size": 3927, "mtime": 1748930023076, "results": "107", "hashOfConfig": "98"}, {"size": 6144, "mtime": 1749531263982, "results": "108", "hashOfConfig": "98"}, {"size": 41974, "mtime": 1753680638476, "results": "109", "hashOfConfig": "98"}, {"size": 5077, "mtime": 1753273113730, "results": "110", "hashOfConfig": "98"}, {"size": 3731, "mtime": 1753270938486, "results": "111", "hashOfConfig": "98"}, {"size": 14132, "mtime": 1753681081056, "results": "112", "hashOfConfig": "98"}, {"size": 13281, "mtime": 1753681081050, "results": "113", "hashOfConfig": "98"}, {"size": 56751, "mtime": 1753681081061, "results": "114", "hashOfConfig": "98"}, {"size": 1898, "mtime": 1748930023076, "results": "115", "hashOfConfig": "98"}, {"size": 1954, "mtime": 1751432283612, "results": "116", "hashOfConfig": "98"}, {"size": 9087, "mtime": 1753158986542, "results": "117", "hashOfConfig": "98"}, {"size": 299011, "mtime": 1753765553712, "results": "118", "hashOfConfig": "98"}, {"size": 193, "mtime": 1748929949654, "results": "119", "hashOfConfig": "98"}, {"size": 9097, "mtime": 1753158986506, "results": "120", "hashOfConfig": "98"}, {"size": 30700, "mtime": 1753072098544, "results": "121", "hashOfConfig": "98"}, {"size": 3012, "mtime": 1753273113704, "results": "122", "hashOfConfig": "98"}, {"size": 2606, "mtime": 1753072098528, "results": "123", "hashOfConfig": "98"}, {"size": 33243, "mtime": 1753273113725, "results": "124", "hashOfConfig": "98"}, {"size": 23270, "mtime": 1753426022486, "results": "125", "hashOfConfig": "98"}, {"size": 13556, "mtime": 1753432486901, "results": "126", "hashOfConfig": "98"}, {"size": 26724, "mtime": 1753432486362, "results": "127", "hashOfConfig": "98"}, {"size": 49705, "mtime": 1753432487861, "results": "128", "hashOfConfig": "98"}, {"size": 7599, "mtime": 1753072087888, "results": "129", "hashOfConfig": "98"}, {"size": 32505, "mtime": 1753681081028, "results": "130", "hashOfConfig": "98"}, {"size": 11669, "mtime": 1753273113763, "results": "131", "hashOfConfig": "98"}, {"size": 24200, "mtime": 1751432283612, "results": "132", "hashOfConfig": "98"}, {"size": 4880, "mtime": 1750229130169, "results": "133", "hashOfConfig": "98"}, {"size": 9238, "mtime": 1748930023061, "results": "134", "hashOfConfig": "98"}, {"size": 1297, "mtime": 1748930023061, "results": "135", "hashOfConfig": "98"}, {"size": 1248, "mtime": 1748929949920, "results": "136", "hashOfConfig": "98"}, {"size": 14238, "mtime": 1748930023076, "results": "137", "hashOfConfig": "98"}, {"size": 2997, "mtime": 1753072087872, "results": "138", "hashOfConfig": "98"}, {"size": 3285, "mtime": 1753273113732, "results": "139", "hashOfConfig": "98"}, {"size": 2749, "mtime": 1753680638463, "results": "140", "hashOfConfig": "98"}, {"size": 2052, "mtime": 1753681081023, "results": "141", "hashOfConfig": "98"}, {"size": 20044, "mtime": 1753681081052, "results": "142", "hashOfConfig": "98"}, {"size": 743, "mtime": 1748929949654, "results": "143", "hashOfConfig": "98"}, {"size": 25466, "mtime": 1753273113732, "results": "144", "hashOfConfig": "98"}, {"size": 2608, "mtime": 1748930023061, "results": "145", "hashOfConfig": "98"}, {"size": 39935, "mtime": 1753769759581, "results": "146", "hashOfConfig": "98"}, {"size": 7772, "mtime": 1753072087872, "results": "147", "hashOfConfig": "98"}, {"size": 16105, "mtime": 1753273113732, "results": "148", "hashOfConfig": "98"}, {"size": 29119, "mtime": 1753158986510, "results": "149", "hashOfConfig": "98"}, {"size": 6245, "mtime": 1748929949857, "results": "150", "hashOfConfig": "98"}, {"size": 2034, "mtime": 1753072098528, "results": "151", "hashOfConfig": "98"}, {"size": 29744, "mtime": 1753158986497, "results": "152", "hashOfConfig": "98"}, {"size": 1962, "mtime": 1748929949654, "results": "153", "hashOfConfig": "98"}, {"size": 27258, "mtime": 1753273113679, "results": "154", "hashOfConfig": "98"}, {"size": 2423, "mtime": 1753681081036, "results": "155", "hashOfConfig": "98"}, {"size": 702, "mtime": 1753072087841, "results": "156", "hashOfConfig": "98"}, {"size": 13889, "mtime": 1753072087841, "results": "157", "hashOfConfig": "98"}, {"size": 19040, "mtime": 1753432486447, "results": "158", "hashOfConfig": "98"}, {"size": 6625, "mtime": 1753072087872, "results": "159", "hashOfConfig": "98"}, {"size": 20321, "mtime": 1753072087872, "results": "160", "hashOfConfig": "98"}, {"size": 3236, "mtime": 1748929949779, "results": "161", "hashOfConfig": "98"}, {"size": 2848, "mtime": 1748929949811, "results": "162", "hashOfConfig": "98"}, {"size": 15285, "mtime": 1753072087825, "results": "163", "hashOfConfig": "98"}, {"size": 15261, "mtime": 1753158986514, "results": "164", "hashOfConfig": "98"}, {"size": 11208, "mtime": 1753432486451, "results": "165", "hashOfConfig": "98"}, {"size": 17396, "mtime": 1753681081048, "results": "166", "hashOfConfig": "98"}, {"size": 8476, "mtime": 1753072087856, "results": "167", "hashOfConfig": "98"}, {"size": 15571, "mtime": 1753072098560, "results": "168", "hashOfConfig": "98"}, {"size": 16126, "mtime": 1753432481019, "results": "169", "hashOfConfig": "98"}, {"size": 33783, "mtime": 1753680638460, "results": "170", "hashOfConfig": "98"}, {"size": 60407, "mtime": 1753426020893, "results": "171", "hashOfConfig": "98"}, {"size": 26698, "mtime": 1753432486147, "results": "172", "hashOfConfig": "98"}, {"size": 5258, "mtime": 1753072087872, "results": "173", "hashOfConfig": "98"}, {"size": 883, "mtime": 1748929949889, "results": "174", "hashOfConfig": "98"}, {"size": 3117, "mtime": 1753273113699, "results": "175", "hashOfConfig": "98"}, {"size": 7943, "mtime": 1748930023061, "results": "176", "hashOfConfig": "98"}, {"size": 1092, "mtime": 1753681081058, "results": "177", "hashOfConfig": "98"}, {"size": 5504, "mtime": 1753072087841, "results": "178", "hashOfConfig": "98"}, {"size": 33137, "mtime": 1753273113673, "results": "179", "hashOfConfig": "98"}, {"size": 37236, "mtime": 1753273113667, "results": "180", "hashOfConfig": "98"}, {"size": 2931, "mtime": 1749010760558, "results": "181", "hashOfConfig": "98"}, {"size": 2669, "mtime": 1748929949748, "results": "182", "hashOfConfig": "98"}, {"size": 17277, "mtime": 1753273113497, "results": "183", "hashOfConfig": "98"}, {"size": 27631, "mtime": 1753273113575, "results": "184", "hashOfConfig": "98"}, {"size": 18953, "mtime": 1753765553712, "results": "185", "hashOfConfig": "98"}, {"size": 15667, "mtime": 1753681081033, "results": "186", "hashOfConfig": "98"}, {"size": 677, "mtime": 1753072098575, "results": "187", "hashOfConfig": "98"}, {"size": 6886, "mtime": 1753158986540, "results": "188", "hashOfConfig": "98"}, {"size": 7158, "mtime": 1753158986535, "results": "189", "hashOfConfig": "98"}, {"size": 10166, "mtime": 1753681081038, "results": "190", "hashOfConfig": "98"}, {"size": 1211, "mtime": 1753158986538, "results": "191", "hashOfConfig": "98"}, {"size": 3836, "mtime": 1753681081020, "results": "192", "hashOfConfig": "98"}, {"size": 3021, "mtime": 1753681081019, "results": "193", "hashOfConfig": "98"}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1c51j82", {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 224, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 21, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 40, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 24, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 27, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 61, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 22, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 21, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 25, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 27, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 50, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 21, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 27, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 54, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 57, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 33, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 31, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 28, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 62, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 19, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 51, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "419", "messages": "420", "suppressedMessages": "421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "422", "messages": "423", "suppressedMessages": "424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "425", "messages": "426", "suppressedMessages": "427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "428", "messages": "429", "suppressedMessages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "431", "messages": "432", "suppressedMessages": "433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "434", "messages": "435", "suppressedMessages": "436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 29, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "437", "messages": "438", "suppressedMessages": "439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 49, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "440", "messages": "441", "suppressedMessages": "442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 62, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "443", "messages": "444", "suppressedMessages": "445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "446", "messages": "447", "suppressedMessages": "448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "449", "messages": "450", "suppressedMessages": "451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "452", "messages": "453", "suppressedMessages": "454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "455", "messages": "456", "suppressedMessages": "457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 32, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "458", "messages": "459", "suppressedMessages": "460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "461", "messages": "462", "suppressedMessages": "463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "464", "messages": "465", "suppressedMessages": "466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "467", "messages": "468", "suppressedMessages": "469", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "470", "messages": "471", "suppressedMessages": "472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "473", "messages": "474", "suppressedMessages": "475", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "476", "messages": "477", "suppressedMessages": "478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "479", "messages": "480", "suppressedMessages": "481", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\index.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\reportWebVitals.ts", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\App.tsx", ["482", "483", "484"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\store\\UserInfoStore.ts", ["485"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\Drawer.tsx", ["486", "487", "488", "489", "490", "491", "492", "493", "494", "495", "496", "497", "498", "499", "500", "501", "502", "503", "504", "505", "506", "507", "508", "509", "510", "511", "512", "513", "514", "515", "516", "517", "518", "519", "520", "521", "522", "523", "524", "525", "526", "527", "528", "529", "530", "531", "532", "533", "534", "535", "536", "537", "538", "539", "540", "541", "542", "543", "544", "545", "546", "547", "548", "549", "550", "551", "552", "553", "554", "555", "556", "557", "558", "559", "560", "561", "562", "563", "564", "565", "566", "567", "568", "569", "570", "571", "572", "573", "574", "575", "576", "577", "578", "579", "580", "581", "582", "583", "584", "585", "586", "587", "588", "589", "590", "591", "592", "593", "594", "595", "596", "597", "598", "599", "600", "601", "602", "603", "604", "605", "606", "607", "608", "609", "610", "611", "612", "613", "614", "615", "616", "617", "618", "619", "620", "621", "622", "623", "624", "625", "626", "627", "628", "629", "630", "631", "632", "633", "634", "635", "636", "637", "638", "639", "640", "641", "642", "643", "644", "645", "646", "647", "648", "649", "650", "651", "652", "653", "654", "655", "656", "657", "658", "659", "660", "661", "662", "663", "664", "665", "666", "667", "668", "669", "670", "671", "672", "673", "674", "675", "676", "677", "678", "679", "680", "681", "682", "683", "684", "685", "686", "687", "688", "689", "690", "691", "692", "693", "694", "695", "696", "697", "698", "699", "700", "701", "702", "703", "704", "705", "706", "707", "708", "709"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\AuthProvider.tsx", ["710", "711", "712", "713", "714", "715"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\login\\AccountContext.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\SnackbarContext.tsx", ["716", "717"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\store\\drawerStore.ts", ["718", "719", "720", "721", "722", "723", "724", "725", "726", "727", "728", "729", "730", "731", "732", "733", "734", "735", "736", "737", "738"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\store\\historyStore.ts", ["739"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\store\\userSession.ts", ["740"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\GuidePopUp.tsx", ["741", "742", "743", "744", "745", "746", "747", "748", "749", "750", "751", "752", "753", "754", "755", "756", "757", "758", "759", "760", "761", "762", "763", "764", "765", "766", "767", "768", "769", "770", "771", "772", "773", "774", "775", "776", "777", "778", "779", "780"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\GuideSettings.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\LogoutPopup.tsx", ["781"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\login\\ExtensionLogin.tsx", ["782", "783", "784", "785", "786", "787", "788", "789", "790", "791", "792", "793", "794", "795", "796", "797"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\GuideMenuOptions.tsx", ["798", "799", "800", "801", "802", "803", "804", "805", "806", "807", "808", "809", "810", "811", "812", "813", "814", "815", "816", "817", "818", "819", "820", "821"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\ScrapingService.ts", ["822", "823", "824", "825", "826", "827", "828", "829", "830", "831", "832", "833", "834", "835", "836"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\UserService.ts", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\SaveGuideService.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\GuideListServices.tsx", ["837", "838", "839"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\assets\\icons\\icons.ts", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\UseAuth.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistLauncherPreview.tsx", ["840", "841", "842", "843", "844", "845", "846", "847"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistPopup.tsx", ["848", "849", "850", "851", "852", "853", "854", "855", "856", "857", "858", "859", "860", "861", "862", "863", "864", "865", "866", "867", "868", "869", "870", "871", "872", "873", "874"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\UndoRedoButtons.tsx", ["875", "876", "877", "878"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\TrainingField.tsx", ["879", "880", "881", "882", "883", "884", "885", "886", "887", "888", "889", "890", "891", "892", "893", "894"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Design.tsx", ["895", "896", "897", "898", "899", "900", "901", "902", "903", "904", "905", "906", "907", "908", "909", "910", "911", "912", "913", "914", "915", "916", "917", "918", "919", "920", "921", "922", "923", "924", "925", "926", "927", "928", "929", "930", "931", "932", "933", "934", "935", "936", "937", "938", "939", "940", "941", "942", "943", "944", "945", "946", "947", "948", "949", "950", "951", "952", "953", "954", "955"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\Banners.tsx", ["956", "957", "958", "959", "960", "961", "962", "963", "964", "965", "966", "967", "968", "969", "970", "971", "972", "973", "974", "975", "976", "977"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Bannerspreview\\Banner.tsx", ["978", "979", "980", "981", "982", "983", "984", "985", "986", "987", "988", "989", "990", "991", "992", "993", "994", "995", "996", "997", "998"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\AnnouncementPreview.tsx", ["999", "1000", "1001", "1002", "1003", "1004", "1005", "1006", "1007", "1008", "1009", "1010", "1011", "1012", "1013", "1014", "1015", "1016", "1017", "1018", "1019", "1020", "1021", "1022", "1023"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\HotspotPreview.tsx", ["1024", "1025", "1026", "1027", "1028", "1029", "1030", "1031", "1032", "1033", "1034", "1035", "1036", "1037", "1038", "1039", "1040", "1041", "1042", "1043", "1044", "1045", "1046", "1047", "1048", "1049", "1050"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\tourTemplate.tsx", ["1051", "1052", "1053", "1054", "1055", "1056", "1057", "1058", "1059", "1060"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\Tooltip.tsx", ["1061", "1062", "1063", "1064", "1065", "1066", "1067", "1068", "1069", "1070", "1071", "1072", "1073", "1074", "1075", "1076", "1077", "1078", "1079", "1080", "1081", "1082", "1083", "1084", "1085", "1086", "1087", "1088", "1089", "1090", "1091", "1092", "1093", "1094", "1095", "1096", "1097", "1098", "1099", "1100", "1101", "1102", "1103", "1104", "1105", "1106", "1107", "1108", "1109", "1110"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\stepPopup.tsx", ["1111", "1112", "1113", "1114", "1115", "1116", "1117", "1118", "1119"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\tourPreview.tsx", ["1120", "1121", "1122", "1123", "1124", "1125", "1126", "1127", "1128", "1129", "1130", "1131", "1132", "1133", "1134", "1135", "1136", "1137", "1138", "1139", "1140"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\tooltippreview\\Tooltips\\Tooltipuserview.tsx", ["1141", "1142", "1143", "1144", "1145", "1146"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\OrganizationService.ts", ["1147", "1148", "1149", "1150"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\FileService.tsx", ["1151", "1152"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\LoginService.tsx", ["1153", "1154", "1155"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\utils\\historyUtils.ts", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\ElementRules.tsx", ["1156", "1157"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PageTrigger.tsx", ["1158"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\AlertPopup.tsx", ["1159", "1160", "1161"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\StopScrapingButton.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\PopupList.tsx", ["1162", "1163", "1164", "1165", "1166", "1167", "1168", "1169", "1170", "1171", "1172", "1173", "1174", "1175"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\OidcConfig.ts", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\Imagesection.tsx", ["1176", "1177", "1178"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\APIService.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\RTEsection.tsx", ["1179", "1180", "1181", "1182", "1183", "1184", "1185"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\VideoSection.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\Button.tsx", ["1186", "1187", "1188", "1189", "1190", "1191", "1192", "1193", "1194", "1195", "1196", "1197", "1198", "1199", "1200", "1201", "1202", "1203", "1204", "1205", "1206", "1207", "1208", "1209", "1210", "1211", "1212"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistPreview.tsx", ["1213", "1214", "1215", "1216", "1217", "1218", "1219", "1220", "1221", "1222", "1223"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\HtmlSection.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AIAgent\\CreateWithAIButton.tsx", ["1224", "1225", "1226", "1227", "1228"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AIAgent\\ModernChatWindow.tsx", ["1229", "1230", "1231", "1232", "1233", "1234", "1235", "1236", "1237", "1238", "1239", "1240", "1241", "1242"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistCheckIcon.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\LauncherSettings.tsx", ["1243", "1244", "1245", "1246", "1247", "1248", "1249", "1250", "1251", "1252", "1253", "1254", "1255", "1256", "1257", "1258", "1259", "1260", "1261", "1262", "1263", "1264", "1265", "1266", "1267", "1268", "1269", "1270", "1271", "1272", "1273", "1274", "1275", "1276", "1277", "1278", "1279", "1280", "1281", "1282", "1283", "1284", "1285", "1286", "1287", "1288", "1289", "1290", "1291", "1292", "1293", "1294", "1295", "1296"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ImageCarousel.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\VideoPlayer.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\Chekpoints.tsx", ["1297", "1298", "1299", "1300", "1301", "1302", "1303", "1304", "1305", "1306", "1307", "1308", "1309", "1310", "1311", "1312", "1313", "1314", "1315", "1316", "1317", "1318", "1319", "1320", "1321", "1322", "1323", "1324", "1325", "1326", "1327", "1328", "1329", "1330", "1331", "1332", "1333", "1334", "1335", "1336", "1337", "1338", "1339", "1340", "1341", "1342", "1343", "1344", "1345", "1346", "1347", "1348", "1349", "1350", "1351", "1352", "1353"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\CanvasSettings.tsx", ["1354", "1355", "1356", "1357"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Overlay.tsx", ["1358", "1359", "1360", "1361", "1362", "1363"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\ElementsSettings.tsx", ["1364", "1365", "1366", "1367", "1368", "1369", "1370", "1371", "1372", "1373", "1374", "1375", "1376", "1377", "1378", "1379", "1380", "1381", "1382", "1383"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Animation.tsx", ["1384", "1385", "1386"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\CustomCss.tsx", ["1387", "1388"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistCanvasSettings.tsx", ["1389", "1390", "1391", "1392", "1393", "1394", "1395", "1396", "1397", "1398", "1399", "1400", "1401", "1402", "1403", "1404", "1405", "1406", "1407", "1408", "1409", "1410", "1411", "1412", "1413", "1414", "1415", "1416", "1417", "1418", "1419", "1420", "1421"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\TitleSubTitle.tsx", ["1422", "1423", "1424", "1425", "1426", "1427", "1428", "1429", "1430", "1431", "1432", "1433", "1434", "1435", "1436", "1437", "1438", "1439", "1440", "1441", "1442", "1443", "1444", "1445", "1446", "1447", "1448", "1449", "1450", "1451", "1452"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\PageInteraction.tsx", ["1453", "1454", "1455", "1456", "1457", "1458", "1459", "1460", "1461", "1462", "1463", "1464", "1465", "1466", "1467", "1468", "1469", "1470", "1471", "1472", "1473", "1474", "1475", "1476", "1477", "1478", "1479", "1480"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ButtonSettings.tsx", ["1481", "1482", "1483", "1484", "1485", "1486", "1487", "1488", "1489", "1490", "1491", "1492", "1493", "1494", "1495", "1496", "1497", "1498", "1499", "1500", "1501", "1502", "1503", "1504", "1505", "1506", "1507", "1508", "1509", "1510", "1511", "1512", "1513", "1514", "1515", "1516", "1517", "1518", "1519", "1520", "1521", "1522", "1523", "1524", "1525", "1526", "1527", "1528", "1529", "1530", "1531", "1532", "1533", "1534", "1535", "1536", "1537", "1538", "1539", "1540", "1541", "1542"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageSectionField.tsx", ["1543", "1544", "1545", "1546", "1547", "1548", "1549", "1550"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\hotspot\\HotspotSettings.tsx", ["1551", "1552", "1553", "1554", "1555", "1556", "1557", "1558", "1559", "1560", "1561", "1562", "1563", "1564", "1565", "1566", "1567", "1568", "1569"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\BannerStepPreview.tsx", ["1570", "1571", "1572", "1573", "1574", "1575", "1576", "1577", "1578", "1579", "1580", "1581", "1582", "1583", "1584", "1585", "1586", "1587", "1588", "1589"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\TooltipBody.tsx", ["1590", "1591", "1592", "1593", "1594", "1595", "1596", "1597", "1598", "1599", "1600", "1601", "1602", "1603", "1604", "1605", "1606", "1607", "1608", "1609", "1610", "1611", "1612", "1613", "1614", "1615", "1616", "1617", "1618", "1619", "1620", "1621", "1622", "1623", "1624", "1625", "1626", "1627", "1628", "1629", "1630", "1631", "1632", "1633", "1634", "1635", "1636", "1637", "1638", "1639", "1640"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\tooltippreview\\Tooltips\\Tooltips.tsx", ["1641", "1642", "1643", "1644", "1645", "1646", "1647", "1648", "1649", "1650", "1651", "1652", "1653", "1654", "1655", "1656"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\designFields\\TooltipCanvasSettings.tsx", ["1657", "1658", "1659", "1660", "1661", "1662", "1663", "1664", "1665", "1666", "1667", "1668", "1669", "1670", "1671", "1672", "1673"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\CloneGuidePopUp.tsx", ["1674", "1675"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\TimeZoneConversion.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\SelectImageFromApplication.tsx", ["1676", "1677", "1678", "1679", "1680", "1681", "1682", "1683", "1684", "1685"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\SpeechRecognitionService.ts", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\AIService.ts", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\DraggableCheckpoint.tsx", ["1686", "1687", "1688", "1689", "1690", "1691", "1692", "1693", "1694", "1695", "1696", "1697", "1698", "1699", "1700", "1701", "1702", "1703", "1704", "1705", "1706", "1707", "1708", "1709", "1710", "1711", "1712", "1713", "1714"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\CheckpointEditPopup.tsx", ["1715", "1716", "1717", "1718", "1719", "1720", "1721", "1722", "1723", "1724", "1725", "1726", "1727", "1728", "1729", "1730", "1731", "1732", "1733", "1734", "1735", "1736", "1737", "1738", "1739", "1740", "1741", "1742", "1743", "1744", "1745", "1746", "1747", "1748", "1749", "1750", "1751", "1752", "1753", "1754", "1755", "1756", "1757", "1758", "1759", "1760", "1761", "1762", "1763"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\CheckpointAddPopup.tsx", ["1764", "1765", "1766", "1767", "1768", "1769", "1770", "1771", "1772", "1773", "1774", "1775", "1776", "1777", "1778", "1779", "1780", "1781", "1782", "1783", "1784", "1785", "1786", "1787", "1788", "1789", "1790", "1791", "1792", "1793", "1794", "1795", "1796", "1797", "1798", "1799", "1800", "1801", "1802", "1803", "1804", "1805", "1806", "1807", "1808", "1809", "1810", "1811", "1812", "1813", "1814", "1815", "1816", "1817", "1818", "1819", "1820", "1821", "1822", "1823", "1824", "1825"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageProperties.tsx", ["1826", "1827", "1828", "1829", "1830", "1831", "1832", "1833", "1834", "1835", "1836", "1837", "1838", "1839"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageGalleryPopup.tsx", ["1840", "1841"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\Buttons.tsx", ["1842", "1843", "1844", "1845", "1846", "1847", "1848", "1849", "1850", "1851", "1852"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\ImageSection.tsx", ["1853", "1854", "1855", "1856", "1857", "1858", "1859", "1860", "1861", "1862", "1863", "1864", "1865"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\RTE\\RTESection.tsx", ["1866", "1867", "1868", "1869", "1870", "1871", "1872", "1873", "1874", "1875", "1876", "1877", "1878", "1879", "1880", "1881", "1882", "1883", "1884", "1885", "1886", "1887", "1888", "1889", "1890", "1891", "1892", "1893", "1894", "1895", "1896", "1897"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\ButtonSetting.tsx", ["1898", "1899", "1900", "1901", "1902", "1903", "1904", "1905", "1906", "1907", "1908", "1909", "1910"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\UserRoleService.ts", ["1911"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\multilinguial\\i18n.ts", ["1912", "1913", "1914"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\contexts\\TranslationContext.tsx", ["1915", "1916"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\LanguageSelector.tsx", ["1917"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\multilinguial\\LanguageService.ts", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\AgentTraining.tsx", ["1918", "1919", "1920", "1921", "1922", "1923", "1924", "1925", "1926", "1927", "1928", "1929", "1930", "1931", "1932", "1933", "1934", "1935", "1936", "1937"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\AgentAdditionalContextPopup.tsx", [], [], {"ruleId": "1938", "severity": 1, "message": "1939", "line": 3, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 3, "endColumn": 18}, {"ruleId": "1938", "severity": 1, "message": "1942", "line": 9, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 9, "endColumn": 11}, {"ruleId": "1938", "severity": 1, "message": "1943", "line": 16, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 16, "endColumn": 19}, {"ruleId": "1938", "severity": 1, "message": "1944", "line": 1, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 1, "endColumn": 23}, {"ruleId": "1938", "severity": 1, "message": "1945", "line": 1, "column": 58, "nodeType": "1940", "messageId": "1941", "endLine": 1, "endColumn": 65}, {"ruleId": "1938", "severity": 1, "message": "1946", "line": 5, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 5, "endColumn": 15}, {"ruleId": "1938", "severity": 1, "message": "1947", "line": 6, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 6, "endColumn": 17}, {"ruleId": "1938", "severity": 1, "message": "1948", "line": 7, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 7, "endColumn": 28}, {"ruleId": "1938", "severity": 1, "message": "1949", "line": 14, "column": 28, "nodeType": "1940", "messageId": "1941", "endLine": 14, "endColumn": 40}, {"ruleId": "1938", "severity": 1, "message": "1950", "line": 19, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 19, "endColumn": 9}, {"ruleId": "1938", "severity": 1, "message": "1951", "line": 24, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 24, "endColumn": 10}, {"ruleId": "1938", "severity": 1, "message": "1952", "line": 25, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 25, "endColumn": 18}, {"ruleId": "1938", "severity": 1, "message": "1953", "line": 26, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 26, "endColumn": 14}, {"ruleId": "1938", "severity": 1, "message": "1954", "line": 27, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 27, "endColumn": 18}, {"ruleId": "1938", "severity": 1, "message": "1955", "line": 28, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 28, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "1956", "line": 29, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 29, "endColumn": 15}, {"ruleId": "1938", "severity": 1, "message": "1957", "line": 30, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 30, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "1958", "line": 31, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 31, "endColumn": 12}, {"ruleId": "1938", "severity": 1, "message": "1959", "line": 32, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 32, "endColumn": 19}, {"ruleId": "1938", "severity": 1, "message": "1960", "line": 33, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 33, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "1961", "line": 34, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 34, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "1962", "line": 35, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 35, "endColumn": 15}, {"ruleId": "1938", "severity": 1, "message": "1963", "line": 36, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 36, "endColumn": 14}, {"ruleId": "1938", "severity": 1, "message": "1964", "line": 37, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 37, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "1965", "line": 39, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 39, "endColumn": 14}, {"ruleId": "1938", "severity": 1, "message": "1966", "line": 40, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 40, "endColumn": 10}, {"ruleId": "1938", "severity": 1, "message": "1967", "line": 41, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 41, "endColumn": 10}, {"ruleId": "1938", "severity": 1, "message": "1968", "line": 42, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 42, "endColumn": 11}, {"ruleId": "1938", "severity": 1, "message": "1969", "line": 46, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 46, "endColumn": 10}, {"ruleId": "1938", "severity": 1, "message": "1970", "line": 52, "column": 20, "nodeType": "1940", "messageId": "1941", "endLine": 52, "endColumn": 26}, {"ruleId": "1938", "severity": 1, "message": "1971", "line": 62, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 62, "endColumn": 16}, {"ruleId": "1938", "severity": 1, "message": "1972", "line": 63, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 63, "endColumn": 16}, {"ruleId": "1938", "severity": 1, "message": "1973", "line": 70, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 70, "endColumn": 6}, {"ruleId": "1938", "severity": 1, "message": "1974", "line": 71, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 71, "endColumn": 6}, {"ruleId": "1938", "severity": 1, "message": "1975", "line": 78, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 78, "endColumn": 20}, {"ruleId": "1938", "severity": 1, "message": "1976", "line": 80, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 80, "endColumn": 17}, {"ruleId": "1938", "severity": 1, "message": "1977", "line": 81, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 81, "endColumn": 28}, {"ruleId": "1938", "severity": 1, "message": "1978", "line": 81, "column": 30, "nodeType": "1940", "messageId": "1941", "endLine": 81, "endColumn": 39}, {"ruleId": "1938", "severity": 1, "message": "1979", "line": 84, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 84, "endColumn": 21}, {"ruleId": "1938", "severity": 1, "message": "1980", "line": 85, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 85, "endColumn": 18}, {"ruleId": "1938", "severity": 1, "message": "1981", "line": 86, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 86, "endColumn": 22}, {"ruleId": "1938", "severity": 1, "message": "1982", "line": 90, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 90, "endColumn": 16}, {"ruleId": "1938", "severity": 1, "message": "1983", "line": 92, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 92, "endColumn": 23}, {"ruleId": "1938", "severity": 1, "message": "1984", "line": 98, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 98, "endColumn": 18}, {"ruleId": "1938", "severity": 1, "message": "1985", "line": 105, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 105, "endColumn": 24}, {"ruleId": "1938", "severity": 1, "message": "1986", "line": 108, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 108, "endColumn": 24}, {"ruleId": "1938", "severity": 1, "message": "1987", "line": 109, "column": 26, "nodeType": "1940", "messageId": "1941", "endLine": 109, "endColumn": 37}, {"ruleId": "1938", "severity": 1, "message": "1988", "line": 114, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 114, "endColumn": 19}, {"ruleId": "1938", "severity": 1, "message": "1989", "line": 114, "column": 21, "nodeType": "1940", "messageId": "1941", "endLine": 114, "endColumn": 28}, {"ruleId": "1938", "severity": 1, "message": "1990", "line": 117, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 117, "endColumn": 17}, {"ruleId": "1938", "severity": 1, "message": "1991", "line": 124, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 124, "endColumn": 22}, {"ruleId": "1938", "severity": 1, "message": "1992", "line": 138, "column": 11, "nodeType": "1940", "messageId": "1941", "endLine": 138, "endColumn": 22}, {"ruleId": "1938", "severity": 1, "message": "1993", "line": 201, "column": 11, "nodeType": "1940", "messageId": "1941", "endLine": 201, "endColumn": 17}, {"ruleId": "1938", "severity": 1, "message": "1994", "line": 218, "column": 11, "nodeType": "1940", "messageId": "1941", "endLine": 218, "endColumn": 17}, {"ruleId": "1938", "severity": 1, "message": "1995", "line": 226, "column": 11, "nodeType": "1940", "messageId": "1941", "endLine": 226, "endColumn": 19}, {"ruleId": "1938", "severity": 1, "message": "1996", "line": 382, "column": 11, "nodeType": "1940", "messageId": "1941", "endLine": 382, "endColumn": 18}, {"ruleId": "1938", "severity": 1, "message": "1997", "line": 417, "column": 5, "nodeType": "1940", "messageId": "1941", "endLine": 417, "endColumn": 16}, {"ruleId": "1938", "severity": 1, "message": "1998", "line": 419, "column": 6, "nodeType": "1940", "messageId": "1941", "endLine": 419, "endColumn": 20}, {"ruleId": "1938", "severity": 1, "message": "1999", "line": 422, "column": 6, "nodeType": "1940", "messageId": "1941", "endLine": 422, "endColumn": 27}, {"ruleId": "1938", "severity": 1, "message": "2000", "line": 438, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 438, "endColumn": 22}, {"ruleId": "1938", "severity": 1, "message": "2001", "line": 439, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 439, "endColumn": 20}, {"ruleId": "1938", "severity": 1, "message": "2002", "line": 441, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 441, "endColumn": 28}, {"ruleId": "1938", "severity": 1, "message": "2003", "line": 444, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 444, "endColumn": 23}, {"ruleId": "1938", "severity": 1, "message": "2004", "line": 448, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 448, "endColumn": 20}, {"ruleId": "1938", "severity": 1, "message": "2005", "line": 449, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 449, "endColumn": 23}, {"ruleId": "1938", "severity": 1, "message": "2006", "line": 460, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 460, "endColumn": 24}, {"ruleId": "1938", "severity": 1, "message": "2007", "line": 461, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 461, "endColumn": 22}, {"ruleId": "1938", "severity": 1, "message": "2008", "line": 462, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 462, "endColumn": 17}, {"ruleId": "1938", "severity": 1, "message": "2009", "line": 464, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 464, "endColumn": 24}, {"ruleId": "1938", "severity": 1, "message": "2010", "line": 464, "column": 26, "nodeType": "1940", "messageId": "1941", "endLine": 464, "endColumn": 44}, {"ruleId": "1938", "severity": 1, "message": "2011", "line": 469, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 469, "endColumn": 21}, {"ruleId": "1938", "severity": 1, "message": "2012", "line": 469, "column": 23, "nodeType": "1940", "messageId": "1941", "endLine": 469, "endColumn": 38}, {"ruleId": "1938", "severity": 1, "message": "2013", "line": 471, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 471, "endColumn": 17}, {"ruleId": "1938", "severity": 1, "message": "2014", "line": 471, "column": 19, "nodeType": "1940", "messageId": "1941", "endLine": 471, "endColumn": 30}, {"ruleId": "1938", "severity": 1, "message": "2015", "line": 474, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 474, "endColumn": 22}, {"ruleId": "1938", "severity": 1, "message": "2016", "line": 474, "column": 24, "nodeType": "1940", "messageId": "1941", "endLine": 474, "endColumn": 40}, {"ruleId": "1938", "severity": 1, "message": "2017", "line": 475, "column": 19, "nodeType": "1940", "messageId": "1941", "endLine": 475, "endColumn": 30}, {"ruleId": "1938", "severity": 1, "message": "2018", "line": 480, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 480, "endColumn": 27}, {"ruleId": "1938", "severity": 1, "message": "2019", "line": 480, "column": 29, "nodeType": "1940", "messageId": "1941", "endLine": 480, "endColumn": 50}, {"ruleId": "1938", "severity": 1, "message": "2020", "line": 487, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 487, "endColumn": 14}, {"ruleId": "1938", "severity": 1, "message": "2021", "line": 487, "column": 16, "nodeType": "1940", "messageId": "1941", "endLine": 487, "endColumn": 24}, {"ruleId": "1938", "severity": 1, "message": "2022", "line": 489, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 489, "endColumn": 25}, {"ruleId": "1938", "severity": 1, "message": "2023", "line": 489, "column": 27, "nodeType": "1940", "messageId": "1941", "endLine": 489, "endColumn": 41}, {"ruleId": "1938", "severity": 1, "message": "2024", "line": 491, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 491, "endColumn": 14}, {"ruleId": "1938", "severity": 1, "message": "2025", "line": 491, "column": 16, "nodeType": "1940", "messageId": "1941", "endLine": 491, "endColumn": 24}, {"ruleId": "1938", "severity": 1, "message": "2026", "line": 505, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 505, "endColumn": 27}, {"ruleId": "1938", "severity": 1, "message": "2027", "line": 506, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 506, "endColumn": 31}, {"ruleId": "1938", "severity": 1, "message": "2028", "line": 506, "column": 33, "nodeType": "1940", "messageId": "1941", "endLine": 506, "endColumn": 58}, {"ruleId": "1938", "severity": 1, "message": "2029", "line": 509, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 509, "endColumn": 17}, {"ruleId": "1938", "severity": 1, "message": "2030", "line": 509, "column": 19, "nodeType": "1940", "messageId": "1941", "endLine": 509, "endColumn": 30}, {"ruleId": "1938", "severity": 1, "message": "2031", "line": 510, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 510, "endColumn": 15}, {"ruleId": "1938", "severity": 1, "message": "2032", "line": 510, "column": 17, "nodeType": "1940", "messageId": "1941", "endLine": 510, "endColumn": 26}, {"ruleId": "1938", "severity": 1, "message": "2033", "line": 511, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 511, "endColumn": 19}, {"ruleId": "1938", "severity": 1, "message": "2034", "line": 511, "column": 21, "nodeType": "1940", "messageId": "1941", "endLine": 511, "endColumn": 34}, {"ruleId": "1938", "severity": 1, "message": "2035", "line": 520, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 520, "endColumn": 25}, {"ruleId": "1938", "severity": 1, "message": "2036", "line": 521, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 521, "endColumn": 18}, {"ruleId": "1938", "severity": 1, "message": "2037", "line": 527, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 527, "endColumn": 26}, {"ruleId": "1938", "severity": 1, "message": "2038", "line": 531, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 531, "endColumn": 24}, {"ruleId": "1938", "severity": 1, "message": "2039", "line": 531, "column": 26, "nodeType": "1940", "messageId": "1941", "endLine": 531, "endColumn": 44}, {"ruleId": "1938", "severity": 1, "message": "2040", "line": 534, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 534, "endColumn": 18}, {"ruleId": "1938", "severity": 1, "message": "2041", "line": 534, "column": 20, "nodeType": "1940", "messageId": "1941", "endLine": 534, "endColumn": 32}, {"ruleId": "2042", "severity": 1, "message": "2043", "line": 574, "column": 5, "nodeType": "2044", "endLine": 574, "endColumn": 27, "suggestions": "2045"}, {"ruleId": "1938", "severity": 1, "message": "2046", "line": 584, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 584, "endColumn": 6}, {"ruleId": "1938", "severity": 1, "message": "2047", "line": 585, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 585, "endColumn": 7}, {"ruleId": "1938", "severity": 1, "message": "2048", "line": 586, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 586, "endColumn": 18}, {"ruleId": "1938", "severity": 1, "message": "2049", "line": 588, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 588, "endColumn": 16}, {"ruleId": "1938", "severity": 1, "message": "2050", "line": 589, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 589, "endColumn": 19}, {"ruleId": "1938", "severity": 1, "message": "2051", "line": 594, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 594, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "2052", "line": 595, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 595, "endColumn": 16}, {"ruleId": "1938", "severity": 1, "message": "2053", "line": 630, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 630, "endColumn": 15}, {"ruleId": "1938", "severity": 1, "message": "2054", "line": 631, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 631, "endColumn": 18}, {"ruleId": "1938", "severity": 1, "message": "2055", "line": 632, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 632, "endColumn": 14}, {"ruleId": "1938", "severity": 1, "message": "2056", "line": 640, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 640, "endColumn": 10}, {"ruleId": "1938", "severity": 1, "message": "2057", "line": 642, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 642, "endColumn": 17}, {"ruleId": "1938", "severity": 1, "message": "2058", "line": 643, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 643, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "2059", "line": 644, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 644, "endColumn": 15}, {"ruleId": "1938", "severity": 1, "message": "2060", "line": 645, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 645, "endColumn": 17}, {"ruleId": "1938", "severity": 1, "message": "2061", "line": 650, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 650, "endColumn": 11}, {"ruleId": "1938", "severity": 1, "message": "2062", "line": 652, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 652, "endColumn": 17}, {"ruleId": "1938", "severity": 1, "message": "2063", "line": 654, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 654, "endColumn": 24}, {"ruleId": "1938", "severity": 1, "message": "2064", "line": 664, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 664, "endColumn": 15}, {"ruleId": "1938", "severity": 1, "message": "2065", "line": 665, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 665, "endColumn": 18}, {"ruleId": "1938", "severity": 1, "message": "2066", "line": 668, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 668, "endColumn": 23}, {"ruleId": "1938", "severity": 1, "message": "2067", "line": 672, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 672, "endColumn": 14}, {"ruleId": "1938", "severity": 1, "message": "2068", "line": 674, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 674, "endColumn": 28}, {"ruleId": "1938", "severity": 1, "message": "2069", "line": 675, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 675, "endColumn": 14}, {"ruleId": "1938", "severity": 1, "message": "2070", "line": 677, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 677, "endColumn": 11}, {"ruleId": "1938", "severity": 1, "message": "2071", "line": 684, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 684, "endColumn": 18}, {"ruleId": "1938", "severity": 1, "message": "2072", "line": 685, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 685, "endColumn": 12}, {"ruleId": "1938", "severity": 1, "message": "2073", "line": 690, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 690, "endColumn": 20}, {"ruleId": "1938", "severity": 1, "message": "2074", "line": 691, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 691, "endColumn": 23}, {"ruleId": "1938", "severity": 1, "message": "2075", "line": 692, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 692, "endColumn": 16}, {"ruleId": "1938", "severity": 1, "message": "2076", "line": 702, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 702, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "2077", "line": 706, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 706, "endColumn": 22}, {"ruleId": "1938", "severity": 1, "message": "2078", "line": 710, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 710, "endColumn": 15}, {"ruleId": "1938", "severity": 1, "message": "2079", "line": 712, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 712, "endColumn": 22}, {"ruleId": "1938", "severity": 1, "message": "2080", "line": 714, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 714, "endColumn": 25}, {"ruleId": "1938", "severity": 1, "message": "2081", "line": 715, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 715, "endColumn": 14}, {"ruleId": "1938", "severity": 1, "message": "2082", "line": 720, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 720, "endColumn": 12}, {"ruleId": "1938", "severity": 1, "message": "2083", "line": 721, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 721, "endColumn": 15}, {"ruleId": "1938", "severity": 1, "message": "2084", "line": 739, "column": 18, "nodeType": "1940", "messageId": "1941", "endLine": 739, "endColumn": 37}, {"ruleId": "1938", "severity": 1, "message": "2085", "line": 740, "column": 18, "nodeType": "1940", "messageId": "1941", "endLine": 740, "endColumn": 37}, {"ruleId": "1938", "severity": 1, "message": "2086", "line": 744, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 744, "endColumn": 21}, {"ruleId": "1938", "severity": 1, "message": "2087", "line": 756, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 756, "endColumn": 35}, {"ruleId": "1938", "severity": 1, "message": "2088", "line": 789, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 789, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "2089", "line": 800, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 800, "endColumn": 22}, {"ruleId": "1938", "severity": 1, "message": "2090", "line": 805, "column": 25, "nodeType": "1940", "messageId": "1941", "endLine": 805, "endColumn": 42}, {"ruleId": "2091", "severity": 1, "message": "2092", "line": 810, "column": 22, "nodeType": "2093", "messageId": "2094", "endLine": 810, "endColumn": 24}, {"ruleId": "2042", "severity": 1, "message": "2095", "line": 896, "column": 5, "nodeType": "2044", "endLine": 896, "endColumn": 46, "suggestions": "2096"}, {"ruleId": "2042", "severity": 1, "message": "2097", "line": 896, "column": 6, "nodeType": "2098", "endLine": 896, "endColumn": 29}, {"ruleId": "2042", "severity": 1, "message": "2099", "line": 914, "column": 5, "nodeType": "2044", "endLine": 914, "endColumn": 18, "suggestions": "2100"}, {"ruleId": "1938", "severity": 1, "message": "2101", "line": 916, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 916, "endColumn": 20}, {"ruleId": "1938", "severity": 1, "message": "2102", "line": 917, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 917, "endColumn": 20}, {"ruleId": "1938", "severity": 1, "message": "2103", "line": 938, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 938, "endColumn": 24}, {"ruleId": "2042", "severity": 1, "message": "2104", "line": 999, "column": 5, "nodeType": "2044", "endLine": 1007, "endColumn": 3, "suggestions": "2105"}, {"ruleId": "2042", "severity": 1, "message": "2106", "line": 1035, "column": 5, "nodeType": "2044", "endLine": 1058, "endColumn": 3, "suggestions": "2107"}, {"ruleId": "2042", "severity": 1, "message": "2108", "line": 1070, "column": 5, "nodeType": "2044", "endLine": 1070, "endColumn": 19, "suggestions": "2109"}, {"ruleId": "2042", "severity": 1, "message": "2110", "line": 1181, "column": 5, "nodeType": "2044", "endLine": 1181, "endColumn": 39, "suggestions": "2111"}, {"ruleId": "1938", "severity": 1, "message": "2112", "line": 1300, "column": 16, "nodeType": "1940", "messageId": "1941", "endLine": 1300, "endColumn": 24}, {"ruleId": "2091", "severity": 1, "message": "2113", "line": 1392, "column": 25, "nodeType": "2093", "messageId": "2094", "endLine": 1392, "endColumn": 27}, {"ruleId": "2091", "severity": 1, "message": "2113", "line": 1399, "column": 25, "nodeType": "2093", "messageId": "2094", "endLine": 1399, "endColumn": 27}, {"ruleId": "2091", "severity": 1, "message": "2113", "line": 1399, "column": 53, "nodeType": "2093", "messageId": "2094", "endLine": 1399, "endColumn": 55}, {"ruleId": "2091", "severity": 1, "message": "2113", "line": 1402, "column": 26, "nodeType": "2093", "messageId": "2094", "endLine": 1402, "endColumn": 28}, {"ruleId": "2091", "severity": 1, "message": "2113", "line": 1402, "column": 58, "nodeType": "2093", "messageId": "2094", "endLine": 1402, "endColumn": 60}, {"ruleId": "1938", "severity": 1, "message": "2114", "line": 1533, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 1533, "endColumn": 33}, {"ruleId": "2091", "severity": 1, "message": "2113", "line": 1610, "column": 19, "nodeType": "2093", "messageId": "2094", "endLine": 1610, "endColumn": 21}, {"ruleId": "1938", "severity": 1, "message": "2115", "line": 1757, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 1757, "endColumn": 30}, {"ruleId": "2091", "severity": 1, "message": "2113", "line": 2019, "column": 19, "nodeType": "2093", "messageId": "2094", "endLine": 2019, "endColumn": 21}, {"ruleId": "2091", "severity": 1, "message": "2113", "line": 2191, "column": 25, "nodeType": "2093", "messageId": "2094", "endLine": 2191, "endColumn": 27}, {"ruleId": "2042", "severity": 1, "message": "2116", "line": 2230, "column": 5, "nodeType": "2044", "endLine": 2230, "endColumn": 69, "suggestions": "2117"}, {"ruleId": "1938", "severity": 1, "message": "2118", "line": 2287, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 2287, "endColumn": 36}, {"ruleId": "1938", "severity": 1, "message": "2119", "line": 2298, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 2298, "endColumn": 27}, {"ruleId": "1938", "severity": 1, "message": "2120", "line": 2298, "column": 29, "nodeType": "1940", "messageId": "1941", "endLine": 2298, "endColumn": 48}, {"ruleId": "1938", "severity": 1, "message": "2121", "line": 2690, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 2690, "endColumn": 27}, {"ruleId": "2042", "severity": 1, "message": "2122", "line": 2725, "column": 5, "nodeType": "2044", "endLine": 2725, "endColumn": 38, "suggestions": "2123"}, {"ruleId": "1938", "severity": 1, "message": "2124", "line": 2742, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 2742, "endColumn": 28}, {"ruleId": "1938", "severity": 1, "message": "2125", "line": 2776, "column": 6, "nodeType": "1940", "messageId": "1941", "endLine": 2776, "endColumn": 18}, {"ruleId": "2042", "severity": 1, "message": "2126", "line": 3176, "column": 4, "nodeType": "2044", "endLine": 3176, "endColumn": 18, "suggestions": "2127"}, {"ruleId": "1938", "severity": 1, "message": "2128", "line": 3520, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 3520, "endColumn": 33}, {"ruleId": "2042", "severity": 1, "message": "2129", "line": 3594, "column": 16, "nodeType": "2098", "endLine": 3594, "endColumn": 37}, {"ruleId": "2091", "severity": 1, "message": "2113", "line": 3595, "column": 56, "nodeType": "2093", "messageId": "2094", "endLine": 3595, "endColumn": 58}, {"ruleId": "2091", "severity": 1, "message": "2113", "line": 3599, "column": 49, "nodeType": "2093", "messageId": "2094", "endLine": 3599, "endColumn": 51}, {"ruleId": "2091", "severity": 1, "message": "2113", "line": 3603, "column": 50, "nodeType": "2093", "messageId": "2094", "endLine": 3603, "endColumn": 52}, {"ruleId": "2091", "severity": 1, "message": "2113", "line": 3609, "column": 51, "nodeType": "2093", "messageId": "2094", "endLine": 3609, "endColumn": 53}, {"ruleId": "2091", "severity": 1, "message": "2113", "line": 3616, "column": 51, "nodeType": "2093", "messageId": "2094", "endLine": 3616, "endColumn": 53}, {"ruleId": "1938", "severity": 1, "message": "2130", "line": 3839, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 3839, "endColumn": 23}, {"ruleId": "2091", "severity": 1, "message": "2092", "line": 3847, "column": 30, "nodeType": "2093", "messageId": "2094", "endLine": 3847, "endColumn": 32}, {"ruleId": "2091", "severity": 1, "message": "2113", "line": 3860, "column": 39, "nodeType": "2093", "messageId": "2094", "endLine": 3860, "endColumn": 41}, {"ruleId": "2042", "severity": 1, "message": "2131", "line": 3874, "column": 5, "nodeType": "2044", "endLine": 3874, "endColumn": 33, "suggestions": "2132"}, {"ruleId": "1938", "severity": 1, "message": "2133", "line": 3878, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 3878, "endColumn": 28}, {"ruleId": "1938", "severity": 1, "message": "2134", "line": 3878, "column": 30, "nodeType": "1940", "messageId": "1941", "endLine": 3878, "endColumn": 52}, {"ruleId": "2091", "severity": 1, "message": "2113", "line": 3976, "column": 55, "nodeType": "2093", "messageId": "2094", "endLine": 3976, "endColumn": 57}, {"ruleId": "1938", "severity": 1, "message": "2135", "line": 3995, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 3995, "endColumn": 23}, {"ruleId": "1938", "severity": 1, "message": "2136", "line": 3997, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 3997, "endColumn": 16}, {"ruleId": "1938", "severity": 1, "message": "2137", "line": 4001, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 4001, "endColumn": 25}, {"ruleId": "1938", "severity": 1, "message": "2138", "line": 4020, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 4020, "endColumn": 26}, {"ruleId": "2091", "severity": 1, "message": "2092", "line": 4054, "column": 66, "nodeType": "2093", "messageId": "2094", "endLine": 4054, "endColumn": 68}, {"ruleId": "2042", "severity": 1, "message": "2139", "line": 4061, "column": 5, "nodeType": "2044", "endLine": 4068, "endColumn": 3, "suggestions": "2140"}, {"ruleId": "2091", "severity": 1, "message": "2113", "line": 4302, "column": 17, "nodeType": "2093", "messageId": "2094", "endLine": 4302, "endColumn": 19}, {"ruleId": "2091", "severity": 1, "message": "2092", "line": 4557, "column": 21, "nodeType": "2093", "messageId": "2094", "endLine": 4557, "endColumn": 23}, {"ruleId": "2091", "severity": 1, "message": "2092", "line": 4565, "column": 21, "nodeType": "2093", "messageId": "2094", "endLine": 4565, "endColumn": 23}, {"ruleId": "2091", "severity": 1, "message": "2113", "line": 4578, "column": 15, "nodeType": "2093", "messageId": "2094", "endLine": 4578, "endColumn": 17}, {"ruleId": "1938", "severity": 1, "message": "2141", "line": 4875, "column": 11, "nodeType": "1940", "messageId": "1941", "endLine": 4875, "endColumn": 23}, {"ruleId": "1938", "severity": 1, "message": "2142", "line": 4886, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 4886, "endColumn": 21}, {"ruleId": "1938", "severity": 1, "message": "2143", "line": 4887, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 4887, "endColumn": 20}, {"ruleId": "2042", "severity": 1, "message": "2144", "line": 4893, "column": 5, "nodeType": "2044", "endLine": 4893, "endColumn": 62, "suggestions": "2145"}, {"ruleId": "2042", "severity": 1, "message": "2097", "line": 4893, "column": 6, "nodeType": "2146", "endLine": 4893, "endColumn": 48}, {"ruleId": "2091", "severity": 1, "message": "2113", "line": 4916, "column": 25, "nodeType": "2093", "messageId": "2094", "endLine": 4916, "endColumn": 27}, {"ruleId": "1938", "severity": 1, "message": "2147", "line": 4920, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 4920, "endColumn": 22}, {"ruleId": "1938", "severity": 1, "message": "2148", "line": 4943, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 4943, "endColumn": 23}, {"ruleId": "2091", "severity": 1, "message": "2092", "line": 5018, "column": 25, "nodeType": "2093", "messageId": "2094", "endLine": 5018, "endColumn": 27}, {"ruleId": "2042", "severity": 1, "message": "2149", "line": 5051, "column": 5, "nodeType": "2044", "endLine": 5051, "endColumn": 22, "suggestions": "2150"}, {"ruleId": "1938", "severity": 1, "message": "2151", "line": 5053, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 5053, "endColumn": 18}, {"ruleId": "2091", "severity": 1, "message": "2092", "line": 5055, "column": 40, "nodeType": "2093", "messageId": "2094", "endLine": 5055, "endColumn": 42}, {"ruleId": "2091", "severity": 1, "message": "2092", "line": 5120, "column": 69, "nodeType": "2093", "messageId": "2094", "endLine": 5120, "endColumn": 71}, {"ruleId": "1938", "severity": 1, "message": "2152", "line": 5170, "column": 12, "nodeType": "1940", "messageId": "1941", "endLine": 5170, "endColumn": 21}, {"ruleId": "1938", "severity": 1, "message": "2153", "line": 5171, "column": 12, "nodeType": "1940", "messageId": "1941", "endLine": 5171, "endColumn": 22}, {"ruleId": "2042", "severity": 1, "message": "2154", "line": 5201, "column": 5, "nodeType": "2044", "endLine": 5201, "endColumn": 38, "suggestions": "2155"}, {"ruleId": "2091", "severity": 1, "message": "2092", "line": 5204, "column": 40, "nodeType": "2093", "messageId": "2094", "endLine": 5204, "endColumn": 42}, {"ruleId": "1938", "severity": 1, "message": "2152", "line": 5210, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 5210, "endColumn": 19}, {"ruleId": "1938", "severity": 1, "message": "2153", "line": 5211, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 5211, "endColumn": 20}, {"ruleId": "2042", "severity": 1, "message": "2156", "line": 5217, "column": 5, "nodeType": "2044", "endLine": 5217, "endColumn": 106, "suggestions": "2157"}, {"ruleId": "2042", "severity": 1, "message": "2158", "line": 5366, "column": 5, "nodeType": "2044", "endLine": 5366, "endColumn": 17, "suggestions": "2159"}, {"ruleId": "2042", "severity": 1, "message": "2160", "line": 5382, "column": 5, "nodeType": "2044", "endLine": 5382, "endColumn": 78, "suggestions": "2161"}, {"ruleId": "1938", "severity": 1, "message": "2162", "line": 5385, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 5385, "endColumn": 29}, {"ruleId": "2042", "severity": 1, "message": "2163", "line": 5398, "column": 8, "nodeType": "2044", "endLine": 5398, "endColumn": 15, "suggestions": "2164"}, {"ruleId": "2165", "severity": 1, "message": "2166", "line": 6057, "column": 80, "nodeType": "2167", "messageId": "2168", "endLine": 6057, "endColumn": 81, "suggestions": "2169"}, {"ruleId": "1938", "severity": 1, "message": "2170", "line": 6262, "column": 25, "nodeType": "1940", "messageId": "1941", "endLine": 6262, "endColumn": 42}, {"ruleId": "1938", "severity": 1, "message": "2171", "line": 2, "column": 16, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 27}, {"ruleId": "1938", "severity": 1, "message": "2172", "line": 7, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 7, "endColumn": 21}, {"ruleId": "1938", "severity": 1, "message": "2173", "line": 7, "column": 23, "nodeType": "1940", "messageId": "1941", "endLine": 7, "endColumn": 34}, {"ruleId": "1938", "severity": 1, "message": "2174", "line": 98, "column": 13, "nodeType": "1940", "messageId": "1941", "endLine": 98, "endColumn": 25}, {"ruleId": "2042", "severity": 1, "message": "2175", "line": 103, "column": 6, "nodeType": "2044", "endLine": 103, "endColumn": 8, "suggestions": "2176"}, {"ruleId": "1938", "severity": 1, "message": "2177", "line": 148, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 148, "endColumn": 15}, {"ruleId": "1938", "severity": 1, "message": "2178", "line": 3, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 3, "endColumn": 31}, {"ruleId": "1938", "severity": 1, "message": "2179", "line": 4, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 4, "endColumn": 32}, {"ruleId": "1938", "severity": 1, "message": "2180", "line": 3, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 3, "endColumn": 16}, {"ruleId": "1938", "severity": 1, "message": "2181", "line": 8, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 8, "endColumn": 16}, {"ruleId": "1938", "severity": 1, "message": "2182", "line": 9, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 9, "endColumn": 20}, {"ruleId": "1938", "severity": 1, "message": "2183", "line": 13, "column": 24, "nodeType": "1940", "messageId": "1941", "endLine": 13, "endColumn": 46}, {"ruleId": "2184", "severity": 1, "message": "2185", "line": 2486, "column": 5, "nodeType": "2186", "messageId": "2094", "endLine": 2486, "endColumn": 17}, {"ruleId": "2184", "severity": 1, "message": "2187", "line": 2487, "column": 5, "nodeType": "2186", "messageId": "2094", "endLine": 2487, "endColumn": 20}, {"ruleId": "2184", "severity": 1, "message": "2188", "line": 2818, "column": 5, "nodeType": "2186", "messageId": "2094", "endLine": 2818, "endColumn": 24}, {"ruleId": "2189", "severity": 1, "message": "2190", "line": 2824, "column": 31, "nodeType": "2098", "messageId": "2191", "endLine": 2824, "endColumn": 51}, {"ruleId": "2184", "severity": 1, "message": "2192", "line": 2995, "column": 5, "nodeType": "2186", "messageId": "2094", "endLine": 2995, "endColumn": 25}, {"ruleId": "1938", "severity": 1, "message": "2193", "line": 3699, "column": 16, "nodeType": "1940", "messageId": "1941", "endLine": 3699, "endColumn": 28}, {"ruleId": "1938", "severity": 1, "message": "2193", "line": 3898, "column": 16, "nodeType": "1940", "messageId": "1941", "endLine": 3898, "endColumn": 28}, {"ruleId": "2184", "severity": 1, "message": "2194", "line": 5385, "column": 5, "nodeType": "2186", "messageId": "2094", "endLine": 5385, "endColumn": 30}, {"ruleId": "1938", "severity": 1, "message": "2195", "line": 5457, "column": 14, "nodeType": "1940", "messageId": "1941", "endLine": 5457, "endColumn": 17}, {"ruleId": "1938", "severity": 1, "message": "2196", "line": 6548, "column": 13, "nodeType": "1940", "messageId": "1941", "endLine": 6548, "endColumn": 23}, {"ruleId": "1938", "severity": 1, "message": "2196", "line": 6574, "column": 13, "nodeType": "1940", "messageId": "1941", "endLine": 6574, "endColumn": 23}, {"ruleId": "1938", "severity": 1, "message": "2196", "line": 6580, "column": 13, "nodeType": "1940", "messageId": "1941", "endLine": 6580, "endColumn": 23}, {"ruleId": "1938", "severity": 1, "message": "2196", "line": 6595, "column": 13, "nodeType": "1940", "messageId": "1941", "endLine": 6595, "endColumn": 23}, {"ruleId": "1938", "severity": 1, "message": "2195", "line": 7372, "column": 13, "nodeType": "1940", "messageId": "1941", "endLine": 7372, "endColumn": 16}, {"ruleId": "1938", "severity": 1, "message": "2195", "line": 7616, "column": 13, "nodeType": "1940", "messageId": "1941", "endLine": 7616, "endColumn": 16}, {"ruleId": "1938", "severity": 1, "message": "2195", "line": 7787, "column": 13, "nodeType": "1940", "messageId": "1941", "endLine": 7787, "endColumn": 16}, {"ruleId": "2091", "severity": 1, "message": "2092", "line": 8452, "column": 66, "nodeType": "2093", "messageId": "2094", "endLine": 8452, "endColumn": 68}, {"ruleId": "1938", "severity": 1, "message": "2197", "line": 70, "column": 23, "nodeType": "1940", "messageId": "1941", "endLine": 70, "endColumn": 29}, {"ruleId": "1938", "severity": 1, "message": "2198", "line": 1, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 1, "endColumn": 15}, {"ruleId": "1938", "severity": 1, "message": "2199", "line": 2, "column": 44, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 56}, {"ruleId": "1938", "severity": 1, "message": "2200", "line": 18, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 18, "endColumn": 12}, {"ruleId": "1938", "severity": 1, "message": "2201", "line": 19, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 19, "endColumn": 7}, {"ruleId": "1938", "severity": 1, "message": "2202", "line": 20, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 20, "endColumn": 18}, {"ruleId": "1938", "severity": 1, "message": "2203", "line": 21, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 21, "endColumn": 7}, {"ruleId": "1938", "severity": 1, "message": "2204", "line": 24, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 24, "endColumn": 14}, {"ruleId": "1938", "severity": 1, "message": "2205", "line": 25, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 25, "endColumn": 18}, {"ruleId": "1938", "severity": 1, "message": "2206", "line": 26, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 26, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "2207", "line": 31, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 31, "endColumn": 15}, {"ruleId": "1938", "severity": 1, "message": "2208", "line": 38, "column": 49, "nodeType": "1940", "messageId": "1941", "endLine": 38, "endColumn": 55}, {"ruleId": "1938", "severity": 1, "message": "2209", "line": 38, "column": 63, "nodeType": "1940", "messageId": "1941", "endLine": 38, "endColumn": 70}, {"ruleId": "1938", "severity": 1, "message": "2210", "line": 46, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 46, "endColumn": 19}, {"ruleId": "1938", "severity": 1, "message": "2211", "line": 48, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 48, "endColumn": 15}, {"ruleId": "1938", "severity": 1, "message": "2212", "line": 92, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 92, "endColumn": 14}, {"ruleId": "1938", "severity": 1, "message": "2213", "line": 93, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 93, "endColumn": 19}, {"ruleId": "1938", "severity": 1, "message": "2214", "line": 99, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 99, "endColumn": 25}, {"ruleId": "1938", "severity": 1, "message": "2215", "line": 100, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 100, "endColumn": 22}, {"ruleId": "1938", "severity": 1, "message": "2216", "line": 104, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 104, "endColumn": 10}, {"ruleId": "1938", "severity": 1, "message": "2217", "line": 108, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 108, "endColumn": 19}, {"ruleId": "1938", "severity": 1, "message": "2218", "line": 112, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 112, "endColumn": 14}, {"ruleId": "1938", "severity": 1, "message": "2219", "line": 113, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 113, "endColumn": 19}, {"ruleId": "1938", "severity": 1, "message": "2220", "line": 114, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 114, "endColumn": 28}, {"ruleId": "1938", "severity": 1, "message": "2221", "line": 115, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 115, "endColumn": 33}, {"ruleId": "1938", "severity": 1, "message": "2222", "line": 116, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 116, "endColumn": 23}, {"ruleId": "1938", "severity": 1, "message": "2223", "line": 119, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 119, "endColumn": 19}, {"ruleId": "1938", "severity": 1, "message": "2224", "line": 120, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 120, "endColumn": 15}, {"ruleId": "1938", "severity": 1, "message": "2225", "line": 162, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 162, "endColumn": 15}, {"ruleId": "1938", "severity": 1, "message": "2226", "line": 172, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 172, "endColumn": 14}, {"ruleId": "1938", "severity": 1, "message": "2227", "line": 180, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 180, "endColumn": 11}, {"ruleId": "1938", "severity": 1, "message": "2228", "line": 181, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 181, "endColumn": 14}, {"ruleId": "1938", "severity": 1, "message": "2229", "line": 182, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 182, "endColumn": 14}, {"ruleId": "1938", "severity": 1, "message": "2230", "line": 183, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 183, "endColumn": 17}, {"ruleId": "1938", "severity": 1, "message": "2231", "line": 184, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 184, "endColumn": 10}, {"ruleId": "1938", "severity": 1, "message": "2232", "line": 205, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 205, "endColumn": 30}, {"ruleId": "1938", "severity": 1, "message": "2233", "line": 209, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 209, "endColumn": 29}, {"ruleId": "1938", "severity": 1, "message": "2234", "line": 456, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 456, "endColumn": 26}, {"ruleId": "2042", "severity": 1, "message": "2235", "line": 548, "column": 5, "nodeType": "2044", "endLine": 548, "endColumn": 60, "suggestions": "2236"}, {"ruleId": "1938", "severity": 1, "message": "2237", "line": 562, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 562, "endColumn": 22}, {"ruleId": "2042", "severity": 1, "message": "2238", "line": 582, "column": 5, "nodeType": "2044", "endLine": 582, "endColumn": 60, "suggestions": "2239"}, {"ruleId": "2042", "severity": 1, "message": "2240", "line": 599, "column": 4, "nodeType": "2044", "endLine": 599, "endColumn": 6, "suggestions": "2241"}, {"ruleId": "1938", "severity": 1, "message": "2242", "line": 2, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "2243", "line": 1, "column": 17, "nodeType": "1940", "messageId": "1941", "endLine": 1, "endColumn": 27}, {"ruleId": "1938", "severity": 1, "message": "2244", "line": 2, "column": 29, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 39}, {"ruleId": "1938", "severity": 1, "message": "1979", "line": 3, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 3, "endColumn": 21}, {"ruleId": "1938", "severity": 1, "message": "1980", "line": 4, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 4, "endColumn": 18}, {"ruleId": "1938", "severity": 1, "message": "1977", "line": 5, "column": 26, "nodeType": "1940", "messageId": "1941", "endLine": 5, "endColumn": 44}, {"ruleId": "1938", "severity": 1, "message": "1978", "line": 5, "column": 46, "nodeType": "1940", "messageId": "1941", "endLine": 5, "endColumn": 55}, {"ruleId": "1938", "severity": 1, "message": "2245", "line": 6, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 6, "endColumn": 22}, {"ruleId": "1938", "severity": 1, "message": "2246", "line": 6, "column": 24, "nodeType": "1940", "messageId": "1941", "endLine": 6, "endColumn": 31}, {"ruleId": "1938", "severity": 1, "message": "1989", "line": 11, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 11, "endColumn": 17}, {"ruleId": "1938", "severity": 1, "message": "2247", "line": 17, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 17, "endColumn": 24}, {"ruleId": "1938", "severity": 1, "message": "2248", "line": 21, "column": 19, "nodeType": "1940", "messageId": "1941", "endLine": 21, "endColumn": 35}, {"ruleId": "1938", "severity": 1, "message": "2249", "line": 24, "column": 5, "nodeType": "1940", "messageId": "1941", "endLine": 24, "endColumn": 42}, {"ruleId": "1938", "severity": 1, "message": "2250", "line": 25, "column": 5, "nodeType": "1940", "messageId": "1941", "endLine": 25, "endColumn": 27}, {"ruleId": "1938", "severity": 1, "message": "2251", "line": 26, "column": 5, "nodeType": "1940", "messageId": "1941", "endLine": 26, "endColumn": 22}, {"ruleId": "1938", "severity": 1, "message": "2022", "line": 35, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 35, "endColumn": 26}, {"ruleId": "1938", "severity": 1, "message": "2023", "line": 35, "column": 28, "nodeType": "1940", "messageId": "1941", "endLine": 35, "endColumn": 42}, {"ruleId": "1938", "severity": 1, "message": "1954", "line": 6, "column": 5, "nodeType": "1940", "messageId": "1941", "endLine": 6, "endColumn": 21}, {"ruleId": "1938", "severity": 1, "message": "2252", "line": 9, "column": 5, "nodeType": "1940", "messageId": "1941", "endLine": 9, "endColumn": 7}, {"ruleId": "1938", "severity": 1, "message": "2253", "line": 12, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 12, "endColumn": 22}, {"ruleId": "1938", "severity": 1, "message": "2254", "line": 25, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 25, "endColumn": 38}, {"ruleId": "1938", "severity": 1, "message": "2255", "line": 51, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 51, "endColumn": 22}, {"ruleId": "1938", "severity": 1, "message": "2256", "line": 52, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 52, "endColumn": 26}, {"ruleId": "1938", "severity": 1, "message": "2061", "line": 53, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 53, "endColumn": 11}, {"ruleId": "1938", "severity": 1, "message": "2257", "line": 54, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 54, "endColumn": 8}, {"ruleId": "1938", "severity": 1, "message": "2258", "line": 55, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 55, "endColumn": 18}, {"ruleId": "1938", "severity": 1, "message": "2078", "line": 56, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 56, "endColumn": 15}, {"ruleId": "1938", "severity": 1, "message": "2259", "line": 57, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 57, "endColumn": 15}, {"ruleId": "1938", "severity": 1, "message": "2260", "line": 58, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 58, "endColumn": 20}, {"ruleId": "1938", "severity": 1, "message": "2079", "line": 59, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 59, "endColumn": 22}, {"ruleId": "1938", "severity": 1, "message": "2261", "line": 60, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 60, "endColumn": 17}, {"ruleId": "1938", "severity": 1, "message": "2262", "line": 61, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 61, "endColumn": 21}, {"ruleId": "1938", "severity": 1, "message": "2263", "line": 62, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 62, "endColumn": 22}, {"ruleId": "1938", "severity": 1, "message": "2264", "line": 63, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 63, "endColumn": 25}, {"ruleId": "1938", "severity": 1, "message": "2265", "line": 64, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 64, "endColumn": 29}, {"ruleId": "1938", "severity": 1, "message": "2219", "line": 65, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 65, "endColumn": 19}, {"ruleId": "1938", "severity": 1, "message": "2222", "line": 66, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 66, "endColumn": 23}, {"ruleId": "2042", "severity": 1, "message": "2266", "line": 83, "column": 5, "nodeType": "2044", "endLine": 83, "endColumn": 7, "suggestions": "2267"}, {"ruleId": "2042", "severity": 1, "message": "2268", "line": 101, "column": 5, "nodeType": "2044", "endLine": 101, "endColumn": 28, "suggestions": "2269"}, {"ruleId": "2042", "severity": 1, "message": "2270", "line": 112, "column": 5, "nodeType": "2044", "endLine": 112, "endColumn": 48, "suggestions": "2271"}, {"ruleId": "1938", "severity": 1, "message": "2272", "line": 191, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 191, "endColumn": 22}, {"ruleId": "1938", "severity": 1, "message": "2273", "line": 3, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 3, "endColumn": 22}, {"ruleId": "1938", "severity": 1, "message": "1987", "line": 3, "column": 26, "nodeType": "1940", "messageId": "1941", "endLine": 3, "endColumn": 37}, {"ruleId": "1938", "severity": 1, "message": "2274", "line": 5, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 5, "endColumn": 19}, {"ruleId": "1938", "severity": 1, "message": "2275", "line": 457, "column": 7, "nodeType": "1940", "messageId": "1941", "endLine": 457, "endColumn": 29}, {"ruleId": "1938", "severity": 1, "message": "2276", "line": 530, "column": 7, "nodeType": "1940", "messageId": "1941", "endLine": 530, "endColumn": 24}, {"ruleId": "2165", "severity": 1, "message": "2166", "line": 682, "column": 41, "nodeType": "2167", "messageId": "2168", "endLine": 682, "endColumn": 42, "suggestions": "2277"}, {"ruleId": "2165", "severity": 1, "message": "2278", "line": 682, "column": 45, "nodeType": "2167", "messageId": "2168", "endLine": 682, "endColumn": 46, "suggestions": "2279"}, {"ruleId": "2165", "severity": 1, "message": "2166", "line": 682, "column": 56, "nodeType": "2167", "messageId": "2168", "endLine": 682, "endColumn": 57, "suggestions": "2280"}, {"ruleId": "2165", "severity": 1, "message": "2278", "line": 682, "column": 60, "nodeType": "2167", "messageId": "2168", "endLine": 682, "endColumn": 61, "suggestions": "2281"}, {"ruleId": "2165", "severity": 1, "message": "2166", "line": 682, "column": 89, "nodeType": "2167", "messageId": "2168", "endLine": 682, "endColumn": 90, "suggestions": "2282"}, {"ruleId": "2165", "severity": 1, "message": "2278", "line": 682, "column": 93, "nodeType": "2167", "messageId": "2168", "endLine": 682, "endColumn": 94, "suggestions": "2283"}, {"ruleId": "2165", "severity": 1, "message": "2166", "line": 682, "column": 104, "nodeType": "2167", "messageId": "2168", "endLine": 682, "endColumn": 105, "suggestions": "2284"}, {"ruleId": "2165", "severity": 1, "message": "2278", "line": 682, "column": 108, "nodeType": "2167", "messageId": "2168", "endLine": 682, "endColumn": 109, "suggestions": "2285"}, {"ruleId": "1938", "severity": 1, "message": "2286", "line": 1316, "column": 11, "nodeType": "1940", "messageId": "1941", "endLine": 1316, "endColumn": 19}, {"ruleId": "1938", "severity": 1, "message": "2287", "line": 1321, "column": 11, "nodeType": "1940", "messageId": "1941", "endLine": 1321, "endColumn": 23}, {"ruleId": "1938", "severity": 1, "message": "2288", "line": 1, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 1, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "2289", "line": 3, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 3, "endColumn": 21}, {"ruleId": "1938", "severity": 1, "message": "2273", "line": 4, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 4, "endColumn": 22}, {"ruleId": "1938", "severity": 1, "message": "1945", "line": 2, "column": 28, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 35}, {"ruleId": "1938", "severity": 1, "message": "2290", "line": 4, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 4, "endColumn": 22}, {"ruleId": "1938", "severity": 1, "message": "2291", "line": 8, "column": 33, "nodeType": "1940", "messageId": "1941", "endLine": 8, "endColumn": 42}, {"ruleId": "1938", "severity": 1, "message": "2292", "line": 8, "column": 44, "nodeType": "1940", "messageId": "1941", "endLine": 8, "endColumn": 59}, {"ruleId": "1938", "severity": 1, "message": "2293", "line": 10, "column": 34, "nodeType": "1940", "messageId": "1941", "endLine": 10, "endColumn": 57}, {"ruleId": "1938", "severity": 1, "message": "2294", "line": 10, "column": 59, "nodeType": "1940", "messageId": "1941", "endLine": 10, "endColumn": 79}, {"ruleId": "1938", "severity": 1, "message": "2295", "line": 59, "column": 16, "nodeType": "1940", "messageId": "1941", "endLine": 59, "endColumn": 24}, {"ruleId": "1938", "severity": 1, "message": "2296", "line": 124, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 124, "endColumn": 22}, {"ruleId": "1938", "severity": 1, "message": "1945", "line": 1, "column": 28, "nodeType": "1940", "messageId": "1941", "endLine": 1, "endColumn": 35}, {"ruleId": "1938", "severity": 1, "message": "2297", "line": 8, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 8, "endColumn": 33}, {"ruleId": "1938", "severity": 1, "message": "2291", "line": 9, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 9, "endColumn": 19}, {"ruleId": "1938", "severity": 1, "message": "2298", "line": 80, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 80, "endColumn": 58}, {"ruleId": "2042", "severity": 1, "message": "2299", "line": 86, "column": 8, "nodeType": "2300", "endLine": 90, "endColumn": 12}, {"ruleId": "2042", "severity": 1, "message": "2301", "line": 86, "column": 8, "nodeType": "2300", "endLine": 90, "endColumn": 12}, {"ruleId": "1938", "severity": 1, "message": "2302", "line": 92, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 92, "endColumn": 23}, {"ruleId": "1938", "severity": 1, "message": "2303", "line": 92, "column": 25, "nodeType": "1940", "messageId": "1941", "endLine": 92, "endColumn": 42}, {"ruleId": "2304", "severity": 1, "message": "2305", "line": 113, "column": 113, "nodeType": "2306", "messageId": "2307", "endLine": 113, "endColumn": 397}, {"ruleId": "2042", "severity": 1, "message": "2308", "line": 154, "column": 5, "nodeType": "2044", "endLine": 154, "endColumn": 38, "suggestions": "2309"}, {"ruleId": "2042", "severity": 1, "message": "2097", "line": 154, "column": 6, "nodeType": "2093", "endLine": 154, "endColumn": 37}, {"ruleId": "2091", "severity": 1, "message": "2113", "line": 154, "column": 33, "nodeType": "2093", "messageId": "2094", "endLine": 154, "endColumn": 35}, {"ruleId": "1938", "severity": 1, "message": "2295", "line": 156, "column": 16, "nodeType": "1940", "messageId": "1941", "endLine": 156, "endColumn": 24}, {"ruleId": "2091", "severity": 1, "message": "2113", "line": 174, "column": 56, "nodeType": "2093", "messageId": "2094", "endLine": 174, "endColumn": 58}, {"ruleId": "1938", "severity": 1, "message": "2310", "line": 181, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 181, "endColumn": 17}, {"ruleId": "1938", "severity": 1, "message": "2311", "line": 182, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 182, "endColumn": 23}, {"ruleId": "1938", "severity": 1, "message": "2312", "line": 305, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 305, "endColumn": 22}, {"ruleId": "1938", "severity": 1, "message": "2295", "line": 771, "column": 16, "nodeType": "1940", "messageId": "1941", "endLine": 771, "endColumn": 24}, {"ruleId": "1938", "severity": 1, "message": "2313", "line": 806, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 806, "endColumn": 17}, {"ruleId": "1938", "severity": 1, "message": "2314", "line": 806, "column": 19, "nodeType": "1940", "messageId": "1941", "endLine": 806, "endColumn": 30}, {"ruleId": "1938", "severity": 1, "message": "2315", "line": 807, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 807, "endColumn": 20}, {"ruleId": "1938", "severity": 1, "message": "2316", "line": 807, "column": 22, "nodeType": "1940", "messageId": "1941", "endLine": 807, "endColumn": 36}, {"ruleId": "1938", "severity": 1, "message": "2119", "line": 808, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 808, "endColumn": 27}, {"ruleId": "1938", "severity": 1, "message": "2120", "line": 808, "column": 29, "nodeType": "1940", "messageId": "1941", "endLine": 808, "endColumn": 48}, {"ruleId": "1938", "severity": 1, "message": "2317", "line": 809, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 809, "endColumn": 25}, {"ruleId": "1938", "severity": 1, "message": "2318", "line": 809, "column": 27, "nodeType": "1940", "messageId": "1941", "endLine": 809, "endColumn": 46}, {"ruleId": "1938", "severity": 1, "message": "2036", "line": 810, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 810, "endColumn": 18}, {"ruleId": "1938", "severity": 1, "message": "2319", "line": 3, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 3, "endColumn": 16}, {"ruleId": "1938", "severity": 1, "message": "2320", "line": 4, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 4, "endColumn": 16}, {"ruleId": "1938", "severity": 1, "message": "2321", "line": 31, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 31, "endColumn": 21}, {"ruleId": "1938", "severity": 1, "message": "2322", "line": 32, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 32, "endColumn": 21}, {"ruleId": "1938", "severity": 1, "message": "2243", "line": 1, "column": 17, "nodeType": "1940", "messageId": "1941", "endLine": 1, "endColumn": 27}, {"ruleId": "1938", "severity": 1, "message": "1949", "line": 2, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 22}, {"ruleId": "1938", "severity": 1, "message": "2247", "line": 4, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 4, "endColumn": 24}, {"ruleId": "1938", "severity": 1, "message": "1971", "line": 9, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 9, "endColumn": 16}, {"ruleId": "1938", "severity": 1, "message": "2323", "line": 11, "column": 5, "nodeType": "1940", "messageId": "1941", "endLine": 11, "endColumn": 9}, {"ruleId": "1938", "severity": 1, "message": "2242", "line": 12, "column": 5, "nodeType": "1940", "messageId": "1941", "endLine": 12, "endColumn": 8}, {"ruleId": "1938", "severity": 1, "message": "2324", "line": 14, "column": 5, "nodeType": "1940", "messageId": "1941", "endLine": 14, "endColumn": 14}, {"ruleId": "1938", "severity": 1, "message": "2206", "line": 16, "column": 5, "nodeType": "1940", "messageId": "1941", "endLine": 16, "endColumn": 16}, {"ruleId": "1938", "severity": 1, "message": "2325", "line": 18, "column": 5, "nodeType": "1940", "messageId": "1941", "endLine": 18, "endColumn": 16}, {"ruleId": "1938", "severity": 1, "message": "2326", "line": 19, "column": 5, "nodeType": "1940", "messageId": "1941", "endLine": 19, "endColumn": 15}, {"ruleId": "1938", "severity": 1, "message": "2327", "line": 20, "column": 5, "nodeType": "1940", "messageId": "1941", "endLine": 20, "endColumn": 11}, {"ruleId": "1938", "severity": 1, "message": "2328", "line": 21, "column": 5, "nodeType": "1940", "messageId": "1941", "endLine": 21, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "2329", "line": 22, "column": 5, "nodeType": "1940", "messageId": "1941", "endLine": 22, "endColumn": 15}, {"ruleId": "1938", "severity": 1, "message": "2330", "line": 23, "column": 5, "nodeType": "1940", "messageId": "1941", "endLine": 23, "endColumn": 12}, {"ruleId": "1938", "severity": 1, "message": "2331", "line": 24, "column": 5, "nodeType": "1940", "messageId": "1941", "endLine": 24, "endColumn": 10}, {"ruleId": "1938", "severity": 1, "message": "2332", "line": 25, "column": 5, "nodeType": "1940", "messageId": "1941", "endLine": 25, "endColumn": 9}, {"ruleId": "1938", "severity": 1, "message": "1974", "line": 3, "column": 65, "nodeType": "1940", "messageId": "1941", "endLine": 3, "endColumn": 69}, {"ruleId": "1938", "severity": 1, "message": "2333", "line": 6, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 6, "endColumn": 22}, {"ruleId": "1938", "severity": 1, "message": "2334", "line": 7, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 7, "endColumn": 16}, {"ruleId": "1938", "severity": 1, "message": "2335", "line": 20, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 20, "endColumn": 23}, {"ruleId": "1938", "severity": 1, "message": "2314", "line": 84, "column": 19, "nodeType": "1940", "messageId": "1941", "endLine": 84, "endColumn": 30}, {"ruleId": "1938", "severity": 1, "message": "2336", "line": 85, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 85, "endColumn": 24}, {"ruleId": "1938", "severity": 1, "message": "2337", "line": 85, "column": 26, "nodeType": "1940", "messageId": "1941", "endLine": 85, "endColumn": 44}, {"ruleId": "1938", "severity": 1, "message": "2338", "line": 86, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 86, "endColumn": 24}, {"ruleId": "1938", "severity": 1, "message": "2339", "line": 86, "column": 26, "nodeType": "1940", "messageId": "1941", "endLine": 86, "endColumn": 44}, {"ruleId": "1938", "severity": 1, "message": "2340", "line": 90, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 90, "endColumn": 27}, {"ruleId": "1938", "severity": 1, "message": "2341", "line": 90, "column": 45, "nodeType": "1940", "messageId": "1941", "endLine": 90, "endColumn": 62}, {"ruleId": "1938", "severity": 1, "message": "2342", "line": 93, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 93, "endColumn": 10}, {"ruleId": "1938", "severity": 1, "message": "2343", "line": 94, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 94, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "2029", "line": 95, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 95, "endColumn": 11}, {"ruleId": "1938", "severity": 1, "message": "2030", "line": 96, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 96, "endColumn": 14}, {"ruleId": "1938", "severity": 1, "message": "2031", "line": 97, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 97, "endColumn": 9}, {"ruleId": "1938", "severity": 1, "message": "2032", "line": 98, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 98, "endColumn": 12}, {"ruleId": "1938", "severity": 1, "message": "2033", "line": 99, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 99, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "2034", "line": 100, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 100, "endColumn": 16}, {"ruleId": "1938", "severity": 1, "message": "2344", "line": 101, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 101, "endColumn": 17}, {"ruleId": "1938", "severity": 1, "message": "2345", "line": 102, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 102, "endColumn": 14}, {"ruleId": "1938", "severity": 1, "message": "2346", "line": 103, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 103, "endColumn": 21}, {"ruleId": "1938", "severity": 1, "message": "2048", "line": 104, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 104, "endColumn": 18}, {"ruleId": "1938", "severity": 1, "message": "2347", "line": 105, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 105, "endColumn": 17}, {"ruleId": "1938", "severity": 1, "message": "2348", "line": 107, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 107, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "2349", "line": 108, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 108, "endColumn": 25}, {"ruleId": "1938", "severity": 1, "message": "2350", "line": 115, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 115, "endColumn": 28}, {"ruleId": "1938", "severity": 1, "message": "2351", "line": 116, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 116, "endColumn": 24}, {"ruleId": "1938", "severity": 1, "message": "2352", "line": 118, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 118, "endColumn": 23}, {"ruleId": "1938", "severity": 1, "message": "2353", "line": 119, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 119, "endColumn": 23}, {"ruleId": "1938", "severity": 1, "message": "2354", "line": 120, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 120, "endColumn": 17}, {"ruleId": "1938", "severity": 1, "message": "2355", "line": 121, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 121, "endColumn": 18}, {"ruleId": "1938", "severity": 1, "message": "2356", "line": 122, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 122, "endColumn": 21}, {"ruleId": "1938", "severity": 1, "message": "2357", "line": 123, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 123, "endColumn": 18}, {"ruleId": "1938", "severity": 1, "message": "2358", "line": 132, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 132, "endColumn": 21}, {"ruleId": "1938", "severity": 1, "message": "2359", "line": 137, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 137, "endColumn": 20}, {"ruleId": "1938", "severity": 1, "message": "2213", "line": 139, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 139, "endColumn": 19}, {"ruleId": "1938", "severity": 1, "message": "2220", "line": 140, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 140, "endColumn": 28}, {"ruleId": "1938", "severity": 1, "message": "2360", "line": 141, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 141, "endColumn": 19}, {"ruleId": "1938", "severity": 1, "message": "2361", "line": 144, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 144, "endColumn": 11}, {"ruleId": "1938", "severity": 1, "message": "2218", "line": 145, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 145, "endColumn": 14}, {"ruleId": "1938", "severity": 1, "message": "2362", "line": 146, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 146, "endColumn": 20}, {"ruleId": "2042", "severity": 1, "message": "2363", "line": 170, "column": 5, "nodeType": "2044", "endLine": 170, "endColumn": 45, "suggestions": "2364"}, {"ruleId": "1938", "severity": 1, "message": "2365", "line": 221, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 221, "endColumn": 29}, {"ruleId": "2091", "severity": 1, "message": "2092", "line": 241, "column": 24, "nodeType": "2093", "messageId": "2094", "endLine": 241, "endColumn": 26}, {"ruleId": "2042", "severity": 1, "message": "2366", "line": 315, "column": 7, "nodeType": "2044", "endLine": 315, "endColumn": 42, "suggestions": "2367"}, {"ruleId": "1938", "severity": 1, "message": "2368", "line": 339, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 339, "endColumn": 24}, {"ruleId": "1938", "severity": 1, "message": "2369", "line": 340, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 340, "endColumn": 24}, {"ruleId": "1938", "severity": 1, "message": "2370", "line": 488, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 488, "endColumn": 23}, {"ruleId": "1938", "severity": 1, "message": "2371", "line": 491, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 491, "endColumn": 23}, {"ruleId": "1938", "severity": 1, "message": "2372", "line": 500, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 500, "endColumn": 31}, {"ruleId": "2091", "severity": 1, "message": "2092", "line": 811, "column": 26, "nodeType": "2093", "messageId": "2094", "endLine": 811, "endColumn": 28}, {"ruleId": "2091", "severity": 1, "message": "2092", "line": 847, "column": 26, "nodeType": "2093", "messageId": "2094", "endLine": 847, "endColumn": 28}, {"ruleId": "1938", "severity": 1, "message": "2373", "line": 1031, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 1031, "endColumn": 25}, {"ruleId": "1938", "severity": 1, "message": "2374", "line": 1035, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 1035, "endColumn": 25}, {"ruleId": "1938", "severity": 1, "message": "2375", "line": 1039, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 1039, "endColumn": 28}, {"ruleId": "1938", "severity": 1, "message": "2376", "line": 1043, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 1043, "endColumn": 32}, {"ruleId": "1938", "severity": 1, "message": "2377", "line": 1047, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 1047, "endColumn": 27}, {"ruleId": "1938", "severity": 1, "message": "2378", "line": 1051, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 1051, "endColumn": 25}, {"ruleId": "1938", "severity": 1, "message": "2379", "line": 1055, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 1055, "endColumn": 31}, {"ruleId": "1938", "severity": 1, "message": "2380", "line": 1059, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 1059, "endColumn": 28}, {"ruleId": "1938", "severity": 1, "message": "2381", "line": 5, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 5, "endColumn": 20}, {"ruleId": "1938", "severity": 1, "message": "2382", "line": 13, "column": 26, "nodeType": "1940", "messageId": "1941", "endLine": 13, "endColumn": 47}, {"ruleId": "1938", "severity": 1, "message": "2383", "line": 15, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 15, "endColumn": 19}, {"ruleId": "1938", "severity": 1, "message": "2384", "line": 79, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 79, "endColumn": 18}, {"ruleId": "1938", "severity": 1, "message": "2385", "line": 81, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 81, "endColumn": 20}, {"ruleId": "1938", "severity": 1, "message": "2386", "line": 82, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 82, "endColumn": 14}, {"ruleId": "1938", "severity": 1, "message": "2387", "line": 83, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 83, "endColumn": 15}, {"ruleId": "1938", "severity": 1, "message": "2388", "line": 84, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 84, "endColumn": 14}, {"ruleId": "1938", "severity": 1, "message": "2056", "line": 88, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 88, "endColumn": 10}, {"ruleId": "1938", "severity": 1, "message": "2389", "line": 89, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 89, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "2390", "line": 91, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 91, "endColumn": 20}, {"ruleId": "1938", "severity": 1, "message": "2058", "line": 92, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 92, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "2059", "line": 93, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 93, "endColumn": 15}, {"ruleId": "1938", "severity": 1, "message": "2057", "line": 94, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 94, "endColumn": 17}, {"ruleId": "1938", "severity": 1, "message": "2223", "line": 104, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 104, "endColumn": 19}, {"ruleId": "2042", "severity": 1, "message": "2391", "line": 209, "column": 7, "nodeType": "2044", "endLine": 209, "endColumn": 9, "suggestions": "2392"}, {"ruleId": "2042", "severity": 1, "message": "2393", "line": 244, "column": 7, "nodeType": "2044", "endLine": 244, "endColumn": 29, "suggestions": "2394"}, {"ruleId": "2042", "severity": 1, "message": "2395", "line": 249, "column": 7, "nodeType": "2044", "endLine": 249, "endColumn": 18, "suggestions": "2396"}, {"ruleId": "2042", "severity": 1, "message": "2397", "line": 292, "column": 7, "nodeType": "2044", "endLine": 292, "endColumn": 72, "suggestions": "2398"}, {"ruleId": "1938", "severity": 1, "message": "2347", "line": 331, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 331, "endColumn": 22}, {"ruleId": "1938", "severity": 1, "message": "2399", "line": 334, "column": 26, "nodeType": "1940", "messageId": "1941", "endLine": 334, "endColumn": 44}, {"ruleId": "1938", "severity": 1, "message": "2400", "line": 463, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 463, "endColumn": 22}, {"ruleId": "1938", "severity": 1, "message": "2401", "line": 4, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 4, "endColumn": 26}, {"ruleId": "1938", "severity": 1, "message": "2402", "line": 6, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 6, "endColumn": 35}, {"ruleId": "1938", "severity": 1, "message": "2403", "line": 7, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 7, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "2404", "line": 7, "column": 17, "nodeType": "1940", "messageId": "1941", "endLine": 7, "endColumn": 27}, {"ruleId": "1938", "severity": 1, "message": "2405", "line": 8, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 8, "endColumn": 17}, {"ruleId": "1938", "severity": 1, "message": "2406", "line": 9, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 9, "endColumn": 22}, {"ruleId": "1938", "severity": 1, "message": "2058", "line": 13, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 13, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "2059", "line": 14, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 14, "endColumn": 15}, {"ruleId": "1938", "severity": 1, "message": "2057", "line": 15, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 15, "endColumn": 17}, {"ruleId": "1938", "severity": 1, "message": "2407", "line": 17, "column": 21, "nodeType": "1940", "messageId": "1941", "endLine": 17, "endColumn": 34}, {"ruleId": "1938", "severity": 1, "message": "2408", "line": 18, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 18, "endColumn": 21}, {"ruleId": "1938", "severity": 1, "message": "2409", "line": 18, "column": 33, "nodeType": "1940", "messageId": "1941", "endLine": 18, "endColumn": 44}, {"ruleId": "1938", "severity": 1, "message": "2410", "line": 19, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 19, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "2411", "line": 96, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 96, "endColumn": 17}, {"ruleId": "1938", "severity": 1, "message": "2412", "line": 97, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 97, "endColumn": 19}, {"ruleId": "1938", "severity": 1, "message": "2413", "line": 100, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 100, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "2414", "line": 101, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 101, "endColumn": 14}, {"ruleId": "1938", "severity": 1, "message": "2415", "line": 109, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 109, "endColumn": 14}, {"ruleId": "1938", "severity": 1, "message": "2416", "line": 129, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 129, "endColumn": 16}, {"ruleId": "2091", "severity": 1, "message": "2113", "line": 176, "column": 45, "nodeType": "2093", "messageId": "2094", "endLine": 176, "endColumn": 47}, {"ruleId": "2091", "severity": 1, "message": "2113", "line": 176, "column": 104, "nodeType": "2093", "messageId": "2094", "endLine": 176, "endColumn": 106}, {"ruleId": "1938", "severity": 1, "message": "2207", "line": 2, "column": 60, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 73}, {"ruleId": "1938", "severity": 1, "message": "2401", "line": 3, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 3, "endColumn": 26}, {"ruleId": "1938", "severity": 1, "message": "2417", "line": 8, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 8, "endColumn": 21}, {"ruleId": "1938", "severity": 1, "message": "2418", "line": 9, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 9, "endColumn": 25}, {"ruleId": "1938", "severity": 1, "message": "2419", "line": 133, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 133, "endColumn": 25}, {"ruleId": "1938", "severity": 1, "message": "2420", "line": 134, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 134, "endColumn": 22}, {"ruleId": "1938", "severity": 1, "message": "2421", "line": 135, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 135, "endColumn": 28}, {"ruleId": "1938", "severity": 1, "message": "2422", "line": 135, "column": 30, "nodeType": "1940", "messageId": "1941", "endLine": 135, "endColumn": 52}, {"ruleId": "1938", "severity": 1, "message": "2223", "line": 137, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 137, "endColumn": 19}, {"ruleId": "2042", "severity": 1, "message": "2423", "line": 163, "column": 8, "nodeType": "2044", "endLine": 163, "endColumn": 10, "suggestions": "2424"}, {"ruleId": "1938", "severity": 1, "message": "2425", "line": 299, "column": 11, "nodeType": "1940", "messageId": "1941", "endLine": 299, "endColumn": 21}, {"ruleId": "1938", "severity": 1, "message": "2426", "line": 342, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 342, "endColumn": 25}, {"ruleId": "1938", "severity": 1, "message": "2427", "line": 343, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 343, "endColumn": 35}, {"ruleId": "1938", "severity": 1, "message": "2428", "line": 344, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 344, "endColumn": 24}, {"ruleId": "1938", "severity": 1, "message": "2429", "line": 346, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 346, "endColumn": 20}, {"ruleId": "2091", "severity": 1, "message": "2113", "line": 465, "column": 22, "nodeType": "2093", "messageId": "2094", "endLine": 465, "endColumn": 24}, {"ruleId": "2091", "severity": 1, "message": "2113", "line": 465, "column": 53, "nodeType": "2093", "messageId": "2094", "endLine": 465, "endColumn": 55}, {"ruleId": "2091", "severity": 1, "message": "2113", "line": 465, "column": 89, "nodeType": "2093", "messageId": "2094", "endLine": 465, "endColumn": 91}, {"ruleId": "2091", "severity": 1, "message": "2113", "line": 465, "column": 125, "nodeType": "2093", "messageId": "2094", "endLine": 465, "endColumn": 127}, {"ruleId": "2091", "severity": 1, "message": "2113", "line": 467, "column": 29, "nodeType": "2093", "messageId": "2094", "endLine": 467, "endColumn": 31}, {"ruleId": "2091", "severity": 1, "message": "2113", "line": 467, "column": 56, "nodeType": "2093", "messageId": "2094", "endLine": 467, "endColumn": 58}, {"ruleId": "2091", "severity": 1, "message": "2113", "line": 467, "column": 88, "nodeType": "2093", "messageId": "2094", "endLine": 467, "endColumn": 90}, {"ruleId": "2091", "severity": 1, "message": "2113", "line": 467, "column": 120, "nodeType": "2093", "messageId": "2094", "endLine": 467, "endColumn": 122}, {"ruleId": "2091", "severity": 1, "message": "2113", "line": 469, "column": 29, "nodeType": "2093", "messageId": "2094", "endLine": 469, "endColumn": 31}, {"ruleId": "2091", "severity": 1, "message": "2113", "line": 469, "column": 64, "nodeType": "2093", "messageId": "2094", "endLine": 469, "endColumn": 66}, {"ruleId": "1938", "severity": 1, "message": "2430", "line": 111, "column": 11, "nodeType": "1940", "messageId": "1941", "endLine": 111, "endColumn": 28}, {"ruleId": "1938", "severity": 1, "message": "2357", "line": 152, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 152, "endColumn": 18}, {"ruleId": "1938", "severity": 1, "message": "2070", "line": 153, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 153, "endColumn": 11}, {"ruleId": "1938", "severity": 1, "message": "2431", "line": 159, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 159, "endColumn": 23}, {"ruleId": "2432", "severity": 1, "message": "2433", "line": 225, "column": 25, "nodeType": "1940", "messageId": "2434", "endLine": 225, "endColumn": 34, "suggestions": "2435"}, {"ruleId": "2042", "severity": 1, "message": "2436", "line": 231, "column": 5, "nodeType": "2044", "endLine": 231, "endColumn": 12, "suggestions": "2437"}, {"ruleId": "2042", "severity": 1, "message": "2438", "line": 237, "column": 5, "nodeType": "2044", "endLine": 237, "endColumn": 21, "suggestions": "2439"}, {"ruleId": "2042", "severity": 1, "message": "2440", "line": 472, "column": 5, "nodeType": "2044", "endLine": 472, "endColumn": 70, "suggestions": "2441"}, {"ruleId": "2091", "severity": 1, "message": "2113", "line": 547, "column": 19, "nodeType": "2093", "messageId": "2094", "endLine": 547, "endColumn": 21}, {"ruleId": "2091", "severity": 1, "message": "2113", "line": 548, "column": 19, "nodeType": "2093", "messageId": "2094", "endLine": 548, "endColumn": 21}, {"ruleId": "2091", "severity": 1, "message": "2113", "line": 549, "column": 24, "nodeType": "2093", "messageId": "2094", "endLine": 549, "endColumn": 26}, {"ruleId": "2091", "severity": 1, "message": "2113", "line": 550, "column": 24, "nodeType": "2093", "messageId": "2094", "endLine": 550, "endColumn": 26}, {"ruleId": "2091", "severity": 1, "message": "2113", "line": 554, "column": 19, "nodeType": "2093", "messageId": "2094", "endLine": 554, "endColumn": 21}, {"ruleId": "2091", "severity": 1, "message": "2113", "line": 555, "column": 19, "nodeType": "2093", "messageId": "2094", "endLine": 555, "endColumn": 21}, {"ruleId": "2091", "severity": 1, "message": "2113", "line": 556, "column": 24, "nodeType": "2093", "messageId": "2094", "endLine": 556, "endColumn": 26}, {"ruleId": "2091", "severity": 1, "message": "2113", "line": 557, "column": 24, "nodeType": "2093", "messageId": "2094", "endLine": 557, "endColumn": 26}, {"ruleId": "2091", "severity": 1, "message": "2113", "line": 561, "column": 19, "nodeType": "2093", "messageId": "2094", "endLine": 561, "endColumn": 21}, {"ruleId": "2091", "severity": 1, "message": "2113", "line": 562, "column": 24, "nodeType": "2093", "messageId": "2094", "endLine": 562, "endColumn": 26}, {"ruleId": "2042", "severity": 1, "message": "2442", "line": 582, "column": 5, "nodeType": "2044", "endLine": 582, "endColumn": 64, "suggestions": "2443"}, {"ruleId": "2042", "severity": 1, "message": "2097", "line": 582, "column": 6, "nodeType": "2146", "endLine": 582, "endColumn": 34}, {"ruleId": "1938", "severity": 1, "message": "2444", "line": 591, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 591, "endColumn": 21}, {"ruleId": "2042", "severity": 1, "message": "2445", "line": 605, "column": 5, "nodeType": "2044", "endLine": 605, "endColumn": 47, "suggestions": "2446"}, {"ruleId": "1938", "severity": 1, "message": "2444", "line": 614, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 614, "endColumn": 21}, {"ruleId": "2042", "severity": 1, "message": "2445", "line": 627, "column": 5, "nodeType": "2044", "endLine": 627, "endColumn": 47, "suggestions": "2447"}, {"ruleId": "2042", "severity": 1, "message": "2448", "line": 1021, "column": 17, "nodeType": "1940", "endLine": 1021, "endColumn": 32}, {"ruleId": "2091", "severity": 1, "message": "2092", "line": 1227, "column": 43, "nodeType": "2093", "messageId": "2094", "endLine": 1227, "endColumn": 45}, {"ruleId": "2091", "severity": 1, "message": "2092", "line": 1232, "column": 78, "nodeType": "2093", "messageId": "2094", "endLine": 1232, "endColumn": 80}, {"ruleId": "1938", "severity": 1, "message": "2449", "line": 1, "column": 17, "nodeType": "1940", "messageId": "1941", "endLine": 1, "endColumn": 26}, {"ruleId": "1938", "severity": 1, "message": "2450", "line": 2, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 14}, {"ruleId": "1938", "severity": 1, "message": "2451", "line": 2, "column": 16, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 27}, {"ruleId": "1938", "severity": 1, "message": "2180", "line": 3, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 3, "endColumn": 16}, {"ruleId": "1938", "severity": 1, "message": "2452", "line": 11, "column": 62, "nodeType": "1940", "messageId": "1941", "endLine": 11, "endColumn": 67}, {"ruleId": "1938", "severity": 1, "message": "2264", "line": 25, "column": 7, "nodeType": "1940", "messageId": "1941", "endLine": 25, "endColumn": 29}, {"ruleId": "1938", "severity": 1, "message": "2222", "line": 28, "column": 7, "nodeType": "1940", "messageId": "1941", "endLine": 28, "endColumn": 27}, {"ruleId": "1938", "severity": 1, "message": "2453", "line": 31, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 31, "endColumn": 27}, {"ruleId": "1938", "severity": 1, "message": "2454", "line": 144, "column": 11, "nodeType": "1940", "messageId": "1941", "endLine": 144, "endColumn": 21}, {"ruleId": "1938", "severity": 1, "message": "2455", "line": 145, "column": 11, "nodeType": "1940", "messageId": "1941", "endLine": 145, "endColumn": 20}, {"ruleId": "1938", "severity": 1, "message": "2456", "line": 1, "column": 28, "nodeType": "1940", "messageId": "1941", "endLine": 1, "endColumn": 34}, {"ruleId": "1938", "severity": 1, "message": "2242", "line": 5, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 5, "endColumn": 5}, {"ruleId": "1938", "severity": 1, "message": "2457", "line": 6, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 6, "endColumn": 19}, {"ruleId": "1938", "severity": 1, "message": "2458", "line": 10, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 10, "endColumn": 16}, {"ruleId": "1938", "severity": 1, "message": "2459", "line": 12, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 12, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "2244", "line": 13, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 13, "endColumn": 12}, {"ruleId": "1938", "severity": 1, "message": "2460", "line": 17, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 17, "endColumn": 32}, {"ruleId": "1938", "severity": 1, "message": "2297", "line": 19, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 19, "endColumn": 33}, {"ruleId": "1938", "severity": 1, "message": "2461", "line": 34, "column": 11, "nodeType": "1940", "messageId": "1941", "endLine": 34, "endColumn": 31}, {"ruleId": "1938", "severity": 1, "message": "2315", "line": 34, "column": 33, "nodeType": "1940", "messageId": "1941", "endLine": 34, "endColumn": 44}, {"ruleId": "2462", "severity": 1, "message": "2463", "line": 96, "column": 2, "nodeType": "2464", "messageId": "2465", "endLine": 112, "endColumn": 4}, {"ruleId": "1938", "severity": 1, "message": "2466", "line": 133, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 133, "endColumn": 10}, {"ruleId": "1938", "severity": 1, "message": "2467", "line": 136, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 136, "endColumn": 12}, {"ruleId": "1938", "severity": 1, "message": "2345", "line": 137, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 137, "endColumn": 14}, {"ruleId": "1938", "severity": 1, "message": "2048", "line": 138, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 138, "endColumn": 18}, {"ruleId": "1938", "severity": 1, "message": "2468", "line": 140, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 140, "endColumn": 15}, {"ruleId": "1938", "severity": 1, "message": "2342", "line": 141, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 141, "endColumn": 10}, {"ruleId": "1938", "severity": 1, "message": "2469", "line": 142, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 142, "endColumn": 8}, {"ruleId": "1938", "severity": 1, "message": "2470", "line": 145, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 145, "endColumn": 15}, {"ruleId": "1938", "severity": 1, "message": "2471", "line": 146, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 146, "endColumn": 15}, {"ruleId": "1938", "severity": 1, "message": "2472", "line": 147, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 147, "endColumn": 15}, {"ruleId": "1938", "severity": 1, "message": "2473", "line": 148, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 148, "endColumn": 18}, {"ruleId": "1938", "severity": 1, "message": "2474", "line": 149, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 149, "endColumn": 20}, {"ruleId": "1938", "severity": 1, "message": "2475", "line": 150, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 150, "endColumn": 25}, {"ruleId": "1938", "severity": 1, "message": "2352", "line": 151, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 151, "endColumn": 23}, {"ruleId": "1938", "severity": 1, "message": "2351", "line": 152, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 152, "endColumn": 24}, {"ruleId": "1938", "severity": 1, "message": "2350", "line": 153, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 153, "endColumn": 28}, {"ruleId": "1938", "severity": 1, "message": "2476", "line": 156, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 156, "endColumn": 20}, {"ruleId": "1938", "severity": 1, "message": "2477", "line": 159, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 159, "endColumn": 18}, {"ruleId": "1938", "severity": 1, "message": "2478", "line": 160, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 160, "endColumn": 21}, {"ruleId": "1938", "severity": 1, "message": "2479", "line": 161, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 161, "endColumn": 17}, {"ruleId": "1938", "severity": 1, "message": "2257", "line": 162, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 162, "endColumn": 8}, {"ruleId": "1938", "severity": 1, "message": "2217", "line": 163, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 163, "endColumn": 19}, {"ruleId": "1938", "severity": 1, "message": "2480", "line": 166, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 166, "endColumn": 22}, {"ruleId": "1938", "severity": 1, "message": "2481", "line": 167, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 167, "endColumn": 18}, {"ruleId": "1938", "severity": 1, "message": "2431", "line": 169, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 169, "endColumn": 23}, {"ruleId": "1938", "severity": 1, "message": "2482", "line": 174, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 174, "endColumn": 17}, {"ruleId": "1938", "severity": 1, "message": "2070", "line": 175, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 175, "endColumn": 11}, {"ruleId": "1938", "severity": 1, "message": "2483", "line": 177, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 177, "endColumn": 25}, {"ruleId": "1938", "severity": 1, "message": "2484", "line": 186, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 186, "endColumn": 23}, {"ruleId": "1938", "severity": 1, "message": "2485", "line": 186, "column": 25, "nodeType": "1940", "messageId": "1941", "endLine": 186, "endColumn": 42}, {"ruleId": "1938", "severity": 1, "message": "2486", "line": 188, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 188, "endColumn": 24}, {"ruleId": "1938", "severity": 1, "message": "2487", "line": 188, "column": 26, "nodeType": "1940", "messageId": "1941", "endLine": 188, "endColumn": 44}, {"ruleId": "2488", "severity": 1, "message": "2489", "line": 350, "column": 5, "nodeType": "2490", "messageId": "2491", "endLine": 350, "endColumn": 52}, {"ruleId": "1938", "severity": 1, "message": "2492", "line": 505, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 505, "endColumn": 31}, {"ruleId": "1938", "severity": 1, "message": "2493", "line": 575, "column": 7, "nodeType": "1940", "messageId": "1941", "endLine": 575, "endColumn": 21}, {"ruleId": "2042", "severity": 1, "message": "2494", "line": 742, "column": 5, "nodeType": "2044", "endLine": 742, "endColumn": 100, "suggestions": "2495"}, {"ruleId": "2042", "severity": 1, "message": "2496", "line": 760, "column": 5, "nodeType": "2044", "endLine": 760, "endColumn": 83, "suggestions": "2497"}, {"ruleId": "1938", "severity": 1, "message": "2498", "line": 940, "column": 7, "nodeType": "1940", "messageId": "1941", "endLine": 940, "endColumn": 18}, {"ruleId": "1938", "severity": 1, "message": "2499", "line": 947, "column": 7, "nodeType": "1940", "messageId": "1941", "endLine": 947, "endColumn": 18}, {"ruleId": "1938", "severity": 1, "message": "2449", "line": 1, "column": 17, "nodeType": "1940", "messageId": "1941", "endLine": 1, "endColumn": 26}, {"ruleId": "1938", "severity": 1, "message": "2500", "line": 1, "column": 28, "nodeType": "1940", "messageId": "1941", "endLine": 1, "endColumn": 36}, {"ruleId": "1938", "severity": 1, "message": "2501", "line": 4, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 4, "endColumn": 18}, {"ruleId": "1938", "severity": 1, "message": "2255", "line": 14, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 14, "endColumn": 28}, {"ruleId": "1938", "severity": 1, "message": "2261", "line": 15, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 15, "endColumn": 23}, {"ruleId": "1938", "severity": 1, "message": "2256", "line": 16, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 16, "endColumn": 32}, {"ruleId": "1938", "severity": 1, "message": "2222", "line": 17, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 17, "endColumn": 23}, {"ruleId": "1938", "severity": 1, "message": "2265", "line": 20, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 20, "endColumn": 29}, {"ruleId": "1938", "severity": 1, "message": "2502", "line": 26, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 26, "endColumn": 29}, {"ruleId": "1938", "severity": 1, "message": "2500", "line": 1, "column": 17, "nodeType": "1940", "messageId": "1941", "endLine": 1, "endColumn": 25}, {"ruleId": "1938", "severity": 1, "message": "2503", "line": 2, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 17}, {"ruleId": "1938", "severity": 1, "message": "2180", "line": 2, "column": 19, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 25}, {"ruleId": "1938", "severity": 1, "message": "2244", "line": 2, "column": 27, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 37}, {"ruleId": "1938", "severity": 1, "message": "2242", "line": 2, "column": 39, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 42}, {"ruleId": "1938", "severity": 1, "message": "2458", "line": 2, "column": 44, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 58}, {"ruleId": "1938", "severity": 1, "message": "2207", "line": 2, "column": 60, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 73}, {"ruleId": "1938", "severity": 1, "message": "2329", "line": 2, "column": 74, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 84}, {"ruleId": "1938", "severity": 1, "message": "2504", "line": 3, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 3, "endColumn": 17}, {"ruleId": "1938", "severity": 1, "message": "2505", "line": 4, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 4, "endColumn": 23}, {"ruleId": "1938", "severity": 1, "message": "2255", "line": 98, "column": 13, "nodeType": "1940", "messageId": "1941", "endLine": 98, "endColumn": 32}, {"ruleId": "1938", "severity": 1, "message": "2261", "line": 99, "column": 13, "nodeType": "1940", "messageId": "1941", "endLine": 99, "endColumn": 27}, {"ruleId": "1938", "severity": 1, "message": "2256", "line": 100, "column": 11, "nodeType": "1940", "messageId": "1941", "endLine": 100, "endColumn": 34}, {"ruleId": "1938", "severity": 1, "message": "2061", "line": 101, "column": 11, "nodeType": "1940", "messageId": "1941", "endLine": 101, "endColumn": 19}, {"ruleId": "1938", "severity": 1, "message": "2257", "line": 102, "column": 11, "nodeType": "1940", "messageId": "1941", "endLine": 102, "endColumn": 16}, {"ruleId": "1938", "severity": 1, "message": "2258", "line": 103, "column": 11, "nodeType": "1940", "messageId": "1941", "endLine": 103, "endColumn": 26}, {"ruleId": "1938", "severity": 1, "message": "2078", "line": 104, "column": 11, "nodeType": "1940", "messageId": "1941", "endLine": 104, "endColumn": 23}, {"ruleId": "1938", "severity": 1, "message": "2079", "line": 105, "column": 13, "nodeType": "1940", "messageId": "1941", "endLine": 105, "endColumn": 32}, {"ruleId": "1938", "severity": 1, "message": "2316", "line": 110, "column": 13, "nodeType": "1940", "messageId": "1941", "endLine": 110, "endColumn": 27}, {"ruleId": "1938", "severity": 1, "message": "2223", "line": 113, "column": 13, "nodeType": "1940", "messageId": "1941", "endLine": 113, "endColumn": 29}, {"ruleId": "2042", "severity": 1, "message": "2506", "line": 172, "column": 12, "nodeType": "2044", "endLine": 172, "endColumn": 35, "suggestions": "2507"}, {"ruleId": "1938", "severity": 1, "message": "1945", "line": 1, "column": 27, "nodeType": "1940", "messageId": "1941", "endLine": 1, "endColumn": 34}, {"ruleId": "1938", "severity": 1, "message": "2173", "line": 4, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 4, "endColumn": 21}, {"ruleId": "1938", "severity": 1, "message": "2273", "line": 7, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 7, "endColumn": 22}, {"ruleId": "1938", "severity": 1, "message": "1987", "line": 7, "column": 26, "nodeType": "1940", "messageId": "1941", "endLine": 7, "endColumn": 37}, {"ruleId": "1938", "severity": 1, "message": "2508", "line": 43, "column": 21, "nodeType": "1940", "messageId": "1941", "endLine": 43, "endColumn": 34}, {"ruleId": "2042", "severity": 1, "message": "2509", "line": 63, "column": 21, "nodeType": "2510", "endLine": 63, "endColumn": 111}, {"ruleId": "1938", "severity": 1, "message": "2511", "line": 2, "column": 25, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 39}, {"ruleId": "1938", "severity": 1, "message": "2512", "line": 4, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 4, "endColumn": 22}, {"ruleId": "1938", "severity": 1, "message": "2172", "line": 5, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 5, "endColumn": 21}, {"ruleId": "1938", "severity": 1, "message": "2513", "line": 11, "column": 7, "nodeType": "1940", "messageId": "1941", "endLine": 11, "endColumn": 14}, {"ruleId": "1938", "severity": 1, "message": "2514", "line": 1, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 1, "endColumn": 23}, {"ruleId": "1938", "severity": 1, "message": "2515", "line": 2, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 25}, {"ruleId": "1938", "severity": 1, "message": "1976", "line": 1, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 1, "endColumn": 17}, {"ruleId": "1938", "severity": 1, "message": "2515", "line": 2, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 25}, {"ruleId": "1938", "severity": 1, "message": "2516", "line": 2, "column": 27, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 40}, {"ruleId": "1938", "severity": 1, "message": "2244", "line": 2, "column": 23, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 33}, {"ruleId": "1938", "severity": 1, "message": "2517", "line": 3, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 3, "endColumn": 24}, {"ruleId": "1938", "severity": 1, "message": "2517", "line": 3, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 3, "endColumn": 24}, {"ruleId": "1938", "severity": 1, "message": "2242", "line": 2, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "2244", "line": 2, "column": 15, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 25}, {"ruleId": "1938", "severity": 1, "message": "2329", "line": 2, "column": 50, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 60}, {"ruleId": "1938", "severity": 1, "message": "2504", "line": 29, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 29, "endColumn": 17}, {"ruleId": "1938", "severity": 1, "message": "2246", "line": 34, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 34, "endColumn": 17}, {"ruleId": "1938", "severity": 1, "message": "2482", "line": 64, "column": 29, "nodeType": "1940", "messageId": "1941", "endLine": 64, "endColumn": 43}, {"ruleId": "1938", "severity": 1, "message": "2518", "line": 72, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 72, "endColumn": 19}, {"ruleId": "1938", "severity": 1, "message": "2519", "line": 74, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 74, "endColumn": 14}, {"ruleId": "1938", "severity": 1, "message": "2520", "line": 77, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 77, "endColumn": 16}, {"ruleId": "1938", "severity": 1, "message": "2521", "line": 78, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 78, "endColumn": 16}, {"ruleId": "1938", "severity": 1, "message": "2522", "line": 97, "column": 15, "nodeType": "1940", "messageId": "1941", "endLine": 97, "endColumn": 22}, {"ruleId": "2091", "severity": 1, "message": "2113", "line": 106, "column": 34, "nodeType": "2093", "messageId": "2094", "endLine": 106, "endColumn": 36}, {"ruleId": "2091", "severity": 1, "message": "2113", "line": 154, "column": 44, "nodeType": "2093", "messageId": "2094", "endLine": 154, "endColumn": 46}, {"ruleId": "1938", "severity": 1, "message": "2523", "line": 173, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 173, "endColumn": 21}, {"ruleId": "2042", "severity": 1, "message": "2524", "line": 302, "column": 5, "nodeType": "2044", "endLine": 302, "endColumn": 50, "suggestions": "2525"}, {"ruleId": "2042", "severity": 1, "message": "2524", "line": 318, "column": 5, "nodeType": "2044", "endLine": 318, "endColumn": 18, "suggestions": "2526"}, {"ruleId": "2091", "severity": 1, "message": "2092", "line": 479, "column": 48, "nodeType": "2093", "messageId": "2094", "endLine": 479, "endColumn": 50}, {"ruleId": "1938", "severity": 1, "message": "2449", "line": 1, "column": 17, "nodeType": "1940", "messageId": "1941", "endLine": 1, "endColumn": 26}, {"ruleId": "1938", "severity": 1, "message": "2527", "line": 61, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 61, "endColumn": 20}, {"ruleId": "1938", "severity": 1, "message": "2528", "line": 63, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 63, "endColumn": 20}, {"ruleId": "1938", "severity": 1, "message": "2529", "line": 2, "column": 15, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 24}, {"ruleId": "1938", "severity": 1, "message": "2530", "line": 6, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 6, "endColumn": 22}, {"ruleId": "1938", "severity": 1, "message": "2531", "line": 43, "column": 16, "nodeType": "1940", "messageId": "1941", "endLine": 43, "endColumn": 28}, {"ruleId": "2042", "severity": 1, "message": "2532", "line": 107, "column": 12, "nodeType": "2044", "endLine": 107, "endColumn": 14, "suggestions": "2533"}, {"ruleId": "1938", "severity": 1, "message": "2534", "line": 297, "column": 15, "nodeType": "1940", "messageId": "1941", "endLine": 297, "endColumn": 28}, {"ruleId": "2042", "severity": 1, "message": "2535", "line": 382, "column": 12, "nodeType": "2044", "endLine": 382, "endColumn": 49, "suggestions": "2536"}, {"ruleId": "2042", "severity": 1, "message": "2537", "line": 393, "column": 13, "nodeType": "2300", "endLine": 393, "endColumn": 43}, {"ruleId": "1938", "severity": 1, "message": "2529", "line": 2, "column": 44, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 53}, {"ruleId": "1938", "severity": 1, "message": "2538", "line": 4, "column": 46, "nodeType": "1940", "messageId": "1941", "endLine": 4, "endColumn": 65}, {"ruleId": "1938", "severity": 1, "message": "1969", "line": 4, "column": 67, "nodeType": "1940", "messageId": "1941", "endLine": 4, "endColumn": 75}, {"ruleId": "1938", "severity": 1, "message": "2504", "line": 7, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 7, "endColumn": 17}, {"ruleId": "1938", "severity": 1, "message": "2539", "line": 8, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 8, "endColumn": 22}, {"ruleId": "1938", "severity": 1, "message": "2255", "line": 30, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 30, "endColumn": 22}, {"ruleId": "1938", "severity": 1, "message": "2540", "line": 31, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 31, "endColumn": 17}, {"ruleId": "1938", "severity": 1, "message": "2057", "line": 34, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 34, "endColumn": 17}, {"ruleId": "1938", "severity": 1, "message": "2541", "line": 44, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 44, "endColumn": 26}, {"ruleId": "1938", "severity": 1, "message": "2542", "line": 45, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 45, "endColumn": 26}, {"ruleId": "1938", "severity": 1, "message": "2543", "line": 46, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 46, "endColumn": 27}, {"ruleId": "1938", "severity": 1, "message": "2544", "line": 47, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 47, "endColumn": 27}, {"ruleId": "1938", "severity": 1, "message": "2545", "line": 51, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 51, "endColumn": 19}, {"ruleId": "1938", "severity": 1, "message": "2546", "line": 51, "column": 21, "nodeType": "1940", "messageId": "1941", "endLine": 51, "endColumn": 34}, {"ruleId": "1938", "severity": 1, "message": "2547", "line": 52, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 52, "endColumn": 21}, {"ruleId": "1938", "severity": 1, "message": "2548", "line": 53, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 53, "endColumn": 21}, {"ruleId": "1938", "severity": 1, "message": "2549", "line": 56, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 56, "endColumn": 24}, {"ruleId": "1938", "severity": 1, "message": "2550", "line": 57, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 57, "endColumn": 18}, {"ruleId": "1938", "severity": 1, "message": "2551", "line": 57, "column": 20, "nodeType": "1940", "messageId": "1941", "endLine": 57, "endColumn": 32}, {"ruleId": "2042", "severity": 1, "message": "2552", "line": 65, "column": 5, "nodeType": "2044", "endLine": 65, "endColumn": 7, "suggestions": "2553"}, {"ruleId": "1938", "severity": 1, "message": "2554", "line": 93, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 93, "endColumn": 32}, {"ruleId": "1938", "severity": 1, "message": "2555", "line": 97, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 97, "endColumn": 32}, {"ruleId": "1938", "severity": 1, "message": "2556", "line": 124, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 124, "endColumn": 18}, {"ruleId": "1938", "severity": 1, "message": "2557", "line": 132, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 132, "endColumn": 26}, {"ruleId": "1938", "severity": 1, "message": "2558", "line": 136, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 136, "endColumn": 26}, {"ruleId": "1938", "severity": 1, "message": "2559", "line": 150, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 150, "endColumn": 26}, {"ruleId": "1938", "severity": 1, "message": "2560", "line": 153, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 153, "endColumn": 28}, {"ruleId": "1938", "severity": 1, "message": "1945", "line": 5, "column": 28, "nodeType": "1940", "messageId": "1941", "endLine": 5, "endColumn": 35}, {"ruleId": "1938", "severity": 1, "message": "2561", "line": 8, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 8, "endColumn": 24}, {"ruleId": "1938", "severity": 1, "message": "2302", "line": 85, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 85, "endColumn": 24}, {"ruleId": "2042", "severity": 1, "message": "2562", "line": 98, "column": 6, "nodeType": "2044", "endLine": 98, "endColumn": 8, "suggestions": "2563"}, {"ruleId": "2042", "severity": 1, "message": "2564", "line": 121, "column": 6, "nodeType": "2044", "endLine": 121, "endColumn": 32, "suggestions": "2565"}, {"ruleId": "2042", "severity": 1, "message": "2308", "line": 125, "column": 6, "nodeType": "2044", "endLine": 125, "endColumn": 40, "suggestions": "2566"}, {"ruleId": "2042", "severity": 1, "message": "2097", "line": 125, "column": 7, "nodeType": "2093", "endLine": 125, "endColumn": 39}, {"ruleId": "2091", "severity": 1, "message": "2113", "line": 125, "column": 35, "nodeType": "2093", "messageId": "2094", "endLine": 125, "endColumn": 37}, {"ruleId": "2042", "severity": 1, "message": "2567", "line": 148, "column": 6, "nodeType": "2044", "endLine": 148, "endColumn": 33, "suggestions": "2568"}, {"ruleId": "2042", "severity": 1, "message": "2097", "line": 148, "column": 7, "nodeType": "2098", "endLine": 148, "endColumn": 32}, {"ruleId": "1938", "severity": 1, "message": "2569", "line": 156, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 156, "endColumn": 29}, {"ruleId": "1938", "severity": 1, "message": "2570", "line": 2, "column": 14, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 18}, {"ruleId": "1938", "severity": 1, "message": "2571", "line": 16, "column": 5, "nodeType": "1940", "messageId": "1941", "endLine": 16, "endColumn": 19}, {"ruleId": "1938", "severity": 1, "message": "2572", "line": 19, "column": 11, "nodeType": "1940", "messageId": "1941", "endLine": 19, "endColumn": 20}, {"ruleId": "1938", "severity": 1, "message": "2573", "line": 22, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 22, "endColumn": 20}, {"ruleId": "2091", "severity": 1, "message": "2092", "line": 43, "column": 100, "nodeType": "2093", "messageId": "2094", "endLine": 43, "endColumn": 102}, {"ruleId": "1938", "severity": 1, "message": "2574", "line": 4, "column": 23, "nodeType": "1940", "messageId": "1941", "endLine": 4, "endColumn": 30}, {"ruleId": "1938", "severity": 1, "message": "2575", "line": 4, "column": 32, "nodeType": "1940", "messageId": "1941", "endLine": 4, "endColumn": 45}, {"ruleId": "1938", "severity": 1, "message": "2576", "line": 10, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 10, "endColumn": 24}, {"ruleId": "1938", "severity": 1, "message": "2577", "line": 65, "column": 12, "nodeType": "1940", "messageId": "1941", "endLine": 65, "endColumn": 22}, {"ruleId": "1938", "severity": 1, "message": "2578", "line": 65, "column": 24, "nodeType": "1940", "messageId": "1941", "endLine": 65, "endColumn": 37}, {"ruleId": "1938", "severity": 1, "message": "2579", "line": 78, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 78, "endColumn": 22}, {"ruleId": "1938", "severity": 1, "message": "2580", "line": 78, "column": 24, "nodeType": "1940", "messageId": "1941", "endLine": 78, "endColumn": 39}, {"ruleId": "2042", "severity": 1, "message": "2581", "line": 120, "column": 6, "nodeType": "2044", "endLine": 120, "endColumn": 8, "suggestions": "2582"}, {"ruleId": "1938", "severity": 1, "message": "2583", "line": 157, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 157, "endColumn": 32}, {"ruleId": "1938", "severity": 1, "message": "2584", "line": 280, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 280, "endColumn": 29}, {"ruleId": "1938", "severity": 1, "message": "2585", "line": 296, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 296, "endColumn": 23}, {"ruleId": "1938", "severity": 1, "message": "2586", "line": 461, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 461, "endColumn": 17}, {"ruleId": "1938", "severity": 1, "message": "2587", "line": 462, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 462, "endColumn": 20}, {"ruleId": "2042", "severity": 1, "message": "2588", "line": 467, "column": 3, "nodeType": "2044", "endLine": 467, "endColumn": 5, "suggestions": "2589"}, {"ruleId": "1938", "severity": 1, "message": "2590", "line": 1, "column": 17, "nodeType": "1940", "messageId": "1941", "endLine": 1, "endColumn": 27}, {"ruleId": "1938", "severity": 1, "message": "2323", "line": 2, "column": 38, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 42}, {"ruleId": "1938", "severity": 1, "message": "1971", "line": 2, "column": 64, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 78}, {"ruleId": "1938", "severity": 1, "message": "2325", "line": 2, "column": 80, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 91}, {"ruleId": "1938", "severity": 1, "message": "2326", "line": 2, "column": 93, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 103}, {"ruleId": "1938", "severity": 1, "message": "2327", "line": 2, "column": 105, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 111}, {"ruleId": "1938", "severity": 1, "message": "2328", "line": 2, "column": 113, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 121}, {"ruleId": "1938", "severity": 1, "message": "2591", "line": 2, "column": 123, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 140}, {"ruleId": "1938", "severity": 1, "message": "2202", "line": 2, "column": 142, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 158}, {"ruleId": "1938", "severity": 1, "message": "2592", "line": 2, "column": 160, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 166}, {"ruleId": "1938", "severity": 1, "message": "2593", "line": 2, "column": 168, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 180}, {"ruleId": "1938", "severity": 1, "message": "2594", "line": 2, "column": 182, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 199}, {"ruleId": "1938", "severity": 1, "message": "2595", "line": 4, "column": 26, "nodeType": "1940", "messageId": "1941", "endLine": 4, "endColumn": 49}, {"ruleId": "1938", "severity": 1, "message": "2596", "line": 4, "column": 51, "nodeType": "1940", "messageId": "1941", "endLine": 4, "endColumn": 71}, {"ruleId": "1938", "severity": 1, "message": "2597", "line": 4, "column": 73, "nodeType": "1940", "messageId": "1941", "endLine": 4, "endColumn": 91}, {"ruleId": "1938", "severity": 1, "message": "2598", "line": 5, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 5, "endColumn": 31}, {"ruleId": "1938", "severity": 1, "message": "2599", "line": 13, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 13, "endColumn": 12}, {"ruleId": "1938", "severity": 1, "message": "2600", "line": 14, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 14, "endColumn": 14}, {"ruleId": "1938", "severity": 1, "message": "2601", "line": 15, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 15, "endColumn": 10}, {"ruleId": "1938", "severity": 1, "message": "1969", "line": 16, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 16, "endColumn": 10}, {"ruleId": "1938", "severity": 1, "message": "2602", "line": 17, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 17, "endColumn": 7}, {"ruleId": "1938", "severity": 1, "message": "2603", "line": 24, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 24, "endColumn": 28}, {"ruleId": "1938", "severity": 1, "message": "2604", "line": 25, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 25, "endColumn": 23}, {"ruleId": "1938", "severity": 1, "message": "2605", "line": 26, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 26, "endColumn": 18}, {"ruleId": "1938", "severity": 1, "message": "2606", "line": 27, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 27, "endColumn": 20}, {"ruleId": "1938", "severity": 1, "message": "2607", "line": 28, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 28, "endColumn": 23}, {"ruleId": "1938", "severity": 1, "message": "2608", "line": 29, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 29, "endColumn": 24}, {"ruleId": "1938", "severity": 1, "message": "2609", "line": 30, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 30, "endColumn": 18}, {"ruleId": "1938", "severity": 1, "message": "2610", "line": 40, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 40, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "2611", "line": 41, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 41, "endColumn": 16}, {"ruleId": "1938", "severity": 1, "message": "2612", "line": 43, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 43, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "2613", "line": 45, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 45, "endColumn": 16}, {"ruleId": "1938", "severity": 1, "message": "2310", "line": 47, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 47, "endColumn": 12}, {"ruleId": "1938", "severity": 1, "message": "2614", "line": 94, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 94, "endColumn": 19}, {"ruleId": "2042", "severity": 1, "message": "2615", "line": 125, "column": 5, "nodeType": "2044", "endLine": 125, "endColumn": 7, "suggestions": "2616"}, {"ruleId": "1938", "severity": 1, "message": "2617", "line": 145, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 145, "endColumn": 30}, {"ruleId": "1938", "severity": 1, "message": "2618", "line": 162, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 162, "endColumn": 25}, {"ruleId": "1938", "severity": 1, "message": "2619", "line": 165, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 165, "endColumn": 24}, {"ruleId": "1938", "severity": 1, "message": "2620", "line": 170, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 170, "endColumn": 25}, {"ruleId": "1938", "severity": 1, "message": "2621", "line": 211, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 211, "endColumn": 29}, {"ruleId": "1938", "severity": 1, "message": "2622", "line": 214, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 214, "endColumn": 33}, {"ruleId": "1938", "severity": 1, "message": "2623", "line": 227, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 227, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "2624", "line": 228, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 228, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "2625", "line": 228, "column": 15, "nodeType": "1940", "messageId": "1941", "endLine": 228, "endColumn": 22}, {"ruleId": "1938", "severity": 1, "message": "2626", "line": 229, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 229, "endColumn": 18}, {"ruleId": "1938", "severity": 1, "message": "2627", "line": 229, "column": 20, "nodeType": "1940", "messageId": "1941", "endLine": 229, "endColumn": 32}, {"ruleId": "1938", "severity": 1, "message": "2024", "line": 245, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 245, "endColumn": 14}, {"ruleId": "1938", "severity": 1, "message": "2025", "line": 245, "column": 16, "nodeType": "1940", "messageId": "1941", "endLine": 245, "endColumn": 24}, {"ruleId": "1938", "severity": 1, "message": "2628", "line": 247, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 247, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "2629", "line": 261, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 261, "endColumn": 36}, {"ruleId": "2042", "severity": 1, "message": "2630", "line": 281, "column": 4, "nodeType": "2044", "endLine": 281, "endColumn": 6, "suggestions": "2631"}, {"ruleId": "1938", "severity": 1, "message": "2629", "line": 334, "column": 12, "nodeType": "1940", "messageId": "1941", "endLine": 334, "endColumn": 38}, {"ruleId": "1938", "severity": 1, "message": "2632", "line": 347, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 347, "endColumn": 25}, {"ruleId": "1938", "severity": 1, "message": "2633", "line": 347, "column": 27, "nodeType": "1940", "messageId": "1941", "endLine": 347, "endColumn": 45}, {"ruleId": "1938", "severity": 1, "message": "2590", "line": 1, "column": 17, "nodeType": "1940", "messageId": "1941", "endLine": 1, "endColumn": 27}, {"ruleId": "1938", "severity": 1, "message": "2323", "line": 2, "column": 38, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 42}, {"ruleId": "1938", "severity": 1, "message": "1971", "line": 2, "column": 64, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 78}, {"ruleId": "1938", "severity": 1, "message": "2325", "line": 2, "column": 80, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 91}, {"ruleId": "1938", "severity": 1, "message": "2326", "line": 2, "column": 93, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 103}, {"ruleId": "1938", "severity": 1, "message": "2327", "line": 2, "column": 105, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 111}, {"ruleId": "1938", "severity": 1, "message": "2328", "line": 2, "column": 113, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 121}, {"ruleId": "1938", "severity": 1, "message": "2591", "line": 2, "column": 123, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 140}, {"ruleId": "1938", "severity": 1, "message": "2202", "line": 2, "column": 142, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 158}, {"ruleId": "1938", "severity": 1, "message": "2592", "line": 2, "column": 160, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 166}, {"ruleId": "1938", "severity": 1, "message": "2330", "line": 2, "column": 168, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 175}, {"ruleId": "1938", "severity": 1, "message": "2595", "line": 4, "column": 26, "nodeType": "1940", "messageId": "1941", "endLine": 4, "endColumn": 49}, {"ruleId": "1938", "severity": 1, "message": "2596", "line": 4, "column": 51, "nodeType": "1940", "messageId": "1941", "endLine": 4, "endColumn": 71}, {"ruleId": "1938", "severity": 1, "message": "2597", "line": 4, "column": 73, "nodeType": "1940", "messageId": "1941", "endLine": 4, "endColumn": 91}, {"ruleId": "1938", "severity": 1, "message": "2598", "line": 5, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 5, "endColumn": 31}, {"ruleId": "1938", "severity": 1, "message": "2599", "line": 8, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 8, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "2600", "line": 9, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 9, "endColumn": 15}, {"ruleId": "1938", "severity": 1, "message": "2601", "line": 10, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 10, "endColumn": 11}, {"ruleId": "1938", "severity": 1, "message": "2602", "line": 11, "column": 5, "nodeType": "1940", "messageId": "1941", "endLine": 11, "endColumn": 10}, {"ruleId": "1938", "severity": 1, "message": "1969", "line": 12, "column": 5, "nodeType": "1940", "messageId": "1941", "endLine": 12, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "2634", "line": 13, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 13, "endColumn": 12}, {"ruleId": "1938", "severity": 1, "message": "2635", "line": 14, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 14, "endColumn": 12}, {"ruleId": "1938", "severity": 1, "message": "2636", "line": 15, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 15, "endColumn": 12}, {"ruleId": "1938", "severity": 1, "message": "2637", "line": 22, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 22, "endColumn": 22}, {"ruleId": "1938", "severity": 1, "message": "2638", "line": 31, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 31, "endColumn": 23}, {"ruleId": "1938", "severity": 1, "message": "2293", "line": 32, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 32, "endColumn": 26}, {"ruleId": "1938", "severity": 1, "message": "2610", "line": 35, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 35, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "2611", "line": 36, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 36, "endColumn": 16}, {"ruleId": "1938", "severity": 1, "message": "2612", "line": 38, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 38, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "2639", "line": 39, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 39, "endColumn": 16}, {"ruleId": "1938", "severity": 1, "message": "2640", "line": 40, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 40, "endColumn": 19}, {"ruleId": "1938", "severity": 1, "message": "2641", "line": 42, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 42, "endColumn": 23}, {"ruleId": "1938", "severity": 1, "message": "2642", "line": 43, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 43, "endColumn": 26}, {"ruleId": "1938", "severity": 1, "message": "2643", "line": 44, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 44, "endColumn": 29}, {"ruleId": "1938", "severity": 1, "message": "2644", "line": 45, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 45, "endColumn": 32}, {"ruleId": "1938", "severity": 1, "message": "2645", "line": 46, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 46, "endColumn": 22}, {"ruleId": "1938", "severity": 1, "message": "2646", "line": 47, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 47, "endColumn": 25}, {"ruleId": "1938", "severity": 1, "message": "2647", "line": 48, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 48, "endColumn": 29}, {"ruleId": "1938", "severity": 1, "message": "2648", "line": 49, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 49, "endColumn": 26}, {"ruleId": "1938", "severity": 1, "message": "2649", "line": 50, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 50, "endColumn": 20}, {"ruleId": "1938", "severity": 1, "message": "2650", "line": 51, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 51, "endColumn": 23}, {"ruleId": "1938", "severity": 1, "message": "2518", "line": 58, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 58, "endColumn": 19}, {"ruleId": "1938", "severity": 1, "message": "2572", "line": 60, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 60, "endColumn": 19}, {"ruleId": "1938", "severity": 1, "message": "2614", "line": 75, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 75, "endColumn": 19}, {"ruleId": "2042", "severity": 1, "message": "2567", "line": 87, "column": 5, "nodeType": "2044", "endLine": 87, "endColumn": 45, "suggestions": "2651"}, {"ruleId": "2042", "severity": 1, "message": "2097", "line": 87, "column": 6, "nodeType": "2146", "endLine": 87, "endColumn": 44}, {"ruleId": "1938", "severity": 1, "message": "2652", "line": 106, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 106, "endColumn": 21}, {"ruleId": "1938", "severity": 1, "message": "2653", "line": 106, "column": 23, "nodeType": "1940", "messageId": "1941", "endLine": 106, "endColumn": 38}, {"ruleId": "1938", "severity": 1, "message": "2654", "line": 107, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 107, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "2655", "line": 107, "column": 15, "nodeType": "1940", "messageId": "1941", "endLine": 107, "endColumn": 22}, {"ruleId": "1938", "severity": 1, "message": "2656", "line": 108, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 108, "endColumn": 11}, {"ruleId": "1938", "severity": 1, "message": "2216", "line": 109, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 109, "endColumn": 16}, {"ruleId": "1938", "severity": 1, "message": "2657", "line": 109, "column": 18, "nodeType": "1940", "messageId": "1941", "endLine": 109, "endColumn": 28}, {"ruleId": "1938", "severity": 1, "message": "2658", "line": 110, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 110, "endColumn": 19}, {"ruleId": "1938", "severity": 1, "message": "2618", "line": 115, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 115, "endColumn": 25}, {"ruleId": "1938", "severity": 1, "message": "2620", "line": 119, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 119, "endColumn": 25}, {"ruleId": "2091", "severity": 1, "message": "2113", "line": 134, "column": 11, "nodeType": "2093", "messageId": "2094", "endLine": 134, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "2330", "line": 2, "column": 64, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 71}, {"ruleId": "1938", "severity": 1, "message": "2659", "line": 4, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 4, "endColumn": 32}, {"ruleId": "1938", "severity": 1, "message": "2660", "line": 5, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 5, "endColumn": 30}, {"ruleId": "1938", "severity": 1, "message": "2661", "line": 10, "column": 30, "nodeType": "1940", "messageId": "1941", "endLine": 10, "endColumn": 39}, {"ruleId": "1938", "severity": 1, "message": "2244", "line": 2, "column": 15, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 25}, {"ruleId": "1938", "severity": 1, "message": "2593", "line": 2, "column": 27, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 39}, {"ruleId": "1938", "severity": 1, "message": "2594", "line": 2, "column": 41, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 58}, {"ruleId": "1938", "severity": 1, "message": "2202", "line": 2, "column": 72, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 88}, {"ruleId": "1938", "severity": 1, "message": "2592", "line": 2, "column": 90, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 96}, {"ruleId": "1938", "severity": 1, "message": "2662", "line": 9, "column": 11, "nodeType": "1940", "messageId": "1941", "endLine": 9, "endColumn": 31}, {"ruleId": "1938", "severity": 1, "message": "2592", "line": 6, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 6, "endColumn": 8}, {"ruleId": "1938", "severity": 1, "message": "2327", "line": 9, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 9, "endColumn": 8}, {"ruleId": "1938", "severity": 1, "message": "2328", "line": 10, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 10, "endColumn": 10}, {"ruleId": "1938", "severity": 1, "message": "2325", "line": 11, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 11, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "2330", "line": 12, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 12, "endColumn": 9}, {"ruleId": "1938", "severity": 1, "message": "2663", "line": 19, "column": 11, "nodeType": "1940", "messageId": "1941", "endLine": 19, "endColumn": 32}, {"ruleId": "1938", "severity": 1, "message": "2362", "line": 35, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 35, "endColumn": 20}, {"ruleId": "1938", "severity": 1, "message": "2664", "line": 37, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 37, "endColumn": 27}, {"ruleId": "1938", "severity": 1, "message": "2461", "line": 38, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 38, "endColumn": 23}, {"ruleId": "1938", "severity": 1, "message": "2315", "line": 39, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 39, "endColumn": 14}, {"ruleId": "1938", "severity": 1, "message": "2665", "line": 40, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 40, "endColumn": 22}, {"ruleId": "1938", "severity": 1, "message": "2218", "line": 42, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 42, "endColumn": 14}, {"ruleId": "1938", "severity": 1, "message": "2223", "line": 48, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 48, "endColumn": 19}, {"ruleId": "1938", "severity": 1, "message": "2666", "line": 55, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 55, "endColumn": 20}, {"ruleId": "1938", "severity": 1, "message": "2667", "line": 56, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 56, "endColumn": 22}, {"ruleId": "1938", "severity": 1, "message": "2668", "line": 57, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 57, "endColumn": 15}, {"ruleId": "1938", "severity": 1, "message": "2669", "line": 86, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 86, "endColumn": 31}, {"ruleId": "1938", "severity": 1, "message": "2670", "line": 90, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 90, "endColumn": 31}, {"ruleId": "1938", "severity": 1, "message": "2671", "line": 95, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 95, "endColumn": 33}, {"ruleId": "2042", "severity": 1, "message": "2672", "line": 195, "column": 5, "nodeType": "2044", "endLine": 195, "endColumn": 30, "suggestions": "2673"}, {"ruleId": "1938", "severity": 1, "message": "2242", "line": 3, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 3, "endColumn": 5}, {"ruleId": "1938", "severity": 1, "message": "2244", "line": 4, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 4, "endColumn": 12}, {"ruleId": "1938", "severity": 1, "message": "2326", "line": 9, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 9, "endColumn": 12}, {"ruleId": "1938", "severity": 1, "message": "2242", "line": 2, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "2244", "line": 2, "column": 23, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 33}, {"ruleId": "1938", "severity": 1, "message": "2323", "line": 2, "column": 38, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 42}, {"ruleId": "1938", "severity": 1, "message": "2330", "line": 2, "column": 64, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 71}, {"ruleId": "1938", "severity": 1, "message": "2659", "line": 4, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 4, "endColumn": 32}, {"ruleId": "1938", "severity": 1, "message": "2660", "line": 5, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 5, "endColumn": 30}, {"ruleId": "1938", "severity": 1, "message": "2674", "line": 9, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 9, "endColumn": 21}, {"ruleId": "1938", "severity": 1, "message": "2675", "line": 9, "column": 23, "nodeType": "1940", "messageId": "1941", "endLine": 9, "endColumn": 30}, {"ruleId": "1938", "severity": 1, "message": "2661", "line": 9, "column": 32, "nodeType": "1940", "messageId": "1941", "endLine": 9, "endColumn": 41}, {"ruleId": "1938", "severity": 1, "message": "2676", "line": 9, "column": 43, "nodeType": "1940", "messageId": "1941", "endLine": 9, "endColumn": 51}, {"ruleId": "1938", "severity": 1, "message": "2677", "line": 9, "column": 53, "nodeType": "1940", "messageId": "1941", "endLine": 9, "endColumn": 63}, {"ruleId": "1938", "severity": 1, "message": "2678", "line": 9, "column": 65, "nodeType": "1940", "messageId": "1941", "endLine": 9, "endColumn": 77}, {"ruleId": "1938", "severity": 1, "message": "2679", "line": 9, "column": 79, "nodeType": "1940", "messageId": "1941", "endLine": 9, "endColumn": 90}, {"ruleId": "1938", "severity": 1, "message": "2680", "line": 9, "column": 92, "nodeType": "1940", "messageId": "1941", "endLine": 9, "endColumn": 102}, {"ruleId": "1938", "severity": 1, "message": "2681", "line": 9, "column": 104, "nodeType": "1940", "messageId": "1941", "endLine": 9, "endColumn": 116}, {"ruleId": "1938", "severity": 1, "message": "2682", "line": 9, "column": 118, "nodeType": "1940", "messageId": "1941", "endLine": 9, "endColumn": 129}, {"ruleId": "1938", "severity": 1, "message": "2683", "line": 9, "column": 131, "nodeType": "1940", "messageId": "1941", "endLine": 9, "endColumn": 140}, {"ruleId": "1938", "severity": 1, "message": "2684", "line": 15, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 15, "endColumn": 19}, {"ruleId": "1938", "severity": 1, "message": "2345", "line": 16, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 16, "endColumn": 14}, {"ruleId": "1938", "severity": 1, "message": "2685", "line": 17, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 17, "endColumn": 19}, {"ruleId": "1938", "severity": 1, "message": "2469", "line": 18, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 18, "endColumn": 8}, {"ruleId": "1938", "severity": 1, "message": "2686", "line": 19, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 19, "endColumn": 11}, {"ruleId": "1938", "severity": 1, "message": "2048", "line": 20, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 20, "endColumn": 18}, {"ruleId": "1938", "severity": 1, "message": "2468", "line": 23, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 23, "endColumn": 15}, {"ruleId": "1938", "severity": 1, "message": "2687", "line": 24, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 24, "endColumn": 18}, {"ruleId": "1938", "severity": 1, "message": "2688", "line": 25, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 25, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "2689", "line": 26, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 26, "endColumn": 16}, {"ruleId": "1938", "severity": 1, "message": "2690", "line": 27, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 27, "endColumn": 16}, {"ruleId": "1938", "severity": 1, "message": "2691", "line": 28, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 28, "endColumn": 19}, {"ruleId": "1938", "severity": 1, "message": "2692", "line": 29, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 29, "endColumn": 12}, {"ruleId": "1938", "severity": 1, "message": "2693", "line": 30, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 30, "endColumn": 15}, {"ruleId": "1938", "severity": 1, "message": "2518", "line": 34, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 34, "endColumn": 19}, {"ruleId": "1938", "severity": 1, "message": "2614", "line": 62, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 62, "endColumn": 19}, {"ruleId": "1938", "severity": 1, "message": "2670", "line": 82, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 82, "endColumn": 31}, {"ruleId": "1938", "severity": 1, "message": "2694", "line": 83, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 83, "endColumn": 35}, {"ruleId": "1938", "severity": 1, "message": "2590", "line": 1, "column": 17, "nodeType": "1940", "messageId": "1941", "endLine": 1, "endColumn": 27}, {"ruleId": "1938", "severity": 1, "message": "2323", "line": 3, "column": 38, "nodeType": "1940", "messageId": "1941", "endLine": 3, "endColumn": 42}, {"ruleId": "1938", "severity": 1, "message": "1971", "line": 3, "column": 64, "nodeType": "1940", "messageId": "1941", "endLine": 3, "endColumn": 78}, {"ruleId": "1938", "severity": 1, "message": "2325", "line": 3, "column": 80, "nodeType": "1940", "messageId": "1941", "endLine": 3, "endColumn": 91}, {"ruleId": "1938", "severity": 1, "message": "2326", "line": 3, "column": 93, "nodeType": "1940", "messageId": "1941", "endLine": 3, "endColumn": 103}, {"ruleId": "1938", "severity": 1, "message": "2327", "line": 3, "column": 105, "nodeType": "1940", "messageId": "1941", "endLine": 3, "endColumn": 111}, {"ruleId": "1938", "severity": 1, "message": "2328", "line": 3, "column": 113, "nodeType": "1940", "messageId": "1941", "endLine": 3, "endColumn": 121}, {"ruleId": "1938", "severity": 1, "message": "2591", "line": 3, "column": 123, "nodeType": "1940", "messageId": "1941", "endLine": 3, "endColumn": 140}, {"ruleId": "1938", "severity": 1, "message": "2202", "line": 3, "column": 142, "nodeType": "1940", "messageId": "1941", "endLine": 3, "endColumn": 158}, {"ruleId": "1938", "severity": 1, "message": "2592", "line": 3, "column": 160, "nodeType": "1940", "messageId": "1941", "endLine": 3, "endColumn": 166}, {"ruleId": "1938", "severity": 1, "message": "2595", "line": 5, "column": 26, "nodeType": "1940", "messageId": "1941", "endLine": 5, "endColumn": 49}, {"ruleId": "1938", "severity": 1, "message": "2596", "line": 5, "column": 51, "nodeType": "1940", "messageId": "1941", "endLine": 5, "endColumn": 71}, {"ruleId": "1938", "severity": 1, "message": "2597", "line": 5, "column": 73, "nodeType": "1940", "messageId": "1941", "endLine": 5, "endColumn": 91}, {"ruleId": "1938", "severity": 1, "message": "2598", "line": 6, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 6, "endColumn": 31}, {"ruleId": "1938", "severity": 1, "message": "2599", "line": 8, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 8, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "2600", "line": 9, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 9, "endColumn": 15}, {"ruleId": "1938", "severity": 1, "message": "2601", "line": 10, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 10, "endColumn": 11}, {"ruleId": "1938", "severity": 1, "message": "2602", "line": 11, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 11, "endColumn": 8}, {"ruleId": "1938", "severity": 1, "message": "2610", "line": 19, "column": 4, "nodeType": "1940", "messageId": "1941", "endLine": 19, "endColumn": 14}, {"ruleId": "1938", "severity": 1, "message": "2612", "line": 22, "column": 4, "nodeType": "1940", "messageId": "1941", "endLine": 22, "endColumn": 14}, {"ruleId": "1938", "severity": 1, "message": "2695", "line": 24, "column": 4, "nodeType": "1940", "messageId": "1941", "endLine": 24, "endColumn": 18}, {"ruleId": "1938", "severity": 1, "message": "2696", "line": 25, "column": 4, "nodeType": "1940", "messageId": "1941", "endLine": 25, "endColumn": 21}, {"ruleId": "1938", "severity": 1, "message": "2697", "line": 26, "column": 4, "nodeType": "1940", "messageId": "1941", "endLine": 26, "endColumn": 21}, {"ruleId": "1938", "severity": 1, "message": "2698", "line": 27, "column": 4, "nodeType": "1940", "messageId": "1941", "endLine": 27, "endColumn": 24}, {"ruleId": "1938", "severity": 1, "message": "2518", "line": 31, "column": 4, "nodeType": "1940", "messageId": "1941", "endLine": 31, "endColumn": 20}, {"ruleId": "1938", "severity": 1, "message": "2614", "line": 36, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 36, "endColumn": 20}, {"ruleId": "1938", "severity": 1, "message": "2699", "line": 104, "column": 21, "nodeType": "1940", "messageId": "1941", "endLine": 104, "endColumn": 33}, {"ruleId": "1938", "severity": 1, "message": "2700", "line": 105, "column": 24, "nodeType": "1940", "messageId": "1941", "endLine": 105, "endColumn": 40}, {"ruleId": "1938", "severity": 1, "message": "2617", "line": 120, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 120, "endColumn": 31}, {"ruleId": "1938", "severity": 1, "message": "2618", "line": 154, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 154, "endColumn": 26}, {"ruleId": "1938", "severity": 1, "message": "2701", "line": 157, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 157, "endColumn": 19}, {"ruleId": "1938", "severity": 1, "message": "2323", "line": 3, "column": 38, "nodeType": "1940", "messageId": "1941", "endLine": 3, "endColumn": 42}, {"ruleId": "1938", "severity": 1, "message": "2330", "line": 3, "column": 56, "nodeType": "1940", "messageId": "1941", "endLine": 3, "endColumn": 63}, {"ruleId": "1938", "severity": 1, "message": "2219", "line": 13, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 13, "endColumn": 19}, {"ruleId": "1938", "severity": 1, "message": "2342", "line": 14, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 14, "endColumn": 10}, {"ruleId": "1938", "severity": 1, "message": "2343", "line": 15, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 15, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "2029", "line": 16, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 16, "endColumn": 11}, {"ruleId": "1938", "severity": 1, "message": "2031", "line": 18, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 18, "endColumn": 9}, {"ruleId": "1938", "severity": 1, "message": "2032", "line": 19, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 19, "endColumn": 12}, {"ruleId": "1938", "severity": 1, "message": "2033", "line": 20, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 20, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "2034", "line": 21, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 21, "endColumn": 16}, {"ruleId": "1938", "severity": 1, "message": "2344", "line": 22, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 22, "endColumn": 17}, {"ruleId": "1938", "severity": 1, "message": "2345", "line": 23, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 23, "endColumn": 14}, {"ruleId": "1938", "severity": 1, "message": "2346", "line": 24, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 24, "endColumn": 21}, {"ruleId": "1938", "severity": 1, "message": "2048", "line": 25, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 25, "endColumn": 18}, {"ruleId": "1938", "severity": 1, "message": "2702", "line": 37, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 37, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "2384", "line": 39, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 39, "endColumn": 18}, {"ruleId": "1938", "severity": 1, "message": "2703", "line": 41, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 41, "endColumn": 20}, {"ruleId": "1938", "severity": 1, "message": "2704", "line": 45, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 45, "endColumn": 28}, {"ruleId": "1938", "severity": 1, "message": "2705", "line": 49, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 49, "endColumn": 23}, {"ruleId": "1938", "severity": 1, "message": "2706", "line": 49, "column": 25, "nodeType": "1940", "messageId": "1941", "endLine": 49, "endColumn": 42}, {"ruleId": "1938", "severity": 1, "message": "2707", "line": 50, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 50, "endColumn": 19}, {"ruleId": "1938", "severity": 1, "message": "2708", "line": 50, "column": 21, "nodeType": "1940", "messageId": "1941", "endLine": 50, "endColumn": 34}, {"ruleId": "1938", "severity": 1, "message": "2709", "line": 51, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 51, "endColumn": 24}, {"ruleId": "1938", "severity": 1, "message": "2710", "line": 51, "column": 26, "nodeType": "1940", "messageId": "1941", "endLine": 51, "endColumn": 44}, {"ruleId": "1938", "severity": 1, "message": "2711", "line": 52, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 52, "endColumn": 28}, {"ruleId": "1938", "severity": 1, "message": "2712", "line": 52, "column": 30, "nodeType": "1940", "messageId": "1941", "endLine": 52, "endColumn": 52}, {"ruleId": "1938", "severity": 1, "message": "2713", "line": 53, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 53, "endColumn": 25}, {"ruleId": "1938", "severity": 1, "message": "2714", "line": 53, "column": 27, "nodeType": "1940", "messageId": "1941", "endLine": 53, "endColumn": 46}, {"ruleId": "1938", "severity": 1, "message": "2715", "line": 3, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 3, "endColumn": 8}, {"ruleId": "1938", "severity": 1, "message": "2716", "line": 4, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 4, "endColumn": 15}, {"ruleId": "1938", "severity": 1, "message": "2717", "line": 5, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 5, "endColumn": 15}, {"ruleId": "1938", "severity": 1, "message": "2718", "line": 6, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 6, "endColumn": 10}, {"ruleId": "1938", "severity": 1, "message": "2503", "line": 9, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 9, "endColumn": 9}, {"ruleId": "1938", "severity": 1, "message": "2200", "line": 17, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 17, "endColumn": 12}, {"ruleId": "1938", "severity": 1, "message": "2201", "line": 18, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 18, "endColumn": 7}, {"ruleId": "1938", "severity": 1, "message": "2202", "line": 19, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 19, "endColumn": 18}, {"ruleId": "1938", "severity": 1, "message": "2203", "line": 20, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 20, "endColumn": 7}, {"ruleId": "1938", "severity": 1, "message": "2204", "line": 23, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 23, "endColumn": 14}, {"ruleId": "1938", "severity": 1, "message": "2205", "line": 24, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 24, "endColumn": 18}, {"ruleId": "1938", "severity": 1, "message": "2206", "line": 25, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 25, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "2330", "line": 26, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 26, "endColumn": 9}, {"ruleId": "1938", "severity": 1, "message": "2402", "line": 43, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 43, "endColumn": 35}, {"ruleId": "1938", "severity": 1, "message": "2719", "line": 44, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 44, "endColumn": 14}, {"ruleId": "1938", "severity": 1, "message": "2720", "line": 50, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 50, "endColumn": 19}, {"ruleId": "1938", "severity": 1, "message": "2721", "line": 51, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 51, "endColumn": 23}, {"ruleId": "1938", "severity": 1, "message": "2722", "line": 53, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 53, "endColumn": 15}, {"ruleId": "1938", "severity": 1, "message": "2723", "line": 54, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 54, "endColumn": 15}, {"ruleId": "1938", "severity": 1, "message": "2724", "line": 55, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 55, "endColumn": 24}, {"ruleId": "1938", "severity": 1, "message": "2725", "line": 56, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 56, "endColumn": 18}, {"ruleId": "1938", "severity": 1, "message": "2255", "line": 59, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 59, "endColumn": 22}, {"ruleId": "1938", "severity": 1, "message": "2540", "line": 60, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 60, "endColumn": 17}, {"ruleId": "1938", "severity": 1, "message": "2213", "line": 62, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 62, "endColumn": 19}, {"ruleId": "1938", "severity": 1, "message": "2214", "line": 68, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 68, "endColumn": 25}, {"ruleId": "1938", "severity": 1, "message": "2215", "line": 69, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 69, "endColumn": 22}, {"ruleId": "1938", "severity": 1, "message": "2216", "line": 75, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 75, "endColumn": 10}, {"ruleId": "1938", "severity": 1, "message": "2726", "line": 77, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 77, "endColumn": 26}, {"ruleId": "1938", "severity": 1, "message": "2058", "line": 80, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 80, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "2059", "line": 81, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 81, "endColumn": 15}, {"ruleId": "1938", "severity": 1, "message": "2057", "line": 82, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 82, "endColumn": 17}, {"ruleId": "1938", "severity": 1, "message": "2727", "line": 83, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 83, "endColumn": 16}, {"ruleId": "1938", "severity": 1, "message": "2728", "line": 84, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 84, "endColumn": 18}, {"ruleId": "1938", "severity": 1, "message": "2729", "line": 85, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 85, "endColumn": 20}, {"ruleId": "1938", "severity": 1, "message": "2229", "line": 87, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 87, "endColumn": 14}, {"ruleId": "1938", "severity": 1, "message": "2227", "line": 89, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 89, "endColumn": 11}, {"ruleId": "1938", "severity": 1, "message": "2231", "line": 91, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 91, "endColumn": 10}, {"ruleId": "1938", "severity": 1, "message": "2730", "line": 92, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 92, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "2224", "line": 94, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 94, "endColumn": 15}, {"ruleId": "1938", "severity": 1, "message": "2345", "line": 101, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 101, "endColumn": 20}, {"ruleId": "1938", "severity": 1, "message": "2344", "line": 101, "column": 22, "nodeType": "1940", "messageId": "1941", "endLine": 101, "endColumn": 36}, {"ruleId": "1938", "severity": 1, "message": "2048", "line": 102, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 102, "endColumn": 24}, {"ruleId": "1938", "severity": 1, "message": "2346", "line": 102, "column": 26, "nodeType": "1940", "messageId": "1941", "endLine": 102, "endColumn": 44}, {"ruleId": "1938", "severity": 1, "message": "2731", "line": 103, "column": 17, "nodeType": "1940", "messageId": "1941", "endLine": 103, "endColumn": 26}, {"ruleId": "1938", "severity": 1, "message": "2732", "line": 104, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 104, "endColumn": 25}, {"ruleId": "1938", "severity": 1, "message": "2733", "line": 105, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 105, "endColumn": 12}, {"ruleId": "1938", "severity": 1, "message": "2734", "line": 105, "column": 14, "nodeType": "1940", "messageId": "1941", "endLine": 105, "endColumn": 20}, {"ruleId": "1938", "severity": 1, "message": "2225", "line": 106, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 106, "endColumn": 15}, {"ruleId": "1938", "severity": 1, "message": "2735", "line": 106, "column": 17, "nodeType": "1940", "messageId": "1941", "endLine": 106, "endColumn": 26}, {"ruleId": "1938", "severity": 1, "message": "2429", "line": 107, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 107, "endColumn": 21}, {"ruleId": "1938", "severity": 1, "message": "2736", "line": 107, "column": 23, "nodeType": "1940", "messageId": "1941", "endLine": 107, "endColumn": 38}, {"ruleId": "1938", "severity": 1, "message": "2668", "line": 118, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 118, "endColumn": 15}, {"ruleId": "1938", "severity": 1, "message": "2737", "line": 118, "column": 17, "nodeType": "1940", "messageId": "1941", "endLine": 118, "endColumn": 26}, {"ruleId": "1938", "severity": 1, "message": "2738", "line": 125, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 125, "endColumn": 24}, {"ruleId": "1938", "severity": 1, "message": "2739", "line": 125, "column": 26, "nodeType": "1940", "messageId": "1941", "endLine": 125, "endColumn": 44}, {"ruleId": "2042", "severity": 1, "message": "2740", "line": 148, "column": 5, "nodeType": "2044", "endLine": 148, "endColumn": 60, "suggestions": "2741"}, {"ruleId": "1938", "severity": 1, "message": "2742", "line": 151, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 151, "endColumn": 17}, {"ruleId": "1938", "severity": 1, "message": "2743", "line": 163, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 163, "endColumn": 24}, {"ruleId": "2042", "severity": 1, "message": "2744", "line": 167, "column": 5, "nodeType": "2044", "endLine": 167, "endColumn": 60, "suggestions": "2745"}, {"ruleId": "1938", "severity": 1, "message": "2746", "line": 169, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 169, "endColumn": 27}, {"ruleId": "1938", "severity": 1, "message": "2232", "line": 203, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 203, "endColumn": 30}, {"ruleId": "1938", "severity": 1, "message": "2233", "line": 207, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 207, "endColumn": 29}, {"ruleId": "1938", "severity": 1, "message": "2529", "line": 2, "column": 56, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 65}, {"ruleId": "1938", "severity": 1, "message": "2328", "line": 2, "column": 67, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 75}, {"ruleId": "1938", "severity": 1, "message": "2180", "line": 2, "column": 77, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 83}, {"ruleId": "1938", "severity": 1, "message": "2538", "line": 13, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 13, "endColumn": 21}, {"ruleId": "1938", "severity": 1, "message": "2747", "line": 47, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 47, "endColumn": 48}, {"ruleId": "1938", "severity": 1, "message": "2425", "line": 59, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 59, "endColumn": 39}, {"ruleId": "1938", "severity": 1, "message": "2748", "line": 68, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 68, "endColumn": 41}, {"ruleId": "1938", "severity": 1, "message": "2749", "line": 74, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 74, "endColumn": 42}, {"ruleId": "1938", "severity": 1, "message": "2590", "line": 1, "column": 17, "nodeType": "1940", "messageId": "1941", "endLine": 1, "endColumn": 27}, {"ruleId": "1938", "severity": 1, "message": "2449", "line": 1, "column": 38, "nodeType": "1940", "messageId": "1941", "endLine": 1, "endColumn": 47}, {"ruleId": "1938", "severity": 1, "message": "2323", "line": 2, "column": 38, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 42}, {"ruleId": "1938", "severity": 1, "message": "1971", "line": 2, "column": 64, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 78}, {"ruleId": "1938", "severity": 1, "message": "2326", "line": 2, "column": 93, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 103}, {"ruleId": "1938", "severity": 1, "message": "2591", "line": 2, "column": 123, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 140}, {"ruleId": "1938", "severity": 1, "message": "2202", "line": 2, "column": 142, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 158}, {"ruleId": "1938", "severity": 1, "message": "2592", "line": 2, "column": 160, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 166}, {"ruleId": "1938", "severity": 1, "message": "2595", "line": 4, "column": 26, "nodeType": "1940", "messageId": "1941", "endLine": 4, "endColumn": 49}, {"ruleId": "1938", "severity": 1, "message": "2596", "line": 4, "column": 51, "nodeType": "1940", "messageId": "1941", "endLine": 4, "endColumn": 71}, {"ruleId": "1938", "severity": 1, "message": "2597", "line": 4, "column": 73, "nodeType": "1940", "messageId": "1941", "endLine": 4, "endColumn": 91}, {"ruleId": "1938", "severity": 1, "message": "2598", "line": 5, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 5, "endColumn": 31}, {"ruleId": "1938", "severity": 1, "message": "2750", "line": 23, "column": 4, "nodeType": "1940", "messageId": "1941", "endLine": 23, "endColumn": 18}, {"ruleId": "1938", "severity": 1, "message": "2067", "line": 24, "column": 4, "nodeType": "1940", "messageId": "1941", "endLine": 24, "endColumn": 15}, {"ruleId": "1938", "severity": 1, "message": "2751", "line": 26, "column": 4, "nodeType": "1940", "messageId": "1941", "endLine": 26, "endColumn": 29}, {"ruleId": "1938", "severity": 1, "message": "2063", "line": 27, "column": 4, "nodeType": "1940", "messageId": "1941", "endLine": 27, "endColumn": 25}, {"ruleId": "1938", "severity": 1, "message": "2752", "line": 28, "column": 4, "nodeType": "1940", "messageId": "1941", "endLine": 28, "endColumn": 29}, {"ruleId": "1938", "severity": 1, "message": "2753", "line": 29, "column": 4, "nodeType": "1940", "messageId": "1941", "endLine": 29, "endColumn": 31}, {"ruleId": "1938", "severity": 1, "message": "2369", "line": 85, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 85, "endColumn": 25}, {"ruleId": "1938", "severity": 1, "message": "2401", "line": 4, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 4, "endColumn": 26}, {"ruleId": "1938", "severity": 1, "message": "2058", "line": 11, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 11, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "2059", "line": 12, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 12, "endColumn": 15}, {"ruleId": "1938", "severity": 1, "message": "2057", "line": 13, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 13, "endColumn": 17}, {"ruleId": "1938", "severity": 1, "message": "2407", "line": 21, "column": 21, "nodeType": "1940", "messageId": "1941", "endLine": 21, "endColumn": 34}, {"ruleId": "1938", "severity": 1, "message": "2408", "line": 22, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 22, "endColumn": 21}, {"ruleId": "1938", "severity": 1, "message": "2409", "line": 22, "column": 33, "nodeType": "1940", "messageId": "1941", "endLine": 22, "endColumn": 44}, {"ruleId": "2091", "severity": 1, "message": "2113", "line": 82, "column": 22, "nodeType": "2093", "messageId": "2094", "endLine": 82, "endColumn": 24}, {"ruleId": "2091", "severity": 1, "message": "2113", "line": 82, "column": 53, "nodeType": "2093", "messageId": "2094", "endLine": 82, "endColumn": 55}, {"ruleId": "2091", "severity": 1, "message": "2113", "line": 85, "column": 36, "nodeType": "2093", "messageId": "2094", "endLine": 85, "endColumn": 38}, {"ruleId": "2091", "severity": 1, "message": "2113", "line": 85, "column": 63, "nodeType": "2093", "messageId": "2094", "endLine": 85, "endColumn": 65}, {"ruleId": "2091", "severity": 1, "message": "2113", "line": 88, "column": 36, "nodeType": "2093", "messageId": "2094", "endLine": 88, "endColumn": 38}, {"ruleId": "1938", "severity": 1, "message": "2411", "line": 95, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 95, "endColumn": 17}, {"ruleId": "1938", "severity": 1, "message": "2412", "line": 96, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 96, "endColumn": 19}, {"ruleId": "1938", "severity": 1, "message": "2413", "line": 99, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 99, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "2414", "line": 100, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 100, "endColumn": 14}, {"ruleId": "1938", "severity": 1, "message": "2415", "line": 108, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 108, "endColumn": 14}, {"ruleId": "1938", "severity": 1, "message": "2416", "line": 128, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 128, "endColumn": 16}, {"ruleId": "2091", "severity": 1, "message": "2113", "line": 268, "column": 45, "nodeType": "2093", "messageId": "2094", "endLine": 268, "endColumn": 47}, {"ruleId": "2091", "severity": 1, "message": "2113", "line": 268, "column": 104, "nodeType": "2093", "messageId": "2094", "endLine": 268, "endColumn": 106}, {"ruleId": "1938", "severity": 1, "message": "2754", "line": 1, "column": 17, "nodeType": "1940", "messageId": "1941", "endLine": 1, "endColumn": 26}, {"ruleId": "1938", "severity": 1, "message": "2457", "line": 4, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 4, "endColumn": 19}, {"ruleId": "1938", "severity": 1, "message": "2529", "line": 8, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 8, "endColumn": 11}, {"ruleId": "1938", "severity": 1, "message": "2755", "line": 15, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 15, "endColumn": 28}, {"ruleId": "1938", "severity": 1, "message": "2756", "line": 15, "column": 30, "nodeType": "1940", "messageId": "1941", "endLine": 15, "endColumn": 44}, {"ruleId": "1938", "severity": 1, "message": "2757", "line": 15, "column": 46, "nodeType": "1940", "messageId": "1941", "endLine": 15, "endColumn": 60}, {"ruleId": "1938", "severity": 1, "message": "2758", "line": 15, "column": 62, "nodeType": "1940", "messageId": "1941", "endLine": 15, "endColumn": 78}, {"ruleId": "1938", "severity": 1, "message": "2759", "line": 15, "column": 80, "nodeType": "1940", "messageId": "1941", "endLine": 15, "endColumn": 96}, {"ruleId": "1938", "severity": 1, "message": "2760", "line": 17, "column": 29, "nodeType": "1940", "messageId": "1941", "endLine": 17, "endColumn": 33}, {"ruleId": "1938", "severity": 1, "message": "2761", "line": 17, "column": 35, "nodeType": "1940", "messageId": "1941", "endLine": 17, "endColumn": 47}, {"ruleId": "1938", "severity": 1, "message": "2208", "line": 17, "column": 49, "nodeType": "1940", "messageId": "1941", "endLine": 17, "endColumn": 55}, {"ruleId": "1938", "severity": 1, "message": "2762", "line": 22, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 22, "endColumn": 11}, {"ruleId": "1938", "severity": 1, "message": "2763", "line": 58, "column": 13, "nodeType": "1940", "messageId": "1941", "endLine": 58, "endColumn": 22}, {"ruleId": "1938", "severity": 1, "message": "2466", "line": 65, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 65, "endColumn": 10}, {"ruleId": "1938", "severity": 1, "message": "2764", "line": 66, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 66, "endColumn": 25}, {"ruleId": "1938", "severity": 1, "message": "2469", "line": 72, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 72, "endColumn": 8}, {"ruleId": "1938", "severity": 1, "message": "2468", "line": 73, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 73, "endColumn": 15}, {"ruleId": "1938", "severity": 1, "message": "2688", "line": 74, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 74, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "2345", "line": 75, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 75, "endColumn": 14}, {"ruleId": "1938", "severity": 1, "message": "2765", "line": 76, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 76, "endColumn": 22}, {"ruleId": "1938", "severity": 1, "message": "2476", "line": 77, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 77, "endColumn": 20}, {"ruleId": "1938", "severity": 1, "message": "2766", "line": 78, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 78, "endColumn": 21}, {"ruleId": "1938", "severity": 1, "message": "2477", "line": 79, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 79, "endColumn": 18}, {"ruleId": "1938", "severity": 1, "message": "2472", "line": 80, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 80, "endColumn": 15}, {"ruleId": "1938", "severity": 1, "message": "2767", "line": 81, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 81, "endColumn": 17}, {"ruleId": "1938", "severity": 1, "message": "2690", "line": 82, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 82, "endColumn": 16}, {"ruleId": "1938", "severity": 1, "message": "2217", "line": 83, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 83, "endColumn": 19}, {"ruleId": "1938", "severity": 1, "message": "2212", "line": 84, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 84, "endColumn": 14}, {"ruleId": "1938", "severity": 1, "message": "2768", "line": 87, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 87, "endColumn": 15}, {"ruleId": "1938", "severity": 1, "message": "2769", "line": 89, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 89, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "2218", "line": 91, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 91, "endColumn": 14}, {"ruleId": "1938", "severity": 1, "message": "2223", "line": 93, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 93, "endColumn": 19}, {"ruleId": "1938", "severity": 1, "message": "2770", "line": 99, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 99, "endColumn": 19}, {"ruleId": "1938", "severity": 1, "message": "2771", "line": 99, "column": 21, "nodeType": "1940", "messageId": "1941", "endLine": 99, "endColumn": 33}, {"ruleId": "1938", "severity": 1, "message": "2772", "line": 103, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 103, "endColumn": 24}, {"ruleId": "1938", "severity": 1, "message": "2773", "line": 103, "column": 26, "nodeType": "1940", "messageId": "1941", "endLine": 103, "endColumn": 44}, {"ruleId": "1938", "severity": 1, "message": "2774", "line": 161, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 161, "endColumn": 27}, {"ruleId": "1938", "severity": 1, "message": "2775", "line": 164, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 164, "endColumn": 28}, {"ruleId": "1938", "severity": 1, "message": "2776", "line": 170, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 170, "endColumn": 23}, {"ruleId": "1938", "severity": 1, "message": "2777", "line": 173, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 173, "endColumn": 23}, {"ruleId": "1938", "severity": 1, "message": "2778", "line": 183, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 183, "endColumn": 21}, {"ruleId": "2042", "severity": 1, "message": "2779", "line": 339, "column": 5, "nodeType": "2044", "endLine": 339, "endColumn": 18, "suggestions": "2780"}, {"ruleId": "1938", "severity": 1, "message": "2781", "line": 341, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 341, "endColumn": 24}, {"ruleId": "1938", "severity": 1, "message": "2479", "line": 593, "column": 33, "nodeType": "1940", "messageId": "1941", "endLine": 593, "endColumn": 47}, {"ruleId": "1938", "severity": 1, "message": "2315", "line": 593, "column": 49, "nodeType": "1940", "messageId": "1941", "endLine": 593, "endColumn": 60}, {"ruleId": "1938", "severity": 1, "message": "2257", "line": 593, "column": 62, "nodeType": "1940", "messageId": "1941", "endLine": 593, "endColumn": 67}, {"ruleId": "1938", "severity": 1, "message": "2217", "line": 593, "column": 69, "nodeType": "1940", "messageId": "1941", "endLine": 593, "endColumn": 85}, {"ruleId": "1938", "severity": 1, "message": "2782", "line": 783, "column": 7, "nodeType": "1940", "messageId": "1941", "endLine": 783, "endColumn": 17}, {"ruleId": "2042", "severity": 1, "message": "2783", "line": 924, "column": 3, "nodeType": "2044", "endLine": 924, "endColumn": 19, "suggestions": "2784"}, {"ruleId": "1938", "severity": 1, "message": "2785", "line": 1023, "column": 15, "nodeType": "1940", "messageId": "1941", "endLine": 1023, "endColumn": 33}, {"ruleId": "1938", "severity": 1, "message": "2786", "line": 1032, "column": 15, "nodeType": "1940", "messageId": "1941", "endLine": 1032, "endColumn": 27}, {"ruleId": "1938", "severity": 1, "message": "2459", "line": 11, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 11, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "2401", "line": 14, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 14, "endColumn": 26}, {"ruleId": "1938", "severity": 1, "message": "2787", "line": 67, "column": 11, "nodeType": "1940", "messageId": "1941", "endLine": 67, "endColumn": 22}, {"ruleId": "1938", "severity": 1, "message": "2788", "line": 184, "column": 86, "nodeType": "1940", "messageId": "1941", "endLine": 184, "endColumn": 101}, {"ruleId": "1938", "severity": 1, "message": "2477", "line": 188, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 188, "endColumn": 24}, {"ruleId": "2042", "severity": 1, "message": "2789", "line": 567, "column": 5, "nodeType": "2044", "endLine": 567, "endColumn": 40, "suggestions": "2790"}, {"ruleId": "2042", "severity": 1, "message": "2791", "line": 590, "column": 6, "nodeType": "2044", "endLine": 590, "endColumn": 42, "suggestions": "2792"}, {"ruleId": "2042", "severity": 1, "message": "2793", "line": 604, "column": 6, "nodeType": "2044", "endLine": 604, "endColumn": 50, "suggestions": "2794"}, {"ruleId": "2042", "severity": 1, "message": "2795", "line": 881, "column": 5, "nodeType": "2044", "endLine": 881, "endColumn": 160, "suggestions": "2796"}, {"ruleId": "2042", "severity": 1, "message": "2797", "line": 949, "column": 5, "nodeType": "2044", "endLine": 949, "endColumn": 110, "suggestions": "2798"}, {"ruleId": "2042", "severity": 1, "message": "2799", "line": 979, "column": 5, "nodeType": "2044", "endLine": 979, "endColumn": 34, "suggestions": "2800"}, {"ruleId": "2042", "severity": 1, "message": "2801", "line": 997, "column": 5, "nodeType": "2044", "endLine": 997, "endColumn": 34, "suggestions": "2802"}, {"ruleId": "2042", "severity": 1, "message": "2801", "line": 1011, "column": 5, "nodeType": "2044", "endLine": 1011, "endColumn": 34, "suggestions": "2803"}, {"ruleId": "2042", "severity": 1, "message": "2801", "line": 1014, "column": 5, "nodeType": "2044", "endLine": 1014, "endColumn": 40, "suggestions": "2804"}, {"ruleId": "1938", "severity": 1, "message": "2805", "line": 1224, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 1224, "endColumn": 26}, {"ruleId": "1938", "severity": 1, "message": "2806", "line": 1227, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 1227, "endColumn": 21}, {"ruleId": "1938", "severity": 1, "message": "2330", "line": 2, "column": 64, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 71}, {"ruleId": "1938", "severity": 1, "message": "2592", "line": 2, "column": 73, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 79}, {"ruleId": "1938", "severity": 1, "message": "1989", "line": 15, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 15, "endColumn": 9}, {"ruleId": "1938", "severity": 1, "message": "2807", "line": 18, "column": 48, "nodeType": "1940", "messageId": "1941", "endLine": 18, "endColumn": 76}, {"ruleId": "1938", "severity": 1, "message": "2808", "line": 18, "column": 78, "nodeType": "1940", "messageId": "1941", "endLine": 18, "endColumn": 85}, {"ruleId": "1938", "severity": 1, "message": "2335", "line": 20, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 20, "endColumn": 23}, {"ruleId": "1938", "severity": 1, "message": "2809", "line": 52, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 52, "endColumn": 31}, {"ruleId": "1938", "severity": 1, "message": "2810", "line": 54, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 54, "endColumn": 31}, {"ruleId": "1938", "severity": 1, "message": "2811", "line": 59, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 59, "endColumn": 16}, {"ruleId": "1938", "severity": 1, "message": "2769", "line": 59, "column": 18, "nodeType": "1940", "messageId": "1941", "endLine": 59, "endColumn": 28}, {"ruleId": "1938", "severity": 1, "message": "2812", "line": 60, "column": 27, "nodeType": "1940", "messageId": "1941", "endLine": 60, "endColumn": 46}, {"ruleId": "1938", "severity": 1, "message": "2024", "line": 61, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 61, "endColumn": 14}, {"ruleId": "1938", "severity": 1, "message": "2025", "line": 61, "column": 16, "nodeType": "1940", "messageId": "1941", "endLine": 61, "endColumn": 24}, {"ruleId": "1938", "severity": 1, "message": "2746", "line": 85, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 85, "endColumn": 27}, {"ruleId": "1938", "severity": 1, "message": "2620", "line": 92, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 92, "endColumn": 25}, {"ruleId": "2042", "severity": 1, "message": "2813", "line": 183, "column": 5, "nodeType": "2044", "endLine": 183, "endColumn": 52, "suggestions": "2814"}, {"ruleId": "2042", "severity": 1, "message": "2097", "line": 183, "column": 6, "nodeType": "2146", "endLine": 183, "endColumn": 51}, {"ruleId": "1938", "severity": 1, "message": "2244", "line": 2, "column": 92, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 102}, {"ruleId": "1938", "severity": 1, "message": "2815", "line": 76, "column": 19, "nodeType": "1940", "messageId": "1941", "endLine": 76, "endColumn": 30}, {"ruleId": "1938", "severity": 1, "message": "2503", "line": 2, "column": 27, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 34}, {"ruleId": "1938", "severity": 1, "message": "2329", "line": 2, "column": 36, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 46}, {"ruleId": "1938", "severity": 1, "message": "2529", "line": 2, "column": 48, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 57}, {"ruleId": "1938", "severity": 1, "message": "2328", "line": 2, "column": 59, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 67}, {"ruleId": "1938", "severity": 1, "message": "2180", "line": 2, "column": 69, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 75}, {"ruleId": "1938", "severity": 1, "message": "2330", "line": 2, "column": 77, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 84}, {"ruleId": "1938", "severity": 1, "message": "2816", "line": 3, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 3, "endColumn": 18}, {"ruleId": "1938", "severity": 1, "message": "2817", "line": 4, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 4, "endColumn": 15}, {"ruleId": "1938", "severity": 1, "message": "2818", "line": 8, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 8, "endColumn": 29}, {"ruleId": "1938", "severity": 1, "message": "2819", "line": 9, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 9, "endColumn": 18}, {"ruleId": "1938", "severity": 1, "message": "2590", "line": 1, "column": 17, "nodeType": "1940", "messageId": "1941", "endLine": 1, "endColumn": 27}, {"ruleId": "1938", "severity": 1, "message": "2500", "line": 1, "column": 29, "nodeType": "1940", "messageId": "1941", "endLine": 1, "endColumn": 37}, {"ruleId": "1938", "severity": 1, "message": "2449", "line": 1, "column": 38, "nodeType": "1940", "messageId": "1941", "endLine": 1, "endColumn": 47}, {"ruleId": "1938", "severity": 1, "message": "2243", "line": 1, "column": 49, "nodeType": "1940", "messageId": "1941", "endLine": 1, "endColumn": 59}, {"ruleId": "1938", "severity": 1, "message": "2456", "line": 1, "column": 61, "nodeType": "1940", "messageId": "1941", "endLine": 1, "endColumn": 67}, {"ruleId": "1938", "severity": 1, "message": "2529", "line": 2, "column": 27, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 36}, {"ruleId": "1938", "severity": 1, "message": "2323", "line": 2, "column": 38, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 42}, {"ruleId": "1938", "severity": 1, "message": "2180", "line": 2, "column": 56, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 62}, {"ruleId": "1938", "severity": 1, "message": "1971", "line": 2, "column": 64, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 78}, {"ruleId": "1938", "severity": 1, "message": "2325", "line": 2, "column": 80, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 91}, {"ruleId": "1938", "severity": 1, "message": "2326", "line": 2, "column": 93, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 103}, {"ruleId": "1938", "severity": 1, "message": "2327", "line": 2, "column": 105, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 111}, {"ruleId": "1938", "severity": 1, "message": "2328", "line": 2, "column": 113, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 121}, {"ruleId": "1938", "severity": 1, "message": "2591", "line": 2, "column": 123, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 140}, {"ruleId": "1938", "severity": 1, "message": "2202", "line": 2, "column": 142, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 158}, {"ruleId": "1938", "severity": 1, "message": "2592", "line": 2, "column": 160, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 166}, {"ruleId": "1938", "severity": 1, "message": "2504", "line": 3, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 3, "endColumn": 17}, {"ruleId": "1938", "severity": 1, "message": "2273", "line": 4, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 4, "endColumn": 22}, {"ruleId": "1938", "severity": 1, "message": "2595", "line": 4, "column": 26, "nodeType": "1940", "messageId": "1941", "endLine": 4, "endColumn": 49}, {"ruleId": "1938", "severity": 1, "message": "2596", "line": 4, "column": 51, "nodeType": "1940", "messageId": "1941", "endLine": 4, "endColumn": 71}, {"ruleId": "1938", "severity": 1, "message": "2597", "line": 4, "column": 73, "nodeType": "1940", "messageId": "1941", "endLine": 4, "endColumn": 91}, {"ruleId": "1938", "severity": 1, "message": "2598", "line": 5, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 5, "endColumn": 31}, {"ruleId": "1938", "severity": 1, "message": "2599", "line": 7, "column": 5, "nodeType": "1940", "messageId": "1941", "endLine": 7, "endColumn": 15}, {"ruleId": "1938", "severity": 1, "message": "2600", "line": 8, "column": 5, "nodeType": "1940", "messageId": "1941", "endLine": 8, "endColumn": 17}, {"ruleId": "1938", "severity": 1, "message": "2601", "line": 9, "column": 5, "nodeType": "1940", "messageId": "1941", "endLine": 9, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "2602", "line": 10, "column": 7, "nodeType": "1940", "messageId": "1941", "endLine": 10, "endColumn": 12}, {"ruleId": "1938", "severity": 1, "message": "1969", "line": 11, "column": 7, "nodeType": "1940", "messageId": "1941", "endLine": 11, "endColumn": 15}, {"ruleId": "1938", "severity": 1, "message": "2634", "line": 12, "column": 7, "nodeType": "1940", "messageId": "1941", "endLine": 12, "endColumn": 17}, {"ruleId": "1938", "severity": 1, "message": "2820", "line": 19, "column": 7, "nodeType": "1940", "messageId": "1941", "endLine": 19, "endColumn": 21}, {"ruleId": "1938", "severity": 1, "message": "2590", "line": 1, "column": 17, "nodeType": "1940", "messageId": "1941", "endLine": 1, "endColumn": 27}, {"ruleId": "1938", "severity": 1, "message": "2323", "line": 2, "column": 38, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 42}, {"ruleId": "1938", "severity": 1, "message": "1971", "line": 2, "column": 64, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 78}, {"ruleId": "1938", "severity": 1, "message": "2326", "line": 2, "column": 93, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 103}, {"ruleId": "1938", "severity": 1, "message": "2591", "line": 2, "column": 123, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 140}, {"ruleId": "1938", "severity": 1, "message": "2202", "line": 2, "column": 142, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 158}, {"ruleId": "1938", "severity": 1, "message": "2592", "line": 2, "column": 160, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 166}, {"ruleId": "1938", "severity": 1, "message": "2205", "line": 2, "column": 177, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 193}, {"ruleId": "1938", "severity": 1, "message": "2595", "line": 4, "column": 26, "nodeType": "1940", "messageId": "1941", "endLine": 4, "endColumn": 49}, {"ruleId": "1938", "severity": 1, "message": "2596", "line": 4, "column": 51, "nodeType": "1940", "messageId": "1941", "endLine": 4, "endColumn": 71}, {"ruleId": "1938", "severity": 1, "message": "2597", "line": 4, "column": 73, "nodeType": "1940", "messageId": "1941", "endLine": 4, "endColumn": 91}, {"ruleId": "1938", "severity": 1, "message": "2598", "line": 5, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 5, "endColumn": 31}, {"ruleId": "1938", "severity": 1, "message": "2599", "line": 7, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 7, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "2600", "line": 8, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 8, "endColumn": 15}, {"ruleId": "1938", "severity": 1, "message": "2601", "line": 9, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 9, "endColumn": 11}, {"ruleId": "1938", "severity": 1, "message": "2602", "line": 10, "column": 5, "nodeType": "1940", "messageId": "1941", "endLine": 10, "endColumn": 10}, {"ruleId": "1938", "severity": 1, "message": "1969", "line": 11, "column": 5, "nodeType": "1940", "messageId": "1941", "endLine": 11, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "2604", "line": 27, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 27, "endColumn": 23}, {"ruleId": "1938", "severity": 1, "message": "2605", "line": 28, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 28, "endColumn": 18}, {"ruleId": "1938", "severity": 1, "message": "2606", "line": 29, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 29, "endColumn": 20}, {"ruleId": "1938", "severity": 1, "message": "2607", "line": 30, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 30, "endColumn": 23}, {"ruleId": "1938", "severity": 1, "message": "2610", "line": 38, "column": 4, "nodeType": "1940", "messageId": "1941", "endLine": 38, "endColumn": 14}, {"ruleId": "1938", "severity": 1, "message": "2611", "line": 39, "column": 4, "nodeType": "1940", "messageId": "1941", "endLine": 39, "endColumn": 17}, {"ruleId": "1938", "severity": 1, "message": "2612", "line": 41, "column": 4, "nodeType": "1940", "messageId": "1941", "endLine": 41, "endColumn": 14}, {"ruleId": "1938", "severity": 1, "message": "2639", "line": 42, "column": 4, "nodeType": "1940", "messageId": "1941", "endLine": 42, "endColumn": 17}, {"ruleId": "1938", "severity": 1, "message": "2640", "line": 43, "column": 4, "nodeType": "1940", "messageId": "1941", "endLine": 43, "endColumn": 20}, {"ruleId": "1938", "severity": 1, "message": "2821", "line": 44, "column": 4, "nodeType": "1940", "messageId": "1941", "endLine": 44, "endColumn": 23}, {"ruleId": "1938", "severity": 1, "message": "2641", "line": 45, "column": 4, "nodeType": "1940", "messageId": "1941", "endLine": 45, "endColumn": 24}, {"ruleId": "1938", "severity": 1, "message": "2643", "line": 47, "column": 4, "nodeType": "1940", "messageId": "1941", "endLine": 47, "endColumn": 30}, {"ruleId": "1938", "severity": 1, "message": "2644", "line": 48, "column": 4, "nodeType": "1940", "messageId": "1941", "endLine": 48, "endColumn": 33}, {"ruleId": "1938", "severity": 1, "message": "2645", "line": 49, "column": 4, "nodeType": "1940", "messageId": "1941", "endLine": 49, "endColumn": 23}, {"ruleId": "1938", "severity": 1, "message": "2647", "line": 51, "column": 4, "nodeType": "1940", "messageId": "1941", "endLine": 51, "endColumn": 30}, {"ruleId": "1938", "severity": 1, "message": "2648", "line": 52, "column": 4, "nodeType": "1940", "messageId": "1941", "endLine": 52, "endColumn": 27}, {"ruleId": "1938", "severity": 1, "message": "2649", "line": 53, "column": 4, "nodeType": "1940", "messageId": "1941", "endLine": 53, "endColumn": 21}, {"ruleId": "1938", "severity": 1, "message": "2650", "line": 54, "column": 4, "nodeType": "1940", "messageId": "1941", "endLine": 54, "endColumn": 24}, {"ruleId": "1938", "severity": 1, "message": "2518", "line": 58, "column": 4, "nodeType": "1940", "messageId": "1941", "endLine": 58, "endColumn": 20}, {"ruleId": "1938", "severity": 1, "message": "2822", "line": 152, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 152, "endColumn": 40}, {"ruleId": "1938", "severity": 1, "message": "2823", "line": 153, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 153, "endColumn": 41}, {"ruleId": "1938", "severity": 1, "message": "2824", "line": 154, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 154, "endColumn": 47}, {"ruleId": "1938", "severity": 1, "message": "2024", "line": 156, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 156, "endColumn": 15}, {"ruleId": "1938", "severity": 1, "message": "2825", "line": 185, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 185, "endColumn": 25}, {"ruleId": "1938", "severity": 1, "message": "2618", "line": 220, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 220, "endColumn": 26}, {"ruleId": "1938", "severity": 1, "message": "2619", "line": 223, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 223, "endColumn": 25}, {"ruleId": "1938", "severity": 1, "message": "2620", "line": 228, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 228, "endColumn": 26}, {"ruleId": "1938", "severity": 1, "message": "2130", "line": 245, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 245, "endColumn": 24}, {"ruleId": "1938", "severity": 1, "message": "2652", "line": 249, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 249, "endColumn": 22}, {"ruleId": "1938", "severity": 1, "message": "2658", "line": 254, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 254, "endColumn": 20}, {"ruleId": "2042", "severity": 1, "message": "2826", "line": 263, "column": 6, "nodeType": "2044", "endLine": 263, "endColumn": 8, "suggestions": "2827"}, {"ruleId": "1938", "severity": 1, "message": "2828", "line": 298, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 298, "endColumn": 25}, {"ruleId": "1938", "severity": 1, "message": "2590", "line": 1, "column": 17, "nodeType": "1940", "messageId": "1941", "endLine": 1, "endColumn": 27}, {"ruleId": "1938", "severity": 1, "message": "2456", "line": 1, "column": 49, "nodeType": "1940", "messageId": "1941", "endLine": 1, "endColumn": 55}, {"ruleId": "1938", "severity": 1, "message": "2829", "line": 1, "column": 69, "nodeType": "1940", "messageId": "1941", "endLine": 1, "endColumn": 80}, {"ruleId": "1938", "severity": 1, "message": "2323", "line": 2, "column": 38, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 42}, {"ruleId": "1938", "severity": 1, "message": "2326", "line": 2, "column": 93, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 103}, {"ruleId": "1938", "severity": 1, "message": "2591", "line": 2, "column": 123, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 140}, {"ruleId": "1938", "severity": 1, "message": "2202", "line": 2, "column": 142, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 158}, {"ruleId": "1938", "severity": 1, "message": "2592", "line": 2, "column": 160, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 166}, {"ruleId": "1938", "severity": 1, "message": "2457", "line": 2, "column": 195, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 212}, {"ruleId": "1938", "severity": 1, "message": "2595", "line": 5, "column": 26, "nodeType": "1940", "messageId": "1941", "endLine": 5, "endColumn": 49}, {"ruleId": "1938", "severity": 1, "message": "2596", "line": 5, "column": 51, "nodeType": "1940", "messageId": "1941", "endLine": 5, "endColumn": 71}, {"ruleId": "1938", "severity": 1, "message": "2597", "line": 5, "column": 73, "nodeType": "1940", "messageId": "1941", "endLine": 5, "endColumn": 91}, {"ruleId": "1938", "severity": 1, "message": "2598", "line": 6, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 6, "endColumn": 31}, {"ruleId": "1938", "severity": 1, "message": "2603", "line": 8, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 8, "endColumn": 28}, {"ruleId": "1938", "severity": 1, "message": "2604", "line": 9, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 9, "endColumn": 23}, {"ruleId": "1938", "severity": 1, "message": "2605", "line": 10, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 10, "endColumn": 18}, {"ruleId": "1938", "severity": 1, "message": "2606", "line": 11, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 11, "endColumn": 20}, {"ruleId": "1938", "severity": 1, "message": "2607", "line": 12, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 12, "endColumn": 23}, {"ruleId": "1938", "severity": 1, "message": "2608", "line": 13, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 13, "endColumn": 24}, {"ruleId": "1938", "severity": 1, "message": "2599", "line": 15, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 15, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "2600", "line": 16, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 16, "endColumn": 15}, {"ruleId": "1938", "severity": 1, "message": "2601", "line": 17, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 17, "endColumn": 11}, {"ruleId": "1938", "severity": 1, "message": "2602", "line": 18, "column": 5, "nodeType": "1940", "messageId": "1941", "endLine": 18, "endColumn": 10}, {"ruleId": "1938", "severity": 1, "message": "1969", "line": 19, "column": 5, "nodeType": "1940", "messageId": "1941", "endLine": 19, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "2817", "line": 34, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 34, "endColumn": 15}, {"ruleId": "1938", "severity": 1, "message": "2830", "line": 44, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 44, "endColumn": 29}, {"ruleId": "1938", "severity": 1, "message": "2610", "line": 45, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 45, "endColumn": 19}, {"ruleId": "1938", "severity": 1, "message": "2611", "line": 46, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 46, "endColumn": 22}, {"ruleId": "1938", "severity": 1, "message": "2612", "line": 48, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 48, "endColumn": 19}, {"ruleId": "1938", "severity": 1, "message": "2639", "line": 49, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 49, "endColumn": 22}, {"ruleId": "1938", "severity": 1, "message": "2640", "line": 50, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 50, "endColumn": 25}, {"ruleId": "1938", "severity": 1, "message": "2821", "line": 51, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 51, "endColumn": 28}, {"ruleId": "1938", "severity": 1, "message": "2641", "line": 52, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 52, "endColumn": 29}, {"ruleId": "1938", "severity": 1, "message": "2643", "line": 54, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 54, "endColumn": 35}, {"ruleId": "1938", "severity": 1, "message": "2644", "line": 55, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 55, "endColumn": 38}, {"ruleId": "1938", "severity": 1, "message": "2645", "line": 56, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 56, "endColumn": 28}, {"ruleId": "1938", "severity": 1, "message": "2647", "line": 58, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 58, "endColumn": 35}, {"ruleId": "1938", "severity": 1, "message": "2648", "line": 59, "column": 6, "nodeType": "1940", "messageId": "1941", "endLine": 59, "endColumn": 29}, {"ruleId": "1938", "severity": 1, "message": "2649", "line": 60, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 60, "endColumn": 26}, {"ruleId": "1938", "severity": 1, "message": "2650", "line": 61, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 61, "endColumn": 29}, {"ruleId": "1938", "severity": 1, "message": "2831", "line": 63, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 63, "endColumn": 29}, {"ruleId": "1938", "severity": 1, "message": "2518", "line": 66, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 66, "endColumn": 19}, {"ruleId": "1938", "severity": 1, "message": "2822", "line": 89, "column": 11, "nodeType": "1940", "messageId": "1941", "endLine": 89, "endColumn": 42}, {"ruleId": "1938", "severity": 1, "message": "2823", "line": 90, "column": 11, "nodeType": "1940", "messageId": "1941", "endLine": 90, "endColumn": 43}, {"ruleId": "1938", "severity": 1, "message": "2824", "line": 91, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 91, "endColumn": 46}, {"ruleId": "2042", "severity": 1, "message": "2832", "line": 112, "column": 5, "nodeType": "2044", "endLine": 112, "endColumn": 52, "suggestions": "2833"}, {"ruleId": "1938", "severity": 1, "message": "2618", "line": 117, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 117, "endColumn": 26}, {"ruleId": "1938", "severity": 1, "message": "2619", "line": 120, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 120, "endColumn": 25}, {"ruleId": "1938", "severity": 1, "message": "2620", "line": 125, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 125, "endColumn": 26}, {"ruleId": "1938", "severity": 1, "message": "2834", "line": 135, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 135, "endColumn": 21}, {"ruleId": "1938", "severity": 1, "message": "2130", "line": 175, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 175, "endColumn": 23}, {"ruleId": "1938", "severity": 1, "message": "2835", "line": 183, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 183, "endColumn": 20}, {"ruleId": "2042", "severity": 1, "message": "2826", "line": 188, "column": 4, "nodeType": "2044", "endLine": 188, "endColumn": 6, "suggestions": "2836"}, {"ruleId": "2091", "severity": 1, "message": "2113", "line": 193, "column": 19, "nodeType": "2093", "messageId": "2094", "endLine": 193, "endColumn": 21}, {"ruleId": "2091", "severity": 1, "message": "2113", "line": 194, "column": 19, "nodeType": "2093", "messageId": "2094", "endLine": 194, "endColumn": 21}, {"ruleId": "2091", "severity": 1, "message": "2092", "line": 194, "column": 39, "nodeType": "2093", "messageId": "2094", "endLine": 194, "endColumn": 41}, {"ruleId": "2091", "severity": 1, "message": "2092", "line": 213, "column": 19, "nodeType": "2093", "messageId": "2094", "endLine": 213, "endColumn": 21}, {"ruleId": "2091", "severity": 1, "message": "2113", "line": 226, "column": 20, "nodeType": "2093", "messageId": "2094", "endLine": 226, "endColumn": 22}, {"ruleId": "1938", "severity": 1, "message": "2837", "line": 279, "column": 7, "nodeType": "1940", "messageId": "1941", "endLine": 279, "endColumn": 19}, {"ruleId": "1938", "severity": 1, "message": "2024", "line": 307, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 307, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "2825", "line": 334, "column": 7, "nodeType": "1940", "messageId": "1941", "endLine": 334, "endColumn": 23}, {"ruleId": "2042", "severity": 1, "message": "2838", "line": 371, "column": 4, "nodeType": "2044", "endLine": 371, "endColumn": 6, "suggestions": "2839"}, {"ruleId": "1938", "severity": 1, "message": "2242", "line": 2, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "2529", "line": 2, "column": 27, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 36}, {"ruleId": "1938", "severity": 1, "message": "2323", "line": 2, "column": 38, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 42}, {"ruleId": "1938", "severity": 1, "message": "2345", "line": 9, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 9, "endColumn": 20}, {"ruleId": "1938", "severity": 1, "message": "2344", "line": 9, "column": 22, "nodeType": "1940", "messageId": "1941", "endLine": 9, "endColumn": 36}, {"ruleId": "1938", "severity": 1, "message": "2048", "line": 10, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 10, "endColumn": 24}, {"ruleId": "1938", "severity": 1, "message": "2346", "line": 10, "column": 26, "nodeType": "1940", "messageId": "1941", "endLine": 10, "endColumn": 44}, {"ruleId": "1938", "severity": 1, "message": "2732", "line": 12, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 12, "endColumn": 25}, {"ruleId": "1938", "severity": 1, "message": "2812", "line": 12, "column": 27, "nodeType": "1940", "messageId": "1941", "endLine": 12, "endColumn": 46}, {"ruleId": "1938", "severity": 1, "message": "2733", "line": 13, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 13, "endColumn": 12}, {"ruleId": "1938", "severity": 1, "message": "2734", "line": 13, "column": 14, "nodeType": "1940", "messageId": "1941", "endLine": 13, "endColumn": 20}, {"ruleId": "1938", "severity": 1, "message": "2429", "line": 15, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 15, "endColumn": 21}, {"ruleId": "1938", "severity": 1, "message": "2668", "line": 16, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 16, "endColumn": 15}, {"ruleId": "1938", "severity": 1, "message": "2840", "line": 28, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 28, "endColumn": 25}, {"ruleId": "1938", "severity": 1, "message": "2841", "line": 3, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 3, "endColumn": 18}, {"ruleId": "1938", "severity": 1, "message": "2603", "line": 6, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 6, "endColumn": 28}, {"ruleId": "1938", "severity": 1, "message": "2244", "line": 2, "column": 32, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 42}, {"ruleId": "1938", "severity": 1, "message": "2529", "line": 2, "column": 44, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 53}, {"ruleId": "1938", "severity": 1, "message": "2538", "line": 4, "column": 46, "nodeType": "1940", "messageId": "1941", "endLine": 4, "endColumn": 65}, {"ruleId": "1938", "severity": 1, "message": "1969", "line": 4, "column": 67, "nodeType": "1940", "messageId": "1941", "endLine": 4, "endColumn": 75}, {"ruleId": "1938", "severity": 1, "message": "2842", "line": 8, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 8, "endColumn": 23}, {"ruleId": "1938", "severity": 1, "message": "2763", "line": 16, "column": 13, "nodeType": "1940", "messageId": "1941", "endLine": 16, "endColumn": 22}, {"ruleId": "1938", "severity": 1, "message": "2843", "line": 31, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 31, "endColumn": 23}, {"ruleId": "1938", "severity": 1, "message": "2359", "line": 33, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 33, "endColumn": 20}, {"ruleId": "1938", "severity": 1, "message": "2844", "line": 43, "column": 6, "nodeType": "1940", "messageId": "1941", "endLine": 43, "endColumn": 34}, {"ruleId": "1938", "severity": 1, "message": "2845", "line": 85, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 85, "endColumn": 28}, {"ruleId": "1938", "severity": 1, "message": "2559", "line": 95, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 95, "endColumn": 26}, {"ruleId": "1938", "severity": 1, "message": "2818", "line": 5, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 5, "endColumn": 29}, {"ruleId": "1938", "severity": 1, "message": "2819", "line": 6, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 6, "endColumn": 18}, {"ruleId": "1938", "severity": 1, "message": "2846", "line": 7, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 7, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "2847", "line": 27, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 27, "endColumn": 14}, {"ruleId": "1938", "severity": 1, "message": "2848", "line": 34, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 34, "endColumn": 21}, {"ruleId": "1938", "severity": 1, "message": "2527", "line": 56, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 56, "endColumn": 21}, {"ruleId": "1938", "severity": 1, "message": "2528", "line": 58, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 58, "endColumn": 21}, {"ruleId": "1938", "severity": 1, "message": "2849", "line": 80, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 80, "endColumn": 22}, {"ruleId": "1938", "severity": 1, "message": "2850", "line": 82, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 82, "endColumn": 21}, {"ruleId": "1938", "severity": 1, "message": "2851", "line": 82, "column": 23, "nodeType": "1940", "messageId": "1941", "endLine": 82, "endColumn": 38}, {"ruleId": "1938", "severity": 1, "message": "2852", "line": 133, "column": 7, "nodeType": "1940", "messageId": "1941", "endLine": 133, "endColumn": 16}, {"ruleId": "1938", "severity": 1, "message": "2853", "line": 290, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 290, "endColumn": 28}, {"ruleId": "2042", "severity": 1, "message": "2854", "line": 344, "column": 5, "nodeType": "2044", "endLine": 344, "endColumn": 22, "suggestions": "2855"}, {"ruleId": "1938", "severity": 1, "message": "2754", "line": 1, "column": 58, "nodeType": "1940", "messageId": "1941", "endLine": 1, "endColumn": 67}, {"ruleId": "1938", "severity": 1, "message": "2503", "line": 2, "column": 15, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 22}, {"ruleId": "1938", "severity": 1, "message": "2244", "line": 2, "column": 24, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 34}, {"ruleId": "1938", "severity": 1, "message": "2330", "line": 2, "column": 48, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 55}, {"ruleId": "1938", "severity": 1, "message": "2762", "line": 5, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 5, "endColumn": 11}, {"ruleId": "1938", "severity": 1, "message": "2199", "line": 6, "column": 41, "nodeType": "1940", "messageId": "1941", "endLine": 6, "endColumn": 53}, {"ruleId": "1938", "severity": 1, "message": "2760", "line": 7, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 7, "endColumn": 14}, {"ruleId": "1938", "severity": 1, "message": "2208", "line": 7, "column": 16, "nodeType": "1940", "messageId": "1941", "endLine": 7, "endColumn": 22}, {"ruleId": "1938", "severity": 1, "message": "2856", "line": 7, "column": 24, "nodeType": "1940", "messageId": "1941", "endLine": 7, "endColumn": 29}, {"ruleId": "1938", "severity": 1, "message": "2857", "line": 7, "column": 31, "nodeType": "1940", "messageId": "1941", "endLine": 7, "endColumn": 35}, {"ruleId": "1938", "severity": 1, "message": "2381", "line": 7, "column": 37, "nodeType": "1940", "messageId": "1941", "endLine": 7, "endColumn": 47}, {"ruleId": "1938", "severity": 1, "message": "2761", "line": 7, "column": 49, "nodeType": "1940", "messageId": "1941", "endLine": 7, "endColumn": 61}, {"ruleId": "1938", "severity": 1, "message": "2504", "line": 8, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 8, "endColumn": 17}, {"ruleId": "1938", "severity": 1, "message": "2817", "line": 9, "column": 8, "nodeType": "1940", "messageId": "1941", "endLine": 9, "endColumn": 15}, {"ruleId": "1938", "severity": 1, "message": "2634", "line": 10, "column": 20, "nodeType": "1940", "messageId": "1941", "endLine": 10, "endColumn": 30}, {"ruleId": "1938", "severity": 1, "message": "2858", "line": 46, "column": 4, "nodeType": "1940", "messageId": "1941", "endLine": 46, "endColumn": 23}, {"ruleId": "1938", "severity": 1, "message": "2859", "line": 47, "column": 4, "nodeType": "1940", "messageId": "1941", "endLine": 47, "endColumn": 18}, {"ruleId": "1938", "severity": 1, "message": "2055", "line": 48, "column": 4, "nodeType": "1940", "messageId": "1941", "endLine": 48, "endColumn": 15}, {"ruleId": "1938", "severity": 1, "message": "2860", "line": 49, "column": 4, "nodeType": "1940", "messageId": "1941", "endLine": 49, "endColumn": 18}, {"ruleId": "1938", "severity": 1, "message": "2388", "line": 50, "column": 4, "nodeType": "1940", "messageId": "1941", "endLine": 50, "endColumn": 15}, {"ruleId": "1938", "severity": 1, "message": "2861", "line": 51, "column": 4, "nodeType": "1940", "messageId": "1941", "endLine": 51, "endColumn": 18}, {"ruleId": "1938", "severity": 1, "message": "2387", "line": 52, "column": 4, "nodeType": "1940", "messageId": "1941", "endLine": 52, "endColumn": 16}, {"ruleId": "1938", "severity": 1, "message": "2386", "line": 53, "column": 4, "nodeType": "1940", "messageId": "1941", "endLine": 53, "endColumn": 15}, {"ruleId": "1938", "severity": 1, "message": "2862", "line": 54, "column": 4, "nodeType": "1940", "messageId": "1941", "endLine": 54, "endColumn": 12}, {"ruleId": "1938", "severity": 1, "message": "2384", "line": 56, "column": 4, "nodeType": "1940", "messageId": "1941", "endLine": 56, "endColumn": 19}, {"ruleId": "1938", "severity": 1, "message": "2863", "line": 57, "column": 4, "nodeType": "1940", "messageId": "1941", "endLine": 57, "endColumn": 24}, {"ruleId": "1938", "severity": 1, "message": "2466", "line": 61, "column": 4, "nodeType": "1940", "messageId": "1941", "endLine": 61, "endColumn": 11}, {"ruleId": "1938", "severity": 1, "message": "2531", "line": 75, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 75, "endColumn": 22}, {"ruleId": "2042", "severity": 1, "message": "2864", "line": 171, "column": 7, "nodeType": "2044", "endLine": 171, "endColumn": 27, "suggestions": "2865"}, {"ruleId": "2042", "severity": 1, "message": "2866", "line": 193, "column": 9, "nodeType": "2300", "endLine": 198, "endColumn": 4, "suggestions": "2867"}, {"ruleId": "2042", "severity": 1, "message": "2868", "line": 275, "column": 6, "nodeType": "2044", "endLine": 275, "endColumn": 19, "suggestions": "2869"}, {"ruleId": "2042", "severity": 1, "message": "2870", "line": 349, "column": 6, "nodeType": "2044", "endLine": 349, "endColumn": 17, "suggestions": "2871"}, {"ruleId": "1938", "severity": 1, "message": "2204", "line": 2, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 14}, {"ruleId": "1938", "severity": 1, "message": "2872", "line": 20, "column": 24, "nodeType": "1940", "messageId": "1941", "endLine": 20, "endColumn": 35}, {"ruleId": "1938", "severity": 1, "message": "2356", "line": 42, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 42, "endColumn": 21}, {"ruleId": "1938", "severity": 1, "message": "2461", "line": 43, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 43, "endColumn": 23}, {"ruleId": "1938", "severity": 1, "message": "2220", "line": 44, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 44, "endColumn": 28}, {"ruleId": "1938", "severity": 1, "message": "2873", "line": 46, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 46, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "2316", "line": 48, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 48, "endColumn": 17}, {"ruleId": "1938", "severity": 1, "message": "2359", "line": 49, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 49, "endColumn": 20}, {"ruleId": "1938", "severity": 1, "message": "2340", "line": 61, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 61, "endColumn": 27}, {"ruleId": "1938", "severity": 1, "message": "2341", "line": 61, "column": 45, "nodeType": "1940", "messageId": "1941", "endLine": 61, "endColumn": 62}, {"ruleId": "1938", "severity": 1, "message": "2874", "line": 101, "column": 11, "nodeType": "1940", "messageId": "1941", "endLine": 101, "endColumn": 27}, {"ruleId": "1938", "severity": 1, "message": "2875", "line": 104, "column": 12, "nodeType": "1940", "messageId": "1941", "endLine": 104, "endColumn": 26}, {"ruleId": "2042", "severity": 1, "message": "2564", "line": 129, "column": 5, "nodeType": "2044", "endLine": 129, "endColumn": 155, "suggestions": "2876"}, {"ruleId": "1938", "severity": 1, "message": "2511", "line": 1, "column": 27, "nodeType": "1940", "messageId": "1941", "endLine": 1, "endColumn": 41}, {"ruleId": "1938", "severity": 1, "message": "2877", "line": 22, "column": 7, "nodeType": "1940", "messageId": "1941", "endLine": 22, "endColumn": 23}, {"ruleId": "1938", "severity": 1, "message": "2878", "line": 31, "column": 7, "nodeType": "1940", "messageId": "1941", "endLine": 31, "endColumn": 19}, {"ruleId": "1938", "severity": 1, "message": "2879", "line": 40, "column": 7, "nodeType": "1940", "messageId": "1941", "endLine": 40, "endColumn": 25}, {"ruleId": "1938", "severity": 1, "message": "2880", "line": 5, "column": 3, "nodeType": "1940", "messageId": "1941", "endLine": 5, "endColumn": 24}, {"ruleId": "2042", "severity": 1, "message": "2881", "line": 121, "column": 6, "nodeType": "2044", "endLine": 121, "endColumn": 26, "suggestions": "2882"}, {"ruleId": "1938", "severity": 1, "message": "2883", "line": 39, "column": 9, "nodeType": "1940", "messageId": "1941", "endLine": 39, "endColumn": 14}, {"ruleId": "1938", "severity": 1, "message": "2243", "line": 1, "column": 17, "nodeType": "1940", "messageId": "1941", "endLine": 1, "endColumn": 27}, {"ruleId": "1938", "severity": 1, "message": "2500", "line": 1, "column": 28, "nodeType": "1940", "messageId": "1941", "endLine": 1, "endColumn": 36}, {"ruleId": "1938", "severity": 1, "message": "1949", "line": 2, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 2, "endColumn": 22}, {"ruleId": "1938", "severity": 1, "message": "2247", "line": 4, "column": 10, "nodeType": "1940", "messageId": "1941", "endLine": 4, "endColumn": 24}, {"ruleId": "1938", "severity": 1, "message": "2715", "line": 6, "column": 5, "nodeType": "1940", "messageId": "1941", "endLine": 6, "endColumn": 11}, {"ruleId": "1938", "severity": 1, "message": "2716", "line": 7, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 7, "endColumn": 15}, {"ruleId": "1938", "severity": 1, "message": "1971", "line": 8, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 8, "endColumn": 16}, {"ruleId": "1938", "severity": 1, "message": "2884", "line": 9, "column": 2, "nodeType": "1940", "messageId": "1941", "endLine": 9, "endColumn": 19}, {"ruleId": "1938", "severity": 1, "message": "2242", "line": 11, "column": 5, "nodeType": "1940", "messageId": "1941", "endLine": 11, "endColumn": 8}, {"ruleId": "1938", "severity": 1, "message": "2324", "line": 13, "column": 5, "nodeType": "1940", "messageId": "1941", "endLine": 13, "endColumn": 14}, {"ruleId": "1938", "severity": 1, "message": "2206", "line": 15, "column": 5, "nodeType": "1940", "messageId": "1941", "endLine": 15, "endColumn": 16}, {"ruleId": "1938", "severity": 1, "message": "2207", "line": 16, "column": 5, "nodeType": "1940", "messageId": "1941", "endLine": 16, "endColumn": 18}, {"ruleId": "1938", "severity": 1, "message": "2326", "line": 18, "column": 5, "nodeType": "1940", "messageId": "1941", "endLine": 18, "endColumn": 15}, {"ruleId": "1938", "severity": 1, "message": "2327", "line": 19, "column": 5, "nodeType": "1940", "messageId": "1941", "endLine": 19, "endColumn": 11}, {"ruleId": "1938", "severity": 1, "message": "2328", "line": 20, "column": 5, "nodeType": "1940", "messageId": "1941", "endLine": 20, "endColumn": 13}, {"ruleId": "1938", "severity": 1, "message": "2329", "line": 21, "column": 5, "nodeType": "1940", "messageId": "1941", "endLine": 21, "endColumn": 15}, {"ruleId": "1938", "severity": 1, "message": "2330", "line": 22, "column": 5, "nodeType": "1940", "messageId": "1941", "endLine": 22, "endColumn": 12}, {"ruleId": "1938", "severity": 1, "message": "2331", "line": 23, "column": 5, "nodeType": "1940", "messageId": "1941", "endLine": 23, "endColumn": 10}, {"ruleId": "1938", "severity": 1, "message": "2332", "line": 24, "column": 5, "nodeType": "1940", "messageId": "1941", "endLine": 24, "endColumn": 9}, {"ruleId": "1938", "severity": 1, "message": "2885", "line": 25, "column": 5, "nodeType": "1940", "messageId": "1941", "endLine": 25, "endColumn": 21}, "@typescript-eslint/no-unused-vars", "'GuidePopup' is defined but never used.", "Identifier", "unusedVar", "'Rte' is defined but never used.", "'isAppReady' is assigned a value but never used.", "'LoginUserInfo' is defined but never used.", "'useMemo' is defined but never used.", "'Steps' is defined but never used.", "'PopupList' is defined but never used.", "'BUTTON_DEFAULT_VALUE' is defined but never used.", "'stopScraping' is defined but never used.", "'addicon' is defined but never used.", "'touricon' is defined but never used.", "'ProductToursicon' is defined but never used.", "'Tooltipsicon' is defined but never used.", "'announcementicon' is defined but never used.", "'Bannersicon' is defined but never used.", "'Checklisticon' is defined but never used.", "'Hotspoticon' is defined but never used.", "'Surveyicon' is defined but never used.", "'Announcementsicon' is defined but never used.", "'bannersicon' is defined but never used.", "'tooltipicon' is defined but never used.", "'checklisticon' is defined but never used.", "'hotspotsicon' is defined but never used.", "'surveysicon' is defined but never used.", "'settingsicon' is defined but never used.", "'undoicon' is defined but never used.", "'redoicon' is defined but never used.", "'shareicon' is defined but never used.", "'editicon' is defined but never used.", "'Outlet' is defined but never used.", "'InputAdornment' is defined but never used.", "'FormHelperText' is defined but never used.", "'List' is defined but never used.", "'Step' is defined but never used.", "'guideSetting' is defined but never used.", "'JSEncrypt' is defined but never used.", "'GetUserDetailsById' is defined but never used.", "'UserLogin' is defined but never used.", "'VisibilityOff' is defined but never used.", "'Visibility' is defined but never used.", "'initialsData' is defined but never used.", "'EditIcon' is defined but never used.", "'TooltipUserview' is defined but never used.", "'SubmitUpdateGuid' is defined but never used.", "'PageInteractions' is defined but never used.", "'ElementsSettings' is defined but never used.", "'DrawerState' is defined but never used.", "'Checklist' is defined but never used.", "'Padding' is defined but never used.", "'CheckIcon' is defined but never used.", "'TooltipPreview' is defined but never used.", "'DismissData' is defined but never used.", "'Canvas' is defined but never used.", "'Design' is defined but never used.", "'Advanced' is defined but never used.", "'Hotspot' is defined but never used.", "'stepId' is defined but never used.", "'userId' is defined but never used.", "'loginUserData' is defined but never used.", "'setIsGuidesListOpen' is assigned a value but never used.", "'setIsInHomeScreen' is assigned a value but never used.", "'setIsAnnouncementListOpen' is assigned a value but never used.", "'setIsBannerslistOpen' is assigned a value but never used.", "'selectedTemplated' is assigned a value but never used.", "'setSelectedTemplated' is assigned a value but never used.", "'errorInStepName' is assigned a value but never used.", "'showTextField' is assigned a value but never used.", "'signOut' is assigned a value but never used.", "'selectedElement' is assigned a value but never used.", "'setSelectedElement' is assigned a value but never used.", "'showPassword' is assigned a value but never used.", "'setShowPassword' is assigned a value but never used.", "'password' is assigned a value but never used.", "'setPassword' is assigned a value but never used.", "'loginUserInfo' is assigned a value but never used.", "'setLoginUserInfo' is assigned a value but never used.", "'setresponse' is assigned a value but never used.", "'isTooltipPopupOpen' is assigned a value but never used.", "'setIsTooltipPopupOpen' is assigned a value but never used.", "'email' is assigned a value but never used.", "'setEmail' is assigned a value but never used.", "'loginUserDetails' is assigned a value but never used.", "'setUserDetails' is assigned a value but never used.", "'error' is assigned a value but never used.", "'setError' is assigned a value but never used.", "'isSelectingElement' is assigned a value but never used.", "'selectedElementDetails' is assigned a value but never used.", "'setSelectedElementDetails' is assigned a value but never used.", "'position' is assigned a value but never used.", "'setPosition' is assigned a value but never used.", "'radius' is assigned a value but never used.", "'setRadius' is assigned a value but never used.", "'borderSize' is assigned a value but never used.", "'setBorderSize' is assigned a value but never used.", "'announcementData' is assigned a value but never used.", "'currentUrl' is assigned a value but never used.", "'isBannerPopupOpen' is assigned a value but never used.", "'i18nInitialized' is assigned a value but never used.", "'setI18nInitialized' is assigned a value but never used.", "'hashValue' is assigned a value but never used.", "'setHashValue' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has an unnecessary dependency: 'window.location.href'. Either exclude it or remove the dependency array. Outer scope values like 'window.location.href' aren't valid dependencies because mutating them doesn't re-render the component.", "ArrayExpression", ["2886"], "'fit' is assigned a value but never used.", "'fill' is assigned a value but never used.", "'backgroundColor' is assigned a value but never used.", "'sectionHeight' is assigned a value but never used.", "'setSectionHeight' is assigned a value but never used.", "'guidedatas' is assigned a value but never used.", "'setGuideDataS' is assigned a value but never used.", "'hotspotPopup' is assigned a value but never used.", "'setHotspotPopup' is assigned a value but never used.", "'textvaluess' is assigned a value but never used.", "'preview' is assigned a value but never used.", "'btnBorderColor' is assigned a value but never used.", "'btnBgColor' is assigned a value but never used.", "'btnTextColor' is assigned a value but never used.", "'isTooltipPopup' is assigned a value but never used.", "'setSteps' is assigned a value but never used.", "'newCurrentStep' is assigned a value but never used.", "'updateCanvasInTooltip' is assigned a value but never used.", "'hotspbgcolor' is assigned a value but never used.", "'setHotspBgColor' is assigned a value but never used.", "'setHotspotDataOnEdit' is assigned a value but never used.", "'openTooltip' is assigned a value but never used.", "'setXpathToTooltipMetaData' is assigned a value but never used.", "'setAxisData' is assigned a value but never used.", "'axisData' is assigned a value but never used.", "'setAutoPosition' is assigned a value but never used.", "'targetURL' is assigned a value but never used.", "'elementButtonName' is assigned a value but never used.", "'setElementButtonName' is assigned a value but never used.", "'isSaveClicked' is assigned a value but never used.", "'setbtnidss' is assigned a value but never used.", "'setPulseAnimationsH' is assigned a value but never used.", "'tooltipCount' is assigned a value but never used.", "'HotspotGuideDetails' is assigned a value but never used.", "'TooltipGuideDetailsNew' is assigned a value but never used.", "'editClicked' is assigned a value but never used.", "'textArray' is assigned a value but never used.", "'setTextArray' is assigned a value but never used.", "'setDrawerActiveMenu' is assigned a value but never used.", "'setDrawerSearchText' is assigned a value but never used.", "'setInteractionData' is assigned a value but never used.", "'syncAIAnnouncementCanvasSettings' is assigned a value but never used.", "'ele4' is assigned a value but never used.", "'targetElement' is assigned a value but never used.", "'setHotspotClicked' is assigned a value but never used.", "eqeqeq", "Expected '!==' and instead saw '!='.", "BinaryExpression", "unexpected", "React Hook useEffect has missing dependencies: 'fetchGuideDetails' and 'hotspot'. Either include them or remove the dependency array.", ["2887"], "React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.", "MemberExpression", "React Hook useEffect has a missing dependency: 'setDesignPopup'. Either include it or remove the dependency array.", ["2888"], "'screenWidth' is assigned a value but never used.", "'dialogWidth' is assigned a value but never used.", "'handlechangeStep' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'determineCurrentScreen'. Either include it or remove the dependency array.", ["2889"], "React Hook useEffect has a missing dependency: 'errors'. Either include it or remove the dependency array. Outer scope values like 'selectedStepType' aren't valid dependencies because mutating them doesn't re-render the component.", ["2890"], "React Hook useEffect has a missing dependency: 'setIsAIGuidePersisted'. Either include it or remove the dependency array.", ["2891"], "React Hook useEffect has missing dependencies: 'bannerPopup', 'clearBannerButtonDetials', 'currentGuideId', 'currentStep', 'selectedTemplate', 'selectedTemplateTour', 'setBannerButtonSelected', 'setBannerPopup', 'setIsAnnouncementCreationBuilderOpen', 'setIsBannerCreationBuilderOpen', 'setIsHotspotCreationBuilderOpen', 'setIsTooltipCreationBuilderOpen', 'updateButtonContainerOnReload', and 'updateRTEContainerOnReload'. Either include them or remove the dependency array.", ["2892"], "'setCount' is assigned a value but never used.", "Expected '===' and instead saw '=='.", "'handleGuidesSettingsclick' is assigned a value but never used.", "'synchronizePreviewData' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'currentStep', 'elementSelected', 'resetALTKeywordForNewTooltip', 'setElementSelected', 'setIsALTKeywordEnabled', and 'toolTipGuideMetaData'. Either include them or remove the dependency array.", ["2893"], "'handleElementSelectionToggle' is assigned a value but never used.", "'isAnnouncementOpen' is assigned a value but never used.", "'setAnnouncementOpen' is assigned a value but never used.", "'aiCreationComplete' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setSettingAnchorEl'. Either include it or remove the dependency array.", ["2894"], "'defaultButtonSection' is assigned a value but never used.", "'responseData' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'setIsUnSavedChanges' and 'stepCreation'. Either include them or remove the dependency array.", ["2895"], "'handleNewInteractionClick' is assigned a value but never used.", "Assignments to the 'accountId' variable from inside React Hook useEffect will be lost after each render. To preserve the value over time, store it in a useRef Hook and keep the mutable value in the '.current' property. Otherwise, you can move this variable directly inside useEffect.", "'handleEditClick' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'userType'. Either include it or remove the dependency array.", ["2896"], "'editstepNameClicked' is assigned a value but never used.", "'setEditStepNameClicked' is assigned a value but never used.", "'handleNextClick' is assigned a value but never used.", "'isValid' is assigned a value but never used.", "'handleEventChange' is assigned a value but never used.", "'isGuideNameUnique' is assigned a value but never used.", "React Hook useEffect has an unnecessary dependency: 'updatedGuideData.GuideStep'. Either exclude it or remove the dependency array. Outer scope values like 'updatedGuideData.GuideStep' aren't valid dependencies because mutating them doesn't re-render the component.", ["2897"], "'getAlignment' is defined but never used.", "'popupVisible' is assigned a value but never used.", "'triggerType' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'currentGuide?.GuideStep'. Either include it or remove the dependency array.", ["2898"], "ChainExpression", "'customButton' is assigned a value but never used.", "'groupedButtons' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'cleanupDuplicateSteps', 'createWithAI', 'currentGuideId', 'interactionData', and 'resetHeightofBanner'. Either include them or remove the dependency array.", ["2899"], "'isDisabled' is assigned a value but never used.", "'guideType' is assigned a value but never used.", "'guideSteps' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'SetGuideName', 'cleanupDuplicateSteps', 'createWithAI', 'currentStep', 'getGuideTypeValue', 'handlecreatefromScratchclick', 'overlayEnabled', 'pageinteraction', 'progress', 'resetHeightofBanner', 'setBannerPopup', 'setBposition', 'setDismiss', 'setIsGuideInfoScreen', 'setOverlayEnabled', 'setPageInteraction', 'setProgress', 'setProgressColor', 'setSelectedOption', 'setSelectedTemplate', 'setSelectedTemplateTour', 'setTooltipCount', and 'setTourDataOnEdit'. Either include them or remove the dependency array.", ["2900"], "React Hook useEffect has missing dependencies: 'SetGuideName', 'currentGuideId', 'currentStep', 'getGuideTypeValue', 'handlecreatefromScratchclick', 'selectedTemplate', 'setIsGuideInfoScreen', 'setSelectedTemplate', and 'steps'. Either include them or remove the dependency array.", ["2901"], "React Hook useEffect has missing dependencies: 'setBannerPopup', 'setCreateWithAI', 'setCurrentGuideId', 'setIsAIGuidePersisted', 'setIsAnnouncementCreationBuilderOpen', 'setIsBannerCreationBuilderOpen', 'setIsGuideInfoScreen', 'setIsHomeScreen', 'setIsHotspotCreationBuilderOpen', 'setIsTemplateScreen', 'setIsTooltipCreationBuilderOpen', and 'setIsTooltipPopup'. Either include them or remove the dependency array.", ["2902"], "React Hook useEffect has an unnecessary dependency: 'updatedGuideData'. Either exclude it or remove the dependency array. Outer scope values like 'updatedGuideData' aren't valid dependencies because mutating them doesn't re-render the component.", ["2903"], "'getAccountIdForUpdate' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'currentStep', 'deleteClicked', 'handleStepChange', and 'updateStepClicked'. Either include them or remove the dependency array.", ["2904"], "no-useless-escape", "Unnecessary escape character: \\/.", "Literal", "unnecessaryEscape", ["2905", "2906"], "'selectedStepTitle' is assigned a value but never used.", "'UserManager' is defined but never used.", "'useNavigate' is defined but never used.", "'useLocation' is defined but never used.", "'redirectPath' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loggedOut'. Either include it or remove the dependency array.", ["2907"], "'signIn' is assigned a value but never used.", "'CelebrationOutlinedIcon' is defined but never used.", "'ErrorOutlineOutlinedIcon' is defined but never used.", "'Button' is defined but never used.", "'Routes' is defined but never used.", "'RouteSharp' is defined but never used.", "'extractStateForHistory' is defined but never used.", "no-dupe-keys", "Duplicate key 'hotspotXaxis'.", "ObjectExpression", "Duplicate key 'setHotspotXaxis'.", "Duplicate key 'setSelectedTemplate'.", "no-self-assign", "'state.overlayEnabled' is assigned to itself.", "selfAssignment", "Duplicate key 'toolTipGuideMetaData'.", "'isTourBanner' is assigned a value but never used.", "Duplicate key 'announcementGuideMetaData'.", "'opt' is assigned a value but never used.", "'targetStep' is assigned a value but never used.", "'future' is assigned a value but never used.", "'FALSE' is defined but never used.", "'TSectionType' is defined but never used.", "'RadioGroup' is defined but never used.", "'Radio' is defined but never used.", "'FormControlLabel' is defined but never used.", "'Input' is defined but never used.", "'Autocomplete' is defined but never used.", "'CircularProgress' is defined but never used.", "'DialogTitle' is defined but never used.", "'DialogActions' is defined but never used.", "'GifBox' is defined but never used.", "'Opacity' is defined but never used.", "'WarningIcon' is defined but never used.", "'color' is defined but never used.", "'dismissData' is assigned a value but never used.", "'setSelectActions' is assigned a value but never used.", "'setSelectedInteraction' is assigned a value but never used.", "'openInteractionList' is assigned a value but never used.", "'loading' is assigned a value but never used.", "'currentStepIndex' is assigned a value but never used.", "'setProgress' is assigned a value but never used.", "'selectedTemplate' is assigned a value but never used.", "'updateTooltipButtonAction' is assigned a value but never used.", "'updateTooltipButtonInteraction' is assigned a value but never used.", "'selectedTemplateTour' is assigned a value but never used.", "'setProgressColor' is assigned a value but never used.", "'createWithAI' is assigned a value but never used.", "'action' is assigned a value but never used.", "'designPopup' is assigned a value but never used.", "'buttonId' is assigned a value but never used.", "'setButtonId' is assigned a value but never used.", "'cuntainerId' is assigned a value but never used.", "'setCuntainerId' is assigned a value but never used.", "'btnname' is assigned a value but never used.", "'handleCloseInteraction' is assigned a value but never used.", "'handleOpenInteraction' is assigned a value but never used.", "'sideAddButtonStyle' is assigned a value but never used.", "React Hook useMemo has missing dependencies: 'getCurrentButtonInfo', 'setBtnName', and 'setCurrentButtonName'. Either include them or remove the dependency array.", ["2908"], "'selectedButton' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'defaultButtonColors.backgroundColor', 'defaultButtonColors.borderColor', 'defaultButtonColors.color', 'getCurrentButtonInfo', 'setTargetURL', and 'targetURL'. Either include them or remove the dependency array.", ["2909"], "React Hook useEffect has missing dependencies: 'selectedActions.value' and 'targetURL'. Either include them or remove the dependency array. You can also replace multiple useState variables with useReducer if 'setSelectedActions' needs the current value of 'selectedActions.value'.", ["2910"], "'Box' is defined but never used.", "'useContext' is defined but never used.", "'Typography' is defined but never used.", "'AuthProvider' is defined but never used.", "'useAuth' is defined but never used.", "'AccountContext' is defined but never used.", "'clearAccessToken' is assigned a value but never used.", "'userLocalData' is assigned a value but never used.", "'SAinitialsData' is assigned a value but never used.", "'userDetails' is defined but never used.", "'ai' is defined but never used.", "'EnableAIButton' is defined but never used.", "'IsOpenAIKeyEnabledForAccount' is defined but never used.", "'setSelectedTemplate' is assigned a value but never used.", "'setSelectedTemplateTour' is assigned a value but never used.", "'steps' is assigned a value but never used.", "'setTooltipCount' is assigned a value but never used.", "'SetGuideName' is assigned a value but never used.", "'setIsTooltipPopup' is assigned a value but never used.", "'setBannerPopup' is assigned a value but never used.", "'setElementSelected' is assigned a value but never used.", "'TooltipGuideDetails' is assigned a value but never used.", "'HotspotGuideDetailsNew' is assigned a value but never used.", "'setSelectedStepTypeHotspot' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'isExtensionClosed' and 'setIsExtensionClosed'. Either include them or remove the dependency array.", ["2911"], "React Hook useEffect has missing dependencies: 'setDrawerActiveMenu', 'setDrawerSearchText', and 'setHasAnnouncementOpened'. Either include them or remove the dependency array.", ["2912"], "React Hook useEffect has missing dependencies: 'setDrawerActiveMenu', 'setDrawerSearchText', and 'setIsPopupOpen'. Either include them or remove the dependency array. If 'setIsPopupOpen' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2913"], "'handleEnableAI' is assigned a value but never used.", "'useDrawerStore' is defined but never used.", "'constants' is defined but never used.", "'addPersistentHighlight' is assigned a value but never used.", "'showClickFeedback' is assigned a value but never used.", ["2914", "2915"], "Unnecessary escape character: \\..", ["2916", "2917"], ["2918", "2919"], ["2920", "2921"], ["2922", "2923"], ["2924", "2925"], ["2926", "2927"], ["2928", "2929"], "'response' is assigned a value but never used.", "'errorMessage' is assigned a value but never used.", "'axios' is defined but never used.", "'AnyMxRecord' is defined but never used.", "'ChecklistPopup' is defined but never used.", "'closeicon' is defined but never used.", "'closepluginicon' is defined but never used.", "'setShowLauncherSettings' is assigned a value but never used.", "'showLauncherSettings' is assigned a value but never used.", "'setIcons' is assigned a value but never used.", "'checklistColor' is assigned a value but never used.", "'GetGudeDetailsByGuideId' is defined but never used.", "'initialCompletedStatus' is assigned a value but never used.", "The 'checkpointslistData' logical expression could make the dependencies of useEffect Hook (at line 143) change on every render. To fix this, wrap the initialization of 'checkpointslistData' in its own useMemo() Hook.", "VariableDeclarator", "The 'checkpointslistData' logical expression could make the dependencies of useEffect Hook (at line 207) change on every render. To fix this, wrap the initialization of 'checkpointslistData' in its own useMemo() Hook.", "'checklistItems' is assigned a value but never used.", "'setChecklistItems' is assigned a value but never used.", "no-lone-blocks", "Nested block is redundant.", "BlockStatement", "redundantNestedBlock", "React Hook useEffect has a missing dependency: 'checkpointslistData'. Either include it or remove the dependency array.", ["2930"], "'iconColor' is assigned a value but never used.", "'base64IconFinal' is assigned a value but never used.", "'handleNavigate' is assigned a value but never used.", "'anchorEl' is assigned a value but never used.", "'setAnchorEl' is assigned a value but never used.", "'currentStep' is assigned a value but never used.", "'setCurrentStep' is assigned a value but never used.", "'scrollPercentage' is assigned a value but never used.", "'setScrollPercentage' is assigned a value but never used.", "'UndoIcon' is defined but never used.", "'RedoIcon' is defined but never used.", "'canUndoValue' is assigned a value but never used.", "'canRedoValue' is assigned a value but never used.", "'Grid' is defined but never used.", "'Container' is defined but never used.", "'FormControl' is defined but never used.", "'InputLabel' is defined but never used.", "'Select' is defined but never used.", "'MenuItem' is defined but never used.", "'IconButton' is defined but never used.", "'Tooltip' is defined but never used.", "'Alert' is defined but never used.", "'Chip' is defined but never used.", "'ViewModuleIcon' is defined but never used.", "'CodeIcon' is defined but never used.", "'TouchAppSharp' is defined but never used.", "'reselectElement' is assigned a value but never used.", "'setReselectElement' is assigned a value but never used.", "'goToNextElement' is assigned a value but never used.", "'setGoToNextElement' is assigned a value but never used.", "'setCurrentGuideId' is assigned a value but never used.", "'getCurrentGuideId' is assigned a value but never used.", "'padding' is assigned a value but never used.", "'setPadding' is assigned a value but never used.", "'setBorderColor' is assigned a value but never used.", "'borderColor' is assigned a value but never used.", "'setBackgroundColor' is assigned a value but never used.", "'overlayEnabled' is assigned a value but never used.", "'setZiindex' is assigned a value but never used.", "'setguidesSettingspopup' is assigned a value but never used.", "'setTooltipBackgroundcolor' is assigned a value but never used.", "'setTooltipBordercolor' is assigned a value but never used.", "'setTooltipBordersize' is assigned a value but never used.", "'CANVAS_DEFAULT_VALUE' is assigned a value but never used.", "'savedGuideData' is assigned a value but never used.", "'ButtonsDropdown' is assigned a value but never used.", "'setButtonsDropdown' is assigned a value but never used.", "'elementSelected' is assigned a value but never used.", "'elementbuttonClick' is assigned a value but never used.", "'highlightedButton' is assigned a value but never used.", "'mapButtonSection' is assigned a value but never used.", "'progress' is assigned a value but never used.", "'setSelectedOption' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'setHotspotPopup', 'setShowLauncherSettings', 'setShowTooltipCanvasSettings', and 'setTitlePopup'. Either include them or remove the dependency array.", ["2931"], "'toggleReselectElement' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'SetElementButtonClick', 'createWithAI', 'currentGuideId', 'interactionData', 'setButtonClick', 'setDropdownValue', 'setElementButtonName', 'setElementClick', and 'setbtnidss'. Either include them or remove the dependency array.", ["2932"], "'existingHotspot' is assigned a value but never used.", "'existingTooltip' is assigned a value but never used.", "'toggleCustomCSS' is assigned a value but never used.", "'toggleAnimation' is assigned a value but never used.", "'handleDismissDataChange' is assigned a value but never used.", "'setTooltipXaxis' is defined but never used.", "'setTooltipYaxis' is defined but never used.", "'setTooltipPosition' is defined but never used.", "'setTooltipBorderradius' is defined but never used.", "'setTooltipPadding' is defined but never used.", "'setTooltipWidth' is defined but never used.", "'updateCanvasInTooltip' is defined but never used.", "'setElementSelected' is defined but never used.", "'TextFormat' is defined but never used.", "'BUTTON_CONT_DEF_VALUE' is defined but never used.", "'saveGuide' is defined but never used.", "'setSectionColor' is assigned a value but never used.", "'setButtonProperty' is assigned a value but never used.", "'BborderSize' is assigned a value but never used.", "'Bbordercolor' is assigned a value but never used.", "'backgroundC' is assigned a value but never used.", "'setPreview' is assigned a value but never used.", "'clearGuideDetails' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'bannerButtonSelected', 'buttonColor', 'rtesContainer', 'setButtonColor', 'textAreas', and 'textBoxRef'. Either include them or remove the dependency array.", ["2933"], "React Hook useEffect has missing dependencies: 'buttonColor', 'removeTextArea', 'setButtonColor', and 'textAreas'. Either include them or remove the dependency array.", ["2934"], "React Hook useEffect has a missing dependency: 'setTextArray'. Either include it or remove the dependency array.", ["2935"], "React Hook useEffect has a missing dependency: 'textAreas'. Either include it or remove the dependency array.", ["2936"], "'setShowEmojiPicker' is assigned a value but never used.", "'enableProgress' is assigned a value but never used.", "'CustomIconButton' is defined but never used.", "'ArrowBackIosNewOutlinedIcon' is defined but never used.", "'parse' is defined but never used.", "'domToReact' is defined but never used.", "'Element' is defined but never used.", "'IconButtonSX' is defined but never used.", "'setShowBanner' is assigned a value but never used.", "'setImageSrc' is assigned a value but never used.", "'htmlContent' is assigned a value but never used.", "'Teext' is assigned a value but never used.", "'IconColor' is assigned a value but never used.", "'IconOpacity' is assigned a value but never used.", "'Width' is assigned a value but never used.", "'Radius' is assigned a value but never used.", "'Design' is assigned a value but never used.", "'brCount' is assigned a value but never used.", "'BannerEndUser' is defined but never used.", "'BannerStepPreview' is defined but never used.", "'setBannerPreview' is assigned a value but never used.", "'bannerPreview' is assigned a value but never used.", "'announcementPreview' is assigned a value but never used.", "'setAnnouncementPreview' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'OverlayValue'. Either include it or remove the dependency array. If 'setOverlayValue' needs the current value of 'OverlayValue', you can also switch to useReducer instead of useState and read 'OverlayValue' in the reducer.", ["2937"], "'imageStyle' is assigned a value but never used.", "'dissmissIconColor' is assigned a value but never used.", "'ActionButtonBackgroundcolor' is assigned a value but never used.", "'overlay' is assigned a value but never used.", "'openInNewTab' is assigned a value but never used.", "'HotspotGuideProps' is defined but never used.", "'hotspotGuideMetaData' is assigned a value but never used.", "valid-typeof", "Invalid typeof comparison value.", "invalidV<PERSON>ue", ["2938"], "React Hook useEffect has a missing dependency: 'getElementPosition'. Either include it or remove the dependency array.", ["2939"], "React Hook useEffect has a missing dependency: 'xpath'. Either include it or remove the dependency array.", ["2940"], "React Hook useEffect has a missing dependency: 'calculateOptimalWidth'. Either include it or remove the dependency array.", ["2941"], "React Hook useEffect has a missing dependency: 'guideStep'. Either include it or remove the dependency array.", ["2942"], "'hotspotData' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'currentStep', 'savedGuideData?.GuideStep', 'selectedTemplateTour', and 'setOpenTooltip'. Either include them or remove the dependency array.", ["2943"], ["2944"], "Assignments to the 'hotspot' variable from inside React Hook useEffect will be lost after each render. To preserve the value over time, store it in a useRef Hook and keep the mutable value in the '.current' property. Otherwise, you can move this variable directly inside useEffect.", "'useEffect' is defined but never used.", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'count' is assigned a value but never used.", "'selectedStepStyle' is assigned a value but never used.", "'isSelected' is assigned a value but never used.", "'isHovered' is assigned a value but never used.", "'useRef' is defined but never used.", "'ClickAwayListener' is defined but never used.", "'LinearProgress' is defined but never used.", "'Breadcrumbs' is defined but never used.", "'updateCacheWithNewRows' is defined but never used.", "'toolTipGuideMetaData' is assigned a value but never used.", "no-unreachable", "Unreachable code.", "ReturnStatement", "unreachableCode", "'tooltip' is assigned a value but never used.", "'guideName' is assigned a value but never used.", "'borderRadius' is assigned a value but never used.", "'width' is assigned a value but never used.", "'tooltipXaxis' is assigned a value but never used.", "'tooltipYaxis' is assigned a value but never used.", "'tooltipWidth' is assigned a value but never used.", "'setTooltipWidth' is assigned a value but never used.", "'setTooltipPadding' is assigned a value but never used.", "'setTooltipBorderradius' is assigned a value but never used.", "'tooltipbordersize' is assigned a value but never used.", "'tooltipPosition' is assigned a value but never used.", "'setTooltipPosition' is assigned a value but never used.", "'selectedOption' is assigned a value but never used.", "'setCurrentStepIndex' is assigned a value but never used.", "'HotspotSettings' is assigned a value but never used.", "'currentGuideId' is assigned a value but never used.", "'setIsALTKeywordEnabled' is assigned a value but never used.", "'hoveredElement' is assigned a value but never used.", "'setHoveredElement' is assigned a value but never used.", "'overlayPosition' is assigned a value but never used.", "'setOverlayPosition' is assigned a value but never used.", "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'currentElement'.", "ArrowFunctionExpression", "unsafeRefs", "'removeAppliedStyleOfEle' is assigned a value but never used.", "'isElementHover' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'applyCustomCursor', 'applyHotspotProperties', 'createWithAI', 'isCollapsed', 'isGuideInfoScreen', 'isTooltipNameScreenOpen', 'rectData', 'selectedTemplate', 'selectedTemplateTour', 'setAxisData', 'setCurrentHoveredElement', 'setElementSelected', 'setOpenTooltip', 'setTooltip', 'setXpathToTooltipMetaData', and 'syncAITooltipContainerData'. Either include them or remove the dependency array.", ["2945"], "React Hook useEffect has a missing dependency: 'applyCustomCursor'. Either include it or remove the dependency array.", ["2946"], "'normalizePx' is assigned a value but never used.", "'DotsStepper' is assigned a value but never used.", "'useState' is defined but never used.", "'ForkLeft' is defined but never used.", "'handleStepTypeChange' is assigned a value but never used.", "'Popover' is defined but never used.", "'CloseIcon' is defined but never used.", "'PopoverOrigin' is defined but never used.", "React Hook useEffect has missing dependencies: 'initializeTourHotspotMetadata', 'savedGuideData?.GuideStep', 'setAnnouncementPreview', 'setBannerPreview', 'setHotspotPreview', 'setOpenTooltip', and 'setTooltipPreview'. Either include them or remove the dependency array.", ["2947"], "'setCurrentUrl' is assigned a value but never used.", "Assignments to the 'savedGuideData' variable from inside React Hook useEffect will be lost after each render. To preserve the value over time, store it in a useRef Hook and keep the mutable value in the '.current' property. Otherwise, you can move this variable directly inside useEffect.", "CallExpression", "'userApiService' is defined but never used.", "'AnySoaRecord' is defined but never used.", "'userUrl' is assigned a value but never used.", "'AxiosResponse' is defined but never used.", "'adminApiService' is defined but never used.", "'idsApiService' is defined but never used.", "'ArrowBackIosIcon' is defined but never used.", "'isUnSavedChanges' is assigned a value but never used.", "'openWarning' is assigned a value but never used.", "'setActiveMenu' is assigned a value but never used.", "'setSearchText' is assigned a value but never used.", "'setName' is assigned a value but never used.", "'handleKeyDown' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAnnouncements'. Either include it or remove the dependency array.", ["2948"], ["2949"], "'snackbarKey' is assigned a value but never used.", "'openSnackbar' is assigned a value but never used.", "'TextField' is defined but never used.", "'selectedtemp' is defined but never used.", "'contentState' is assigned a value but never used.", "React Hook React.useCallback has a missing dependency: 'isContentScrollable'. Either include it or remove the dependency array.", ["2950"], "'toggleToolbar' is assigned a value but never used.", "React Hook useMemo has a missing dependency: 'handlePaste'. Either include it or remove the dependency array.", ["2951"], "The 'containersToRender' array makes the dependencies of useEffect Hook (at line 437) change on every render. To fix this, wrap the initialization of 'containersToRender' in its own useMemo() Hook.", "'backgroundcoloricon' is defined but never used.", "'ButtonSettings' is defined but never used.", "'buttonProperty' is assigned a value but never used.", "'isEditingPrevious' is assigned a value but never used.", "'isEditingContinue' is assigned a value but never used.", "'previousButtonText' is assigned a value but never used.", "'continueButtonText' is assigned a value but never used.", "'buttonText' is assigned a value but never used.", "'setButtonText' is assigned a value but never used.", "'buttonToEdit' is assigned a value but never used.", "'isDeleteIcon' is assigned a value but never used.", "'isEditingButton' is assigned a value but never used.", "'isEditing' is assigned a value but never used.", "'setIsEditing' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setButtonProperty'. Either include it or remove the dependency array.", ["2952"], "'handlePreviousTextChange' is assigned a value but never used.", "'handleContinueTextChange' is assigned a value but never used.", "'toggleEdit' is assigned a value but never used.", "'handlePreviousBlur' is assigned a value but never used.", "'handleContinueBlur' is assigned a value but never used.", "'handleChangeButton' is assigned a value but never used.", "'handleEditButtonText' is assigned a value but never used.", "'LauncherSettings' is defined but never used.", "React Hook useEffect has missing dependencies: 'checkpointslistData' and 'completedStatus'. Either include them or remove the dependency array.", ["2953"], "React Hook useEffect has missing dependencies: 'createWithAI' and 'interactionData'. Either include them or remove the dependency array.", ["2954"], ["2955"], "React Hook useEffect has a missing dependency: 'checklistGuideMetaData'. Either include it or remove the dependency array.", ["2956"], "'toggleItemCompletion' is assigned a value but never used.", "'beta' is defined but never used.", "'setIsCollapsed' is assigned a value but never used.", "'accountId' is assigned a value but never used.", "'handleClick' is assigned a value but never used.", "'micicon' is defined but never used.", "'micicon_hover' is defined but never used.", "'PerfectScrollbar' is defined but never used.", "'isChatOpen' is assigned a value but never used.", "'setIsChatOpen' is assigned a value but never used.", "'isMicHovered' is assigned a value but never used.", "'setIsMicHovered' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'accountId' and 'openSnackbar'. Either include them or remove the dependency array.", ["2957"], "'handleSpeechRecognition' is assigned a value but never used.", "'isTourCreationPrompt' is assigned a value but never used.", "'parseTourSteps' is assigned a value but never used.", "'dataNew' is assigned a value but never used.", "'stepDataNew' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setElementSelected'. Either include it or remove the dependency array.", ["2958"], "'useReducer' is defined but never used.", "'SelectChangeEvent' is defined but never used.", "'Switch' is defined but never used.", "'ToggleButton' is defined but never used.", "'ToggleButtonGroup' is defined but never used.", "'BUTTON_CONT_DEF_VALUE_1' is defined but never used.", "'CANVAS_DEFAULT_VALUE' is defined but never used.", "'IMG_CONT_DEF_VALUE' is defined but never used.", "'HOTSPOT_DEFAULT_VALUE' is defined but never used.", "'InfoFilled' is defined but never used.", "'QuestionFill' is defined but never used.", "'Reselect' is defined but never used.", "'Solid' is defined but never used.", "'AddCircleOutlineIcon' is defined but never used.", "'InsertPhotoIcon' is defined but never used.", "'PersonIcon' is defined but never used.", "'FavoriteIcon' is defined but never used.", "'CheckCircleIcon' is defined but never used.", "'ErrorOutlineIcon' is defined but never used.", "'position' is defined but never used.", "'titlePopup' is assigned a value but never used.", "'setTitlePopup' is assigned a value but never used.", "'titleColor' is assigned a value but never used.", "'launcherColor' is assigned a value but never used.", "'hasChanges' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'checklistGuideMetaData', 'checklistLauncherProperties', and 'icons'. Either include them or remove the dependency array.", ["2959"], "'handleTitleColorChange' is assigned a value but never used.", "'handledesignclose' is assigned a value but never used.", "'handleSizeChange' is assigned a value but never used.", "'onReselectElement' is assigned a value but never used.", "'handleIconColorChange' is assigned a value but never used.", "'handleLauncherColorChange' is assigned a value but never used.", "'type' is assigned a value but never used.", "'text' is assigned a value but never used.", "'setText' is assigned a value but never used.", "'textColor' is assigned a value but never used.", "'setTextColor' is assigned a value but never used.", "'icon' is assigned a value but never used.", "'appliedIconColorBase64Icon' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'checklistLauncherProperties', 'icons', and 'updateChecklistLauncher'. Either include them or remove the dependency array.", ["2960"], "'setPositionLeft' is assigned a value but never used.", "'setSetPositionLeft' is assigned a value but never used.", "'deleteicon' is defined but never used.", "'deletestep' is defined but never used.", "'editpricol' is defined but never used.", "'getAllGuides' is defined but never used.", "'ShowLauncherSettings' is assigned a value but never used.", "'setTitleColor' is assigned a value but never used.", "'checkpointsPopup' is assigned a value but never used.", "'checkpointTitleColor' is assigned a value but never used.", "'setCheckpointTitleColor' is assigned a value but never used.", "'checkpointTitleDescription' is assigned a value but never used.", "'setCheckpointTitleDescription' is assigned a value but never used.", "'checkpointIconColor' is assigned a value but never used.", "'setCheckpointIconColor' is assigned a value but never used.", "'setUnlockCheckPointInOrder' is assigned a value but never used.", "'unlockCheckPointInOrder' is assigned a value but never used.", "'checkPointMessage' is assigned a value but never used.", "'setCheckPointMessage' is assigned a value but never used.", ["2961"], "'interactions' is assigned a value but never used.", "'setInteractions' is assigned a value but never used.", "'skip' is assigned a value but never used.", "'setSkip' is assigned a value but never used.", "'top' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", "'dropdownRef' is assigned a value but never used.", "'RadioButtonUncheckedIcon' is defined but never used.", "'RadioButtonCheckedIcon' is defined but never used.", "'topCenter' is defined but never used.", "'OverlaySettingsProps' is defined but never used.", "'ElementsSettingsProps' is defined but never used.", "'setTooltipElementOptions' is assigned a value but never used.", "'updateprogressclick' is assigned a value but never used.", "'displayType' is assigned a value but never used.", "'dontShowAgain' is assigned a value but never used.", "'colors' is assigned a value but never used.", "'handleDisplayTypeChange' is assigned a value but never used.", "'handleBorderColorChange' is assigned a value but never used.", "'handleDontShowAgainChange' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'dismissData.Color' and 'setDismiss'. Either include them or remove the dependency array.", ["2962"], "'defaultDots' is defined but never used.", "'topLeft' is defined but never used.", "'topRight' is defined but never used.", "'middleLeft' is defined but never used.", "'middleCenter' is defined but never used.", "'middleRight' is defined but never used.", "'bottomLeft' is defined but never used.", "'bottomMiddle' is defined but never used.", "'bottomRight' is defined but never used.", "'topcenter' is defined but never used.", "'setCanvasSetting' is assigned a value but never used.", "'announcement<PERSON>son' is assigned a value but never used.", "'setWidth' is assigned a value but never used.", "'setBorderRadius' is assigned a value but never used.", "'Annpadding' is assigned a value but never used.", "'setAnnPadding' is assigned a value but never used.", "'AnnborderSize' is assigned a value but never used.", "'setAnnBorderSize' is assigned a value but never used.", "'Bposition' is assigned a value but never used.", "'setBposition' is assigned a value but never used.", "'handleBackgroundColorChange' is assigned a value but never used.", "'checklistTitle' is assigned a value but never used.", "'setChecklistTitle' is assigned a value but never used.", "'checklistSubTitle' is assigned a value but never used.", "'setChecklistSubTitle' is assigned a value but never used.", "'setTempTitle' is assigned a value but never used.", "'settempTempTitle' is assigned a value but never used.", "'handleBlur' is assigned a value but never used.", "'setZindeex' is assigned a value but never used.", "'setOverlayEnabled' is assigned a value but never used.", "'handlePositionChange' is assigned a value but never used.", "'tempBorderSize' is assigned a value but never used.", "'setTempBorderSize' is assigned a value but never used.", "'tempZIndex' is assigned a value but never used.", "'setTempZIndex' is assigned a value but never used.", "'tempBorderColor' is assigned a value but never used.", "'setTempBorderColor' is assigned a value but never used.", "'tempBackgroundColor' is assigned a value but never used.", "'setTempBackgroundColor' is assigned a value but never used.", "'tempSectionColor' is assigned a value but never used.", "'setTempSectionColor' is assigned a value but never used.", "'Dialog' is defined but never used.", "'DialogContent' is defined but never used.", "'useMediaQuery' is defined but never used.", "'useTheme' is defined but never used.", "'zIndex' is defined but never used.", "'buttonsContainer' is assigned a value but never used.", "'cloneButtonContainer' is assigned a value but never used.", "'addNewButton' is assigned a value but never used.", "'deleteButton' is assigned a value but never used.", "'deleteButtonContainer' is assigned a value but never used.", "'updateContainer' is assigned a value but never used.", "'updateButtonInteraction' is assigned a value but never used.", "'setBtnBgColor' is assigned a value but never used.", "'setBtnTextColor' is assigned a value but never used.", "'setBtnBorderColor' is assigned a value but never used.", "'setBtnName' is assigned a value but never used.", "'setIsOpen' is assigned a value but never used.", "'selectedPosition' is assigned a value but never used.", "'url' is assigned a value but never used.", "'setUrl' is assigned a value but never used.", "'setAction' is assigned a value but never used.", "'setOpenInNewTab' is assigned a value but never used.", "'setColors' is assigned a value but never used.", "'buttonNameError' is assigned a value but never used.", "'setButtonNameError' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'defaultButtonColors.backgroundColor', 'defaultButtonColors.borderColor', 'defaultButtonColors.color', 'getCurrentButtonInfo', 'setCurrentButtonName', 'setSelectedTab', and 'setTargetURL'. Either include them or remove the dependency array.", ["2963"], "'positions' is assigned a value but never used.", "'curronButtonInfo' is assigned a value but never used.", "React Hook useMemo has missing dependencies: 'getCurrentButtonInfo' and 'setCurrentButtonName'. Either include them or remove the dependency array.", ["2964"], "'handlePositionClick' is assigned a value but never used.", "'imageContainerStyle' is assigned a value but never used.", "'iconRowStyle' is assigned a value but never used.", "'iconTextStyle' is assigned a value but never used.", "'setOpenTooltip' is assigned a value but never used.", "'setTooltipPositionByXpath' is assigned a value but never used.", "'updateTooltipBtnContainer' is assigned a value but never used.", "'updateTooltipImageContainer' is assigned a value but never used.", "'RefObject' is defined but never used.", "'CustomWidthTooltip' is defined but never used.", "'EXTENSION_PART' is defined but never used.", "'TOOLTIP_HEIGHT' is defined but never used.", "'TOOLTIP_MN_WIDTH' is defined but never used.", "'TOOLTIP_MX_WIDTH' is defined but never used.", "'Code' is defined but never used.", "'VideoLibrary' is defined but never used.", "'RTE' is defined but never used.", "'translate' is assigned a value but never used.", "'tooltipBackgroundcolor' is assigned a value but never used.", "'tooltipborderradius' is assigned a value but never used.", "'tooltipBordercolor' is assigned a value but never used.", "'tooltippadding' is assigned a value but never used.", "'elementClick' is assigned a value but never used.", "'setDismiss' is assigned a value but never used.", "'savedRange' is assigned a value but never used.", "'setSaveRange' is assigned a value but never used.", "'isEditorFocused' is assigned a value but never used.", "'setIsEditorFocused' is assigned a value but never used.", "'handleDeleteSection' is assigned a value but never used.", "'handleCloneContainer' is assigned a value but never used.", "'handleDragStart' is assigned a value but never used.", "'handleDragEnter' is assigned a value but never used.", "'handleDragEnd' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'currentStep', 'popupPosition', 'setCurrentHoveredElement', 'setTooltipPositionByXpath', and 'toolTipGuideMetaData'. Either include them or remove the dependency array.", ["2965"], "'canvasProperties' is assigned a value but never used.", "'RTEToolbar' is assigned a value but never used.", "React Hook useMemo has a missing dependency: 'handleFocus'. Either include it or remove the dependency array.", ["2966"], "'isInsideJoditPopup' is assigned a value but never used.", "'isPasteEvent' is assigned a value but never used.", "'CustomImage' is defined but never used.", "'pageinteraction' is assigned a value but never used.", "React Hook useCallback has an unnecessary dependency: 'smoothScrollTo'. Either exclude it or remove the dependency array.", ["2967"], "React Hook useEffect has missing dependencies: 'currentStep' and 'selectedTemplate'. Either include them or remove the dependency array.", ["2968"], "React Hook useEffect has missing dependencies: 'currentStep' and 'currentStepIndex'. Either include them or remove the dependency array.", ["2969"], "React Hook useCallback has unnecessary dependencies: 'calculateBestPosition' and 'scrollToTargetElement'. Either exclude them or remove the dependency array.", ["2970"], "React Hook useCallback has a missing dependency: 'steps'. Either include it or remove the dependency array.", ["2971"], "React Hook useEffect has a missing dependency: 'currentStepIndex'. Either include it or remove the dependency array.", ["2972"], "React Hook useEffect has a missing dependency: 'updateTargetAndPosition'. Either include it or remove the dependency array.", ["2973"], ["2974"], ["2975"], "'hasOnlyTextContent' is assigned a value but never used.", "'hasOnlyButton' is assigned a value but never used.", "'CANVAS_DEFAULT_VALUE_HOTSPOT' is defined but never used.", "'TCanvas' is defined but never used.", "'updateDesignelementInTooltip' is assigned a value but never used.", "'CANVAS_DEFAULT_VALUE_HOTSPOT' is assigned a value but never used.", "'dismiss' is assigned a value but never used.", "'setSelectedPosition' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'currentStep', 'selectedTemplate', 'selectedTemplateTour', 'setTooltipBackgroundcolor', 'setTooltipBordercolor', 'setTooltipBorderradius', 'setTooltipBordersize', 'setTooltipPadding', 'setTooltipPosition', 'setTooltipWidth', 'setTooltipXaxis', 'setTooltipYaxis', and 'toolTipGuideMetaData'. Either include them or remove the dependency array.", ["2976"], "'guideStatus' is assigned a value but never used.", "'RemoveIcon' is defined but never used.", "'AddIcon' is defined but never used.", "'DriveFolderUploadIcon' is defined but never used.", "'BackupIcon' is defined but never used.", "'modifySVGColor' is assigned a value but never used.", "'setCheckPointsPopup' is assigned a value but never used.", "'handleCheckPointIconColorChange' is assigned a value but never used.", "'handleCheckPointTitleColorChange' is assigned a value but never used.", "'handleCheckPointDescriptionColorChange' is assigned a value but never used.", "'handleFileUpload' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", ["2977"], "'handleMenuScroll' is assigned a value but never used.", "'useCallback' is defined but never used.", "'checkpointsEditPopup' is assigned a value but never used.", "'updateChecklistCheckPoints' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'filteredInteractions'. Either include it or remove the dependency array.", ["2978"], "'applyclicked' is assigned a value but never used.", "'isSearching' is assigned a value but never used.", ["2979"], "'handleSearch' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'checklistCheckpointListProperties' and 'icons'. Either include them or remove the dependency array.", ["2980"], "'handleColorChange' is assigned a value but never used.", "'FolderIcon' is defined but never used.", "'useAsyncError' is defined but never used.", "'getCurrentButtonInfo' is assigned a value but never used.", "'clickTimeout' is defined but never used.", "'handleEditButtonName' is assigned a value but never used.", "'Modal' is defined but never used.", "'IMG_EXPONENT' is defined but never used.", "'getAllFiles' is defined but never used.", "'selectedColor' is assigned a value but never used.", "'formOfUpload' is assigned a value but never used.", "'setFormOfUpload' is assigned a value but never used.", "'urll' is defined but never used.", "'handleHyperlinkClick' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setImageAnchorEl'. Either include it or remove the dependency array.", ["2981"], "'Image' is defined but never used.", "'Link' is defined but never used.", "'setIsUnSavedChanges' is assigned a value but never used.", "'setHtmlContent' is assigned a value but never used.", "'setTextvaluess' is assigned a value but never used.", "'setBackgroundC' is assigned a value but never used.", "'bpadding' is assigned a value but never used.", "'handleTooltipRTEBlur' is assigned a value but never used.", "React Hook useMemo has an unnecessary dependency: 'id'. Either exclude it or remove the dependency array.", ["2982"], "The 'updateContentState' function makes the dependencies of useCallback Hook (at line 270) change on every render. To fix this, wrap the definition of 'updateContentState' in its own useCallback() Hook.", ["2983"], "React Hook useEffect has a missing dependency: 'updateContentState'. Either include it or remove the dependency array.", ["2984"], "React Hook useEffect has a missing dependency: 'setToolbarVisibleRTEId'. Either include it or remove the dependency array. If 'setToolbarVisibleRTEId' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2985"], "'ColorResult' is defined but never used.", "'setBtnIdss' is assigned a value but never used.", "'gotoNextButtonId' is assigned a value but never used.", "'matchingButton' is assigned a value but never used.", ["2986"], "'getSavedLanguage' is assigned a value but never used.", "'saveLanguage' is assigned a value but never used.", "'clearSavedLanguage' is assigned a value but never used.", "'getAvailableLanguages' is defined but never used.", "React Hook useEffect has a missing dependency: 'userInfoObj'. Either include it or remove the dependency array.", ["2987"], "'orgId' is assigned a value but never used.", "'DialogContentText' is defined but never used.", "'TextareaAutosize' is defined but never used.", {"desc": "2988", "fix": "2989"}, {"desc": "2990", "fix": "2991"}, {"desc": "2992", "fix": "2993"}, {"desc": "2994", "fix": "2995"}, {"desc": "2996", "fix": "2997"}, {"desc": "2998", "fix": "2999"}, {"desc": "3000", "fix": "3001"}, {"desc": "3002", "fix": "3003"}, {"desc": "3004", "fix": "3005"}, {"desc": "3006", "fix": "3007"}, {"desc": "3008", "fix": "3009"}, {"desc": "3010", "fix": "3011"}, {"desc": "3012", "fix": "3013"}, {"desc": "3014", "fix": "3015"}, {"desc": "3016", "fix": "3017"}, {"desc": "3018", "fix": "3019"}, {"desc": "3020", "fix": "3021"}, {"desc": "3022", "fix": "3023"}, {"desc": "3024", "fix": "3025"}, {"messageId": "3026", "fix": "3027", "desc": "3028"}, {"messageId": "3029", "fix": "3030", "desc": "3031"}, {"desc": "3032", "fix": "3033"}, {"desc": "3034", "fix": "3035"}, {"desc": "3036", "fix": "3037"}, {"desc": "3038", "fix": "3039"}, {"desc": "3040", "fix": "3041"}, {"desc": "3042", "fix": "3043"}, {"desc": "3044", "fix": "3045"}, {"messageId": "3026", "fix": "3046", "desc": "3028"}, {"messageId": "3029", "fix": "3047", "desc": "3031"}, {"messageId": "3026", "fix": "3048", "desc": "3028"}, {"messageId": "3029", "fix": "3049", "desc": "3031"}, {"messageId": "3026", "fix": "3050", "desc": "3028"}, {"messageId": "3029", "fix": "3051", "desc": "3031"}, {"messageId": "3026", "fix": "3052", "desc": "3028"}, {"messageId": "3029", "fix": "3053", "desc": "3031"}, {"messageId": "3026", "fix": "3054", "desc": "3028"}, {"messageId": "3029", "fix": "3055", "desc": "3031"}, {"messageId": "3026", "fix": "3056", "desc": "3028"}, {"messageId": "3029", "fix": "3057", "desc": "3031"}, {"messageId": "3026", "fix": "3058", "desc": "3028"}, {"messageId": "3029", "fix": "3059", "desc": "3031"}, {"messageId": "3026", "fix": "3060", "desc": "3028"}, {"messageId": "3029", "fix": "3061", "desc": "3031"}, {"desc": "3062", "fix": "3063"}, {"desc": "3064", "fix": "3065"}, {"desc": "3066", "fix": "3067"}, {"desc": "3068", "fix": "3069"}, {"desc": "3070", "fix": "3071"}, {"desc": "3072", "fix": "3073"}, {"desc": "3074", "fix": "3075"}, {"desc": "3076", "fix": "3077"}, {"messageId": "3078", "data": "3079", "fix": "3080", "desc": "3081"}, {"desc": "3082", "fix": "3083"}, {"desc": "3084", "fix": "3085"}, {"desc": "3086", "fix": "3087"}, {"desc": "3088", "fix": "3089"}, {"desc": "3090", "fix": "3091"}, {"desc": "3092", "fix": "3093"}, {"desc": "3094", "fix": "3095"}, {"desc": "3096", "fix": "3097"}, {"desc": "3098", "fix": "3099"}, {"desc": "3100", "fix": "3101"}, {"desc": "3102", "fix": "3103"}, {"desc": "3104", "fix": "3105"}, {"desc": "3106", "fix": "3107"}, {"desc": "3108", "fix": "3109"}, {"desc": "3110", "fix": "3111"}, {"desc": "3112", "fix": "3113"}, {"desc": "3062", "fix": "3114"}, {"desc": "3115", "fix": "3116"}, {"desc": "3117", "fix": "3118"}, {"desc": "3119", "fix": "3120"}, {"desc": "3121", "fix": "3122"}, {"desc": "3123", "fix": "3124"}, {"desc": "3115", "fix": "3125"}, {"desc": "3126", "fix": "3127"}, {"desc": "3128", "fix": "3129"}, {"desc": "3130", "fix": "3131"}, {"desc": "3132", "fix": "3133"}, {"desc": "3134", "fix": "3135"}, {"desc": "3136", "fix": "3137"}, {"desc": "3138", "fix": "3139"}, {"desc": "3140", "fix": "3141"}, {"desc": "3142", "fix": "3143"}, {"desc": "3144", "fix": "3145"}, {"desc": "3146", "fix": "3147"}, {"desc": "3148", "fix": "3149"}, {"desc": "3148", "fix": "3150"}, {"desc": "3151", "fix": "3152"}, {"desc": "3153", "fix": "3154"}, {"desc": "3155", "fix": "3156"}, {"desc": "3157", "fix": "3158"}, {"desc": "3155", "fix": "3159"}, {"desc": "3160", "fix": "3161"}, {"desc": "3162", "fix": "3163"}, {"desc": "3164", "fix": "3165"}, {"desc": "3166", "fix": "3167"}, {"desc": "3168", "fix": "3169"}, {"desc": "3170", "fix": "3171"}, {"desc": "3172", "fix": "3173"}, {"desc": "3174", "fix": "3175"}, "Update the dependencies array to be: []", {"range": "3176", "text": "3177"}, "Update the dependencies array to be: [fetchGuideDetails, hotspot, hotspotClicked]", {"range": "3178", "text": "3179"}, "Update the dependencies array to be: [designPopup, setDesignPopup]", {"range": "3180", "text": "3181"}, "Update the dependencies array to be: [isTemplateScreen, isPopupOpen, bannerPopup, currentGuideId, selectedTemplate, selectedTemplateTour, determineCurrentScreen]", {"range": "3182", "text": "3183"}, "Update the dependencies array to be: [isCollapsed, isGuideInfoScreen, isTemplateScreen, isPopupOpen, bannerPopup, currentScreen, selectedTemplate, selectedTemplateTour, guideName, currentGuideId, guideStep, htmlContent, imageSrc, buttonColor, imageName, errors]", {"range": "3184", "text": "3185"}, "Update the dependencies array to be: [initialState, setIsAIGuidePersisted]", {"range": "3186", "text": "3187"}, "Update the dependencies array to be: [savedGuideData, isSaveInProgress, currentGuideId, selectedTemplate, selectedTemplateTour, bannerPopup, currentStep, setBannerPopup, setIsBannerCreationBuilderOpen, setIsAnnouncementCreationBuilderOpen, setIsTooltipCreationBuilderOpen, setIsHotspotCreationBuilderOpen, clearBannerButtonDetials, updateRTEContainerOnReload, setBannerButtonSelected, updateButtonContainerOnReload]", {"range": "3188", "text": "3189"}, "Update the dependencies array to be: [handleClose, selectedTemplate, selectedTemplateTour, isShowIcon, toolTipGuideMetaData, currentStep, elementSelected, resetALTKeywordForNewTooltip, setElementSelected, setIsALTKeywordEnabled]", {"range": "3190", "text": "3191"}, "Update the dependencies array to be: [openStepDropdown, plusIconclick, setSettingAnchorEl]", {"range": "3192", "text": "3193"}, "Update the dependencies array to be: [createWithAI, setIsUnSavedChanges, stepCreation]", {"range": "3194", "text": "3195"}, "Update the dependencies array to be: [isLoggedIn, organizationId, userType]", {"range": "3196", "text": "3197"}, "Update the dependencies array to be: [isAnnouncementPopupOpen, showBannerenduser, showTooltipenduser, showHotspotenduser, isTourTemplate]", {"range": "3198", "text": "3199"}, "Update the dependencies array to be: [currentGuide?.GuideStep, currentStep]", {"range": "3200", "text": "3201"}, "Update the dependencies array to be: [cleanupDuplicateSteps, createWithAI, currentGuideId, elementSelected, interactionData, resetHeightofBanner]", {"range": "3202", "text": "3203"}, "Update the dependencies array to be: [SetGuideName, cleanupDuplicateSteps, createWithAI, currentGuideId, currentStep, getGuideTypeValue, handlecreatefromScratchclick, interactionData, overlayEnabled, pageinteraction, progress, resetHeightofBanner, setBannerPopup, setBposition, setDismiss, setIsGuideInfoScreen, setOverlayEnabled, setPageInteraction, setProgress, setProgressColor, setSelectedOption, setSelectedTemplate, setSelectedTemplateTour, setTooltipCount, setTourDataOnEdit]", {"range": "3204", "text": "3205"}, "Update the dependencies array to be: [isAnnouncementPopupOpen, showHotspotenduser, isHotspotPopupOpen, isTourTemplate, isChecklistPreview, currentGuideId, setIsGuideInfoScreen, setSelectedTemplate, handlecreatefromScratchclick, SetGuideName, steps, selectedTemplate, getGuideTypeValue, currentStep]", {"range": "3206", "text": "3207"}, "Update the dependencies array to be: [isLoggedIn, setBannerPopup, setCreateWithAI, setCurrentGuideId, setIsAIGuidePersisted, setIsAnnouncementCreationBuilderOpen, setIsBannerCreationBuilderOpen, setIsGuideInfoScreen, setIsHomeScreen, setIsHotspotCreationBuilderOpen, setIsTemplateScreen, setIsTooltipCreationBuilderOpen, setIsTooltipPopup]", {"range": "3208", "text": "3209"}, "Update the dependencies array to be: [pendingWebTourModal, isTourPopupOpen, tourModalSource]", {"range": "3210", "text": "3211"}, "Update the dependencies array to be: [currentStep, deleteClicked, handleStepChange, steps, updateStepClicked]", {"range": "3212", "text": "3213"}, "removeEscape", {"range": "3214", "text": "3215"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "3216", "text": "3217"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", "Update the dependencies array to be: [loggedOut]", {"range": "3218", "text": "3219"}, "Update the dependencies array to be: [getCurrentButtonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, setCurrentButtonName, setBtnName]", {"range": "3220", "text": "3221"}, "Update the dependencies array to be: [settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, setTargetURL, defaultButtonColors.backgroundColor, defaultButtonColors.borderColor, defaultButtonColors.color, targetURL]", {"range": "3222", "text": "3223"}, "Update the dependencies array to be: [selectedActions.value, targetURL]", {"range": "3224", "text": "3225"}, "Update the dependencies array to be: [isExtensionClosed, setIsExtensionClosed]", {"range": "3226", "text": "3227"}, "Update the dependencies array to be: [hasAnnouncementOpened, setDrawerActiveMenu, setDrawerSearchText, setHasAnnouncementOpened]", {"range": "3228", "text": "3229"}, "Update the dependencies array to be: [isExtensionClosed, activeMenu, searchText, setDrawerActiveMenu, setDrawerSearchText, setIsPopupOpen]", {"range": "3230", "text": "3231"}, {"range": "3232", "text": "3215"}, {"range": "3233", "text": "3217"}, {"range": "3234", "text": "3215"}, {"range": "3235", "text": "3217"}, {"range": "3236", "text": "3215"}, {"range": "3237", "text": "3217"}, {"range": "3238", "text": "3215"}, {"range": "3239", "text": "3217"}, {"range": "3240", "text": "3215"}, {"range": "3241", "text": "3217"}, {"range": "3242", "text": "3215"}, {"range": "3243", "text": "3217"}, {"range": "3244", "text": "3215"}, {"range": "3245", "text": "3217"}, {"range": "3246", "text": "3215"}, {"range": "3247", "text": "3217"}, "Update the dependencies array to be: [checkpointslistData]", {"range": "3248", "text": "3249"}, "Update the dependencies array to be: [selectedTemplate, selectedTemplateTour, setHotspotPopup, setShowLauncherSettings, setShowTooltipCanvasSettings, setTitlePopup]", {"range": "3250", "text": "3251"}, "Update the dependencies array to be: [SetElementButtonClick, createWithAI, currentGuideId, currentStep, interactionData, setButtonClick, setDropdownValue, setElementButtonName, setElementClick, setbtnidss, toolTipGuideMetaData]", {"range": "3252", "text": "3253"}, "Update the dependencies array to be: [bannerButtonSelected, buttonColor, rtesContainer, setButtonColor, textAreas, textBoxRef]", {"range": "3254", "text": "3255"}, "Update the dependencies array to be: [bannerButtonSelected, buttonColor, removeTextArea, setButtonColor, textAreas]", {"range": "3256", "text": "3257"}, "Update the dependencies array to be: [setTextArray, textAreas]", {"range": "3258", "text": "3259"}, "Update the dependencies array to be: [createWithAI, bannerButtonSelected, buttonColor, setButtonColor, textAreas]", {"range": "3260", "text": "3261"}, "Update the dependencies array to be: [OverlayValue]", {"range": "3262", "text": "3263"}, "suggestString", {"type": "3264"}, {"range": "3265", "text": "3266"}, "Use `\"undefined\"` instead of `undefined`.", "Update the dependencies array to be: [getElementPosition, xpath]", {"range": "3267", "text": "3268"}, "Update the dependencies array to be: [savedGuideData, xpath]", {"range": "3269", "text": "3270"}, "Update the dependencies array to be: [textFieldProperties, imageProperties, customButton, currentStep, calculateOptimalWidth]", {"range": "3271", "text": "3272"}, "Update the dependencies array to be: [currentStep, guideStep, setOpenTooltip]", {"range": "3273", "text": "3274"}, "Update the dependencies array to be: [currentStep, isHotspotPopupOpen, savedGuideData?.GuideStep, selectedTemplateTour, setOpenTooltip, toolTipGuideMetaData]", {"range": "3275", "text": "3276"}, "Update the dependencies array to be: [currentStep, savedGuideData?.GuideStep, selectedTemplateTour, setOpenTooltip, showHotspotenduser, toolTipGuideMetaData]", {"range": "3277", "text": "3278"}, "Update the dependencies array to be: [toolTipGuideMetaData, elementSelected, borderSize, isALTKeywordEnabled, currentHoveredElement, rectData, selectedTemplate, selectedTemplateTour, isTooltipNameScreenOpen, isCollapsed, setCurrentHoveredElement, isGuideInfoScreen, setElementSelected, setAxisData, setTooltip, setOpenTooltip, setXpathToTooltipMetaData, createWithAI, syncAITooltipContainerData, applyHotspotProperties, applyCustomCursor]", {"range": "3279", "text": "3280"}, "Update the dependencies array to be: [isALTKeywordEnabled, elementSelected, selectedTemplate, selectedTemplateTour, applyCustomCursor]", {"range": "3281", "text": "3282"}, "Update the dependencies array to be: [stepType, currentStep, setAnnouncementPreview, setBannerPreview, setTooltipPreview, setHotspotPreview, savedGuideData?.GuideStep, setOpenTooltip, initializeTourHotspotMetadata]", {"range": "3283", "text": "3284"}, "Update the dependencies array to be: [paginationModel, activeTab, Open, accountId, fetchAnnouncements]", {"range": "3285", "text": "3286"}, "Update the dependencies array to be: [fetchAnnouncements, searchQuery]", {"range": "3287", "text": "3288"}, "Update the dependencies array to be: [isContentScrollable]", {"range": "3289", "text": "3290"}, "Update the dependencies array to be: [handlePaste, isRtlDirection, toolbarVisibleRTEId]", {"range": "3291", "text": "3292"}, "Update the dependencies array to be: [setButtonProperty]", {"range": "3293", "text": "3294"}, "Update the dependencies array to be: [checkpointslistData, completedStatus]", {"range": "3295", "text": "3296"}, "Update the dependencies array to be: [selectedItem, activeItem, createWithAI, interactionData]", {"range": "3297", "text": "3298"}, {"range": "3299", "text": "3249"}, "Update the dependencies array to be: [checklistGuideMetaData]", {"range": "3300", "text": "3301"}, "Update the dependencies array to be: [accountId, openSnackbar]", {"range": "3302", "text": "3303"}, "Update the dependencies array to be: [setElementSelected]", {"range": "3304", "text": "3305"}, "Update the dependencies array to be: [checklistGuideMetaData, checklistLauncherProperties, icons]", {"range": "3306", "text": "3307"}, "Update the dependencies array to be: [checklistLauncherProperties, icons, updateChecklistLauncher]", {"range": "3308", "text": "3309"}, {"range": "3310", "text": "3301"}, "Update the dependencies array to be: [dismissData.Color, dismissData?.dismisssel, setDismiss]", {"range": "3311", "text": "3312"}, "Update the dependencies array to be: [settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, setCurrentButtonName, setTargetURL, defaultButtonColors.backgroundColor, defaultButtonColors.borderColor, defaultButtonColors.color, setSelectedTab]", {"range": "3313", "text": "3314"}, "Update the dependencies array to be: [getCurrentButtonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, setCurrentButtonName]", {"range": "3315", "text": "3316"}, "Update the dependencies array to be: [currentStep, openTooltip, popupPosition, setCurrentHoveredElement, setTooltipPositionByXpath, toolTipGuideMetaData]", {"range": "3317", "text": "3318"}, "Update the dependencies array to be: [handleFocus, isRtlDirection]", {"range": "3319", "text": "3320"}, "Update the dependencies array to be: [universalScrollTo]", {"range": "3321", "text": "3322"}, "Update the dependencies array to be: [currentStep, currentStepIndex, interactWithPage, selectedTemplate]", {"range": "3323", "text": "3324"}, "Update the dependencies array to be: [currentStep, currentStepData?.overlay, currentStepIndex, interactWithPage]", {"range": "3325", "text": "3326"}, "Update the dependencies array to be: [currentStepIndex, steps, selectedTemplate, currentStep, pollForElement, setCurrentStep, setCurrentStepIndex]", {"range": "3327", "text": "3328"}, "Update the dependencies array to be: [selectedTemplate, currentStepIndex, setCurrentStep, currentStep, steps]", {"range": "3329", "text": "3330"}, "Update the dependencies array to be: [currentStepData, currentStepIndex, handleNext]", {"range": "3331", "text": "3332"}, "Update the dependencies array to be: [currentStepData, currentUrl, updateTargetAndPosition]", {"range": "3333", "text": "3334"}, {"range": "3335", "text": "3334"}, "Update the dependencies array to be: [currentStepData, currentUrl, rect, updateTargetAndPosition]", {"range": "3336", "text": "3337"}, "Update the dependencies array to be: [currentStep, selectedTemplate, selectedTemplateTour, setTooltipBackgroundcolor, setTooltipBordercolor, setTooltipBorderradius, setTooltipBordersize, setTooltipPadding, setTooltipPosition, setTooltipWidth, setTooltipXaxis, setTooltipYaxis, toolTipGuideMetaData]", {"range": "3338", "text": "3339"}, "Update the dependencies array to be: [fetchData]", {"range": "3340", "text": "3341"}, "Update the dependencies array to be: [selectedInteraction, interactions, searchTerm, filteredInteractions]", {"range": "3342", "text": "3343"}, {"range": "3344", "text": "3341"}, "Update the dependencies array to be: [checklistCheckpointListProperties, icons]", {"range": "3345", "text": "3346"}, "Update the dependencies array to be: [setImageAnchorEl, tooltip.visible]", {"range": "3347", "text": "3348"}, "Update the dependencies array to be: [toolbarVisible]", {"range": "3349", "text": "3350"}, "Wrap the definition of 'updateContentState' in its own useCallback() Hook.", {"range": "3351", "text": "3352"}, "Update the dependencies array to be: [rteBoxValue, updateContentState]", {"range": "3353", "text": "3354"}, "Update the dependencies array to be: [isEditing, setToolbarVisibleRTEId]", {"range": "3355", "text": "3356"}, "Update the dependencies array to be: [settingAnchorEl.value, buttonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, btnidss, currentGuideId, currentStep, createWithAI, interactionData]", {"range": "3357", "text": "3358"}, "Update the dependencies array to be: [orgId, accessToken, userInfoObj]", {"range": "3359", "text": "3360"}, [18364, 18386], "[]", [26804, 26845], "[fetchGuideDetails, hotspot, hotspotClicked]", [27327, 27340], "[designPopup, setDesignPopup]", [30064, 30211], "[isTemplateScreen, isPopupOpen, bannerPopup, currentGuideId, selectedTemplate, selectedTemplateTour, determineCurrentScreen]", [30689, 31049], "[isCollapsed, isGuideInfoScreen, isTemplateScreen, isPopupOpen, bannerPopup, currentScreen, selectedTemplate, selectedTemplateTour, guideName, currentGuideId, guideStep, htmlContent, imageSrc, buttonColor, imageName, errors]", [31417, 31431], "[initialState, setIsAIGuidePersisted]", [35825, 35859], "[savedGuideData, isSaveInProgress, currentGuideId, selectedTemplate, selectedTemplateTour, bannerPopup, currentStep, setBannerPopup, setIsBannerCreationBuilderOpen, setIsAnnouncementCreationBuilderOpen, setIsTooltipCreationBuilderOpen, setIsHotspotCreationBuilderOpen, clearBannerButtonDetials, updateRTEContainerOnReload, setBannerButtonSelected, updateButtonContainerOnReload]", [71610, 71674], "[handleClose, selectedTemplate, selectedTemplateTour, isShowIcon, toolTipGuideMetaData, currentStep, elementSelected, resetALTKeywordForNewTooltip, setElementSelected, setIsALTKeywordEnabled]", [87717, 87750], "[openStepDropdown, plusIconclick, setSettingAnchorEl]", [102933, 102947], "[createWithAI, setIsUnSavedChanges, stepCreation]", [126049, 126077], "[isLoggedIn, organizationId, userType]", [132554, 132706], "[isAnnounce<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, showB<PERSON><PERSON><PERSON>er, showTooltipenduser, showHotspotenduser, isTourTemplate]", [162411, 162468], "[currentGuide?.GuideStep, currentStep]", [167724, 167741], "[cleanupDuplicateSteps, createWithAI, currentGuideId, elementSelected, interactionData, resetHeightofBanner]", [173674, 173707], "[SetGuideName, cleanupDuplicateSteps, createWithAI, currentGuideId, currentStep, getGuideTypeValue, handlecreatefromScratchclick, interactionData, overlayEnabled, pageinteraction, progress, resetHeightofBanner, setBannerPopup, setBposition, setDismiss, setIsGuideInfoScreen, setOverlayEnabled, setPageInteraction, setProgress, setProgressColor, setSelectedOption, setSelectedTemplate, setSelectedTemplateTour, setTooltipCount, setTourDataOnEdit]", [174417, 174518], "[isAnnouncementPopupOpen, showHotspotenduser, isHotspotPopupOpen, isTourTemplate, isChecklistPreview, currentGuideId, setIsGuideInfoScreen, setSelectedTemplate, handlecreatefromScratchclick, SetGuideName, steps, selectedTemplate, getGuideTypeValue, currentStep]", [179948, 179960], "[isLoggedIn, setBannerPopup, setCreateWithAI, setCurrentGuideId, setIsAIGuidePersisted, setIsAnnouncementCreationBuilderOpen, setIsBannerCreationBuilderOpen, setIsGuideInfoScreen, setIsHomeScreen, setIsHotspotCreationBuilderOpen, setIsTemplateScreen, setIsTooltipCreationBuilderOpen, setIsTooltipPopup]", [180380, 180453], "[pendingWebTourModal, isTourPopupOpen, tourModalSource]", [181022, 181029], "[currentStep, deleteClicked, handleStepChange, steps, updateStepClicked]", [203179, 203180], "", [203179, 203179], "\\", [4501, 4503], "[loggedOut]", [16146, 16201], "[getCurrentButtonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, setCurrentButtonName, setBtnName]", [17575, 17630], "[settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, setTargetURL, defaultButtonColors.backgroundColor, defaultButtonColors.borderColor, defaultButtonColors.color, targetURL]", [18075, 18077], "[selectedActions.value, targetURL]", [2420, 2422], "[isExtensionClosed, setIsExtensionClosed]", [3087, 3110], "[hasAnnouncementOpened, setDrawerActiveMenu, setDrawerSearchText, setHasAnnouncementOpened]", [3454, 3497], "[isExtensionClosed, activeMenu, searchText, setDrawerActiveMenu, setDrawerSearchText, setIsPopupOpen]", [22521, 22522], [22521, 22521], [22525, 22526], [22525, 22525], [22536, 22537], [22536, 22536], [22540, 22541], [22540, 22540], [22569, 22570], [22569, 22569], [22573, 22574], [22573, 22573], [22584, 22585], [22584, 22584], [22588, 22589], [22588, 22588], [5960, 5993], "[checkpointslistData]", [5310, 5350], "[selectedTemplate, selectedTemplateTour, setHotspotPopup, setShowLauncherSettings, setShowTooltipCanvasSettings, setTitlePopup]", [10234, 10269], "[SetElementButtonClick, createWithAI, currentGuideId, currentStep, interactionData, setButtonClick, setDropdownValue, setElementButtonName, setElementClick, setbtnidss, toolTipGuideMetaData]", [6153, 6155], "[bannerButtonSelected, buttonColor, rtesContainer, setButtonColor, textAreas, textBoxRef]", [7218, 7240], "[bannerButtonSelected, buttonColor, removeTextArea, setButtonColor, textAreas]", [7401, 7412], "[setTex<PERSON><PERSON><PERSON><PERSON>, text<PERSON><PERSON>s]", [8963, 9028], "[createWithAI, bannerButtonSelected, buttonColor, setButtonColor, textAreas]", [4728, 4730], "[OverlayValue]", "undefined", [6583, 6592], "\"undefined\"", [6719, 6726], "[getElementPosition, xpath]", [6855, 6871], "[savedGuideData, xpath]", [14985, 15050], "[textFieldProperties, imageProperties, customButton, currentStep, calculateOptimalWidth]", [19065, 19124], "[currentStep, guideStep, setOpenTooltip]", [19978, 20020], "[currentStep, isHotspotPopupOpen, savedGuideData?.GuideStep, selectedTemplateTour, setOpenTooltip, toolTipGuideMetaData]", [20812, 20854], "[currentStep, savedGuideData?.GuideStep, selectedTemplateTour, setOpenTooltip, showHotspotenduser, toolTipGuideMetaData]", [25153, 25248], "[toolTipGuideMetaData, elementSelected, borderSize, isALTKeywordEnabled, currentHoveredElement, rectData, selectedTemplate, selectedTemplateTour, isTooltipNameScreenOpen, isCollapsed, setCurrentHoveredElement, isGuideInfoScreen, setElementSelected, setAxisData, setTooltip, setOpenTooltip, setXpathToTooltipMetaData, createWithAI, syncAITooltipContainerData, applyHotspotProperties, applyCustomCursor]", [25832, 25910], "[isALTKeywordEnabled, elementSelected, selectedTemplate, selectedTemplateTour, applyCustomCursor]", [6220, 6243], "[stepType, currentStep, setAnnouncementPreview, setBannerPreview, setTooltipPreview, setHotspotPreview, savedGuideData?.GuideStep, setOpenTooltip, initializeTourHotspotMetadata]", [8915, 8960], "[paginationModel, activeTab, Open, accountId, fetchAnnouncements]", [9266, 9279], "[fetchAnnouncements, searchQuery]", [4497, 4499], "[isContentScrollable]", [17939, 17976], "[handlePaste, isRtlDirection, toolbarVisibleRTEId]", [2651, 2653], "[setButtonProperty]", [3543, 3545], "[checkpointslistData, completedStatus]", [4387, 4413], "[selectedItem, activeItem, createWithAI, interactionData]", [4517, 4551], [5507, 5534], "[checklistGuideMetaData]", [4604, 4606], "[accountId, openSnackbar]", [17421, 17423], "[setElementSelected]", [4872, 4874], "[checklistGuideMetaData, checklistLauncherProperties, icons]", [9650, 9652], "[checklistLauncherProperties, icons, updateChecklistLauncher]", [3211, 3251], [6878, 6903], "[dismissData.Color, dismissData?.dismisssel, setDismiss]", [4631, 4686], "[settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, setCurrentButtonName, setTargetURL, defaultButtonColors.backgroundColor, defaultButtonColors.borderColor, defaultButtonColors.color, setSelectedTab]", [6164, 6219], "[getCurrentButtonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, setCurrentButtonName]", [10303, 10316], "[currentStep, openTooltip, popupPosition, setCurrentHoveredElement, setTooltipPositionByXpath, toolTipGuideMetaData]", [26651, 26667], "[handleFocus, isRtlDirection]", [19414, 19449], "[universalScrollTo]", [20567, 20603], "[currentStep, currentStepIndex, interactWithPage, selectedTemplate]", [21014, 21058], "[currentStep, currentStepData?.overlay, currentStepIndex, interactWithPage]", [30136, 30291], "[currentStepIndex, steps, selectedTemplate, currentStep, pollForElement, setCurrentStep, setCurrentStepIndex]", [32376, 32481], "[selectedTemplate, currentStepIndex, setCurrentStep, currentStep, steps]", [33682, 33711], "[currentStepData, currentStepIndex, handleNext]", [34188, 34217], "[currentStepData, currentUrl, updateTargetAndPosition]", [34629, 34658], [34717, 34752], "[currentStepData, currentUrl, rect, updateTargetAndPosition]", [7898, 7945], "[currentStep, selectedTemplate, selectedTemplateTour, setTooltipBackgroundcolor, setTooltipBordercolor, setTooltipBorderradius, setTooltipBordersize, setTooltipPadding, setTooltipPosition, setTooltipWidth, setTooltipXaxis, setTooltipYaxis, toolTipGuideMetaData]", [7680, 7682], "[fetchData]", [4285, 4332], "[selectedInteraction, interactions, searchTerm, filteredInteractions]", [6555, 6557], [12538, 12540], "[checklistCheckpointListProperties, icons]", [10130, 10147], "[setImageAnchorEl, tooltip.visible]", [5516, 5536], "[toolbarVisible]", [6319, 6489], "useCallback((content: string) => {\r\n\t\t\tconst isEmpty = isContentEmpty(content);\r\n\t\t\tconst isScrollable = isContentScrollable();\r\n\r\n\t\t\tsetContentState({ isEmpty, isScrollable });\r\n\t\t})", [9066, 9079], "[rteBoxValue, updateContentState]", [12369, 12380], "[isEditing, setToolbarVisibleRTEId]", [4267, 4417], "[settingAnchorEl.value, buttonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, btnidss, currentGuideId, currentStep, createWithAI, interactionData]", [4596, 4616], "[orgId, accessToken, userInfoObj]"]