{"ast": null, "code": "var _jsxFileName = \"E:\\\\quixy\\\\newadopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\guideSetting\\\\PopupSections\\\\RTEsection.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef, forwardRef, useCallback } from \"react\";\nimport { Box, IconButton } from \"@mui/material\";\nimport JoditEditor from \"jodit-react\";\nimport useDrawerStore from \"../../../store/drawerStore\";\nimport { copyicon, editicon, grythreedot, deletestep } from \"../../../assets/icons/icons\";\nimport { useTranslation } from 'react-i18next';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst RTEsection = /*#__PURE__*/_s(/*#__PURE__*/forwardRef(_c = _s(({\n  textBoxRef,\n  isBanner,\n  handleDeleteRTESection,\n  index,\n  guidePopUpRef,\n  onClone,\n  isCloneDisabled\n}, ref) => {\n  _s();\n  const {\n    t: translate\n  } = useTranslation();\n  const {\n    rtesContainer,\n    updateRTEContainer,\n    setIsUnSavedChanges,\n    cloneRTEContainer,\n    clearRteDetails,\n    selectedTemplate,\n    selectedTemplateTour,\n    announcementGuideMetaData,\n    toolTipGuideMetaData,\n    handleAnnouncementRTEValue,\n    handleTooltipRTEValue,\n    createWithAI,\n    currentStep,\n    ensureAnnouncementRTEContainer\n  } = useDrawerStore();\n\n  // Individual state management for each RTE\n  const [editingRTEId, setEditingRTEId] = useState(null);\n  const [toolbarVisibleRTEId, setToolbarVisibleRTEId] = useState(null);\n  const [hoveredRTEId, setHoveredRTEId] = useState(null);\n  const [contentState, setContentState] = useState({});\n  const contentRef = useRef(\"\");\n\n  // Map to store individual refs for each RTE\n  const editorRefs = useRef(new Map());\n  const containerRefs = useRef(new Map());\n\n  // Helper function to get or create editor ref for specific RTE\n  const getEditorRef = rteId => {\n    if (!editorRefs.current.has(rteId)) {\n      editorRefs.current.set(rteId, /*#__PURE__*/React.createRef());\n    }\n    return editorRefs.current.get(rteId);\n  };\n\n  // Helper function to get or create container ref for specific RTE\n  const getContainerRef = rteId => {\n    if (!containerRefs.current.has(rteId)) {\n      containerRefs.current.set(rteId, /*#__PURE__*/React.createRef());\n    }\n    return containerRefs.current.get(rteId);\n  };\n\n  // Helper functions for content state management\n  const isContentEmpty = (content, rteId) => {\n    if (!content) return true;\n\n    // Remove HTML tags and check if there's actual text content\n    const textContent = content.replace(/<[^>]*>/g, '').trim();\n    if (!textContent) return true;\n\n    // Check if it's just default placeholder content\n    const defaultContent = ['<p><br></p>', '<p></p>', '<br>', ''];\n    if (defaultContent.includes(content.trim())) return true;\n    return false;\n  };\n  const isContentScrollable = rteId => {\n    const editorRef = getEditorRef(rteId);\n    if (editorRef !== null && editorRef !== void 0 && editorRef.current) {\n      try {\n        var _editor;\n        const workplace = (_editor = editorRef.current.editor) === null || _editor === void 0 ? void 0 : _editor.workplace;\n        if (workplace) {\n          return workplace.scrollHeight > workplace.clientHeight;\n        }\n      } catch (error) {\n        // Silently handle any errors\n      }\n    }\n    return false;\n  };\n\n  // Update content state for dynamic icon positioning\n  const updateContentState = React.useCallback((content, rteId) => {\n    const isEmpty = isContentEmpty(content, rteId);\n    const isScrollable = isContentScrollable(rteId);\n    setContentState(prev => ({\n      ...prev,\n      [rteId]: {\n        isEmpty,\n        isScrollable\n      }\n    }));\n  }, []);\n\n  // Handle clicks outside the editor - now works with individual RTEs\n  useEffect(() => {\n    const handleClickOutside = event => {\n      var _document$querySelect, _document$querySelect2, _document$querySelect3, _document$querySelect4;\n      if (!editingRTEId) return; // No RTE is currently being edited\n\n      const isInsideJoditPopupContent = event.target.closest(\".jodit-popup__content\") !== null;\n      const isInsideAltTextPopup = event.target.closest(\".jodit-ui-input\") !== null;\n      const isInsidePopup = (_document$querySelect = document.querySelector(\".jodit-popup\")) === null || _document$querySelect === void 0 ? void 0 : _document$querySelect.contains(event.target);\n      const isInsideJoditPopup = (_document$querySelect2 = document.querySelector(\".jodit-wysiwyg\")) === null || _document$querySelect2 === void 0 ? void 0 : _document$querySelect2.contains(event.target);\n      const isInsideWorkplacePopup = isInsideJoditPopup || ((_document$querySelect3 = document.querySelector(\".jodit-dialog__panel\")) === null || _document$querySelect3 === void 0 ? void 0 : _document$querySelect3.contains(event.target));\n      const isSelectionMarker = event.target.id.startsWith(\"jodit-selection_marker_\");\n      const isLinkPopup = (_document$querySelect4 = document.querySelector(\".jodit-ui-input__input\")) === null || _document$querySelect4 === void 0 ? void 0 : _document$querySelect4.contains(event.target);\n      const isInsideToolbarButton = event.target.closest(\".jodit-toolbar-button__button\") !== null;\n      const isInsertButton = event.target.closest(\"button[aria-pressed='false']\") !== null;\n\n      // Get the container ref for the currently editing RTE\n      const currentContainerRef = getContainerRef(editingRTEId);\n\n      // Check if the target is inside the currently editing RTE or related elements\n      if (currentContainerRef !== null && currentContainerRef !== void 0 && currentContainerRef.current && !currentContainerRef.current.contains(event.target) &&\n      // Click outside the current editor container\n      !isInsidePopup &&\n      // Click outside the popup\n      !isInsideJoditPopup &&\n      // Click outside the WYSIWYG editor\n      !isInsideWorkplacePopup &&\n      // Click outside the workplace popup\n      !isSelectionMarker &&\n      // Click outside selection markers\n      !isLinkPopup &&\n      // Click outside link input popup\n      !isInsideToolbarButton &&\n      // Click outside the toolbar button\n      !isInsertButton && !isInsideJoditPopupContent && !isInsideAltTextPopup) {\n        setEditingRTEId(null); // Close the currently editing RTE\n        setToolbarVisibleRTEId(null); // Also hide toolbar when clicking outside\n      }\n    };\n    document.addEventListener(\"mousedown\", handleClickOutside);\n    return () => document.removeEventListener(\"mousedown\", handleClickOutside);\n  }, [editingRTEId]);\n  useEffect(() => {\n    if (editingRTEId) {\n      const editorRef = getEditorRef(editingRTEId);\n      if (editorRef !== null && editorRef !== void 0 && editorRef.current) {\n        setTimeout(() => {\n          //(editorRef.current as any).editor.focus();\n        }, 50);\n      }\n    }\n  }, [editingRTEId]);\n  const handleUpdate = useCallback((newContent, rteId, containerId) => {\n    contentRef.current = newContent;\n\n    // Check if this is an AI-created guide\n    const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\n    const isAITour = createWithAI && selectedTemplate === \"Tour\";\n    const isTourAnnouncement = isAITour && selectedTemplateTour === \"Announcement\";\n    const isTourBanner = isAITour && selectedTemplateTour === \"Banner\";\n    const isTourTooltip = isAITour && (selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\");\n    console.log(\"RTEsection handleUpdate:\", {\n      createWithAI,\n      selectedTemplate,\n      selectedTemplateTour,\n      isAIAnnouncement,\n      isAITour,\n      isTourBanner,\n      containerId,\n      newContent: newContent.substring(0, 50) + \"...\"\n    });\n    if (isAIAnnouncement) {\n      const currentStepIndex = currentStep - 1;\n      if (isTourAnnouncement) {\n        var _toolTipGuideMetaData, _toolTipGuideMetaData2;\n        // For Tour+Announcement, use toolTipGuideMetaData\n        const tooltipContainer = (_toolTipGuideMetaData = toolTipGuideMetaData[currentStepIndex]) === null || _toolTipGuideMetaData === void 0 ? void 0 : (_toolTipGuideMetaData2 = _toolTipGuideMetaData.containers) === null || _toolTipGuideMetaData2 === void 0 ? void 0 : _toolTipGuideMetaData2.find(container => container.id === containerId && container.type === \"rte\");\n        if (tooltipContainer) {\n          // Use the tooltip-specific handler for tour announcements\n          handleTooltipRTEValue(containerId, newContent);\n        }\n      } else {\n        var _announcementGuideMet, _announcementGuideMet2;\n        // For pure Announcements, use announcementGuideMetaData\n        const announcementContainer = (_announcementGuideMet = announcementGuideMetaData[currentStepIndex]) === null || _announcementGuideMet === void 0 ? void 0 : (_announcementGuideMet2 = _announcementGuideMet.containers) === null || _announcementGuideMet2 === void 0 ? void 0 : _announcementGuideMet2.find(container => container.id === containerId && container.type === \"rte\");\n        if (announcementContainer) {\n          // Use the announcement-specific handler\n          handleAnnouncementRTEValue(containerId, newContent);\n        }\n      }\n    } else if (isAITour && (isTourBanner || isTourTooltip)) {\n      var _toolTipGuideMetaData3, _toolTipGuideMetaData4;\n      // For AI Tour with Banner, Tooltip, or Hotspot steps, use toolTipGuideMetaData\n      const currentStepIndex = currentStep - 1;\n      const tooltipContainer = (_toolTipGuideMetaData3 = toolTipGuideMetaData[currentStepIndex]) === null || _toolTipGuideMetaData3 === void 0 ? void 0 : (_toolTipGuideMetaData4 = _toolTipGuideMetaData3.containers) === null || _toolTipGuideMetaData4 === void 0 ? void 0 : _toolTipGuideMetaData4.find(container => container.id === containerId && container.type === \"rte\");\n      if (tooltipContainer) {\n        // Use the tooltip-specific handler for all tour step types\n        handleTooltipRTEValue(containerId, newContent);\n        console.log(`Used handleTooltipRTEValue for ${selectedTemplateTour} step in AI tour`);\n      } else {\n        var _toolTipGuideMetaData5, _toolTipGuideMetaData6;\n        console.warn(`No tooltip container found for ${selectedTemplateTour} step`, {\n          currentStepIndex,\n          containerId,\n          availableContainers: (_toolTipGuideMetaData5 = toolTipGuideMetaData[currentStepIndex]) === null || _toolTipGuideMetaData5 === void 0 ? void 0 : (_toolTipGuideMetaData6 = _toolTipGuideMetaData5.containers) === null || _toolTipGuideMetaData6 === void 0 ? void 0 : _toolTipGuideMetaData6.map(c => ({\n            id: c.id,\n            type: c.type\n          }))\n        });\n      }\n    } else {\n      // For non-AI content or other cases, use the regular RTE container system\n      updateRTEContainer(containerId, rteId, newContent);\n      console.log(\"Used updateRTEContainer for non-AI content\");\n    }\n    setIsUnSavedChanges(true);\n\n    // Update content state for dynamic icon positioning\n    updateContentState(newContent, rteId);\n  }, [createWithAI, selectedTemplate, selectedTemplateTour, currentStep, toolTipGuideMetaData, announcementGuideMetaData, handleTooltipRTEValue, handleAnnouncementRTEValue, updateRTEContainer, setIsUnSavedChanges, updateContentState]);\n  const handleCloneContainer = containerId => {\n    // Check if cloning is disabled due to section limits\n    if (isCloneDisabled) {\n      return; // Don't clone if limit is reached\n    }\n\n    // Call the clone function from the store\n    cloneRTEContainer(containerId);\n\n    // Call the onClone callback if provided\n    if (onClone) {\n      onClone();\n    }\n  };\n  const handleDeleteSection = (containerId, rteId) => {\n    // Check if this is an AI-created announcement\n    const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\n    if (isAIAnnouncement) {\n      // For AI announcements, we need to remove from announcementGuideMetaData\n      // This would require a new function in the store, for now just call the existing one\n      clearRteDetails(containerId, rteId);\n    } else {\n      // For banners and non-AI content, use the regular clear function\n      clearRteDetails(containerId, rteId);\n    }\n\n    // Call the handleDeleteRTESection callback to update section counts\n    handleDeleteRTESection(index);\n  };\n  const handlePaste = event => {\n    event.preventDefault();\n    const clipboardData = event.clipboardData;\n    const pastedText = clipboardData.getData(\"text/plain\");\n    const pastedHtml = clipboardData.getData(\"text/html\");\n    if (pastedHtml) {\n      const isRTEContent = pastedHtml.includes(\"<!--RTE-->\");\n      if (isRTEContent) {\n        insertContent(pastedHtml);\n      } else {\n        insertContent(pastedHtml);\n      }\n    } else {\n      insertContent(pastedText);\n    }\n  };\n  const insertContent = content => {\n    if (editingRTEId) {\n      const editorRef = getEditorRef(editingRTEId);\n      if (editorRef !== null && editorRef !== void 0 && editorRef.current) {\n        const editor = editorRef.current.editor;\n        editor.selection.insertHTML(content);\n      }\n    }\n  };\n  const toggleToolbar = rteId => {\n    if (toolbarVisibleRTEId === rteId) {\n      setToolbarVisibleRTEId(null);\n    } else {\n      setToolbarVisibleRTEId(rteId);\n      // Don't set editing state, just show toolbar\n    }\n  };\n  const [isRtlDirection, setIsRtlDirection] = useState(false);\n  useEffect(() => {\n    const dir = document.body.getAttribute(\"dir\") || \"ltr\";\n    setIsRtlDirection(dir.toLowerCase() === \"rtl\");\n  }, []);\n  // Create a function to generate config for each specific RTE instance\n  const createConfigForRTE = useCallback(rteId => ({\n    readonly: false,\n    // all options from https://xdsoft.net/jodit/docs/,\n    direction: isRtlDirection ? 'rtl' : 'ltr',\n    language: 'en',\n    // Optional: change language as well\n    placeholder: 'Enter your text here',\n    toolbarSticky: false,\n    toolbarAdaptive: false,\n    // Hide toolbar by default, will be controlled by toolbarVisibleRTEId state\n    // Only show toolbar for this specific RTE instance\n    toolbar: toolbarVisibleRTEId === rteId,\n    showCharsCounter: false,\n    showWordsCounter: false,\n    showXPathInStatusbar: false,\n    statusbar: false,\n    pastePlain: true,\n    askBeforePasteHTML: false,\n    askBeforePasteFromWord: false,\n    // Enable auto-resize behavior to prevent double scrollbars\n    height: 'auto',\n    minHeight: toolbarVisibleRTEId === rteId ? 150 : 28,\n    // 150px when toolbar visible, 28px when hidden\n    maxHeight: 180,\n    buttons: ['bold', 'italic', 'underline', 'strikethrough', 'ul', 'ol', 'brush', 'font', 'fontsize', 'link', {\n      name: 'more',\n      iconURL: 'https://cdn.jsdelivr.net/npm/jodit/build/icons/three-dots.svg',\n      list: ['source', 'image', 'video', 'table', 'align', 'undo', 'redo', '|', 'hr', 'eraser', 'copyformat', 'symbol', 'fullsize', 'print', 'superscript', 'subscript', '|', 'outdent', 'indent', 'paragraph']\n    }],\n    autofocus: false,\n    // Fix dialog positioning by setting popup root to document body\n    popupRoot: document.body,\n    // Fix popup positioning issues\n    zIndex: 100000,\n    globalFullSize: false,\n    // Fix link dialog positioning\n    link: {\n      followOnDblClick: false,\n      processVideoLink: true,\n      processPastedLink: true,\n      openInNewTabCheckbox: true,\n      noFollowCheckbox: false,\n      modeClassName: 'input'\n    },\n    // Dialog configuration\n    dialog: {\n      zIndex: 100001\n    },\n    cursorAfterAutofocus: 'end',\n    events: {\n      onPaste: handlePaste // Attach custom onPaste handler\n    },\n    controls: {\n      font: {\n        list: {\n          \"Poppins, sans-serif\": \"Poppins\",\n          \"Roboto, sans-serif\": \"Roboto\",\n          \"Comic Sans MS, sans-serif\": \"Comic Sans MS\",\n          \"Open Sans, sans-serif\": \"Open Sans\",\n          \"Calibri, sans-serif\": \"Calibri\",\n          \"Century Gothic, sans-serif\": \"Century Gothic\"\n        }\n      }\n    }\n  }), [isRtlDirection, toolbarVisibleRTEId, handlePaste]);\n\n  // Determine which containers to use based on guide type\n  const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\n  const isAITour = createWithAI && selectedTemplate === \"Tour\";\n  const isTourAnnouncement = isAITour && selectedTemplateTour === \"Announcement\";\n  const isTourBanner = isAITour && selectedTemplateTour === \"Banner\";\n  const isTourTooltip = isAITour && (selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\");\n  const currentStepIndex = currentStep - 1;\n  let containersToRender = [];\n  if (isAIAnnouncement && !isTourAnnouncement) {\n    // For pure AI announcements (not in tours), use announcementGuideMetaData\n    containersToRender = ensureAnnouncementRTEContainer(currentStepIndex, false);\n  } else if (isAITour && (isTourBanner || isTourTooltip || isTourAnnouncement)) {\n    var _toolTipGuideMetaData7;\n    // For AI Tour with any step type (Banner, Tooltip, Hotspot, or Announcement), use toolTipGuideMetaData\n    if ((_toolTipGuideMetaData7 = toolTipGuideMetaData[currentStepIndex]) !== null && _toolTipGuideMetaData7 !== void 0 && _toolTipGuideMetaData7.containers) {\n      containersToRender = toolTipGuideMetaData[currentStepIndex].containers.filter(c => c.type === \"rte\");\n      console.log(`RTEsection: Using toolTipGuideMetaData containers for ${selectedTemplateTour} step ${currentStepIndex}:`, {\n        totalContainers: toolTipGuideMetaData[currentStepIndex].containers.length,\n        rteContainers: containersToRender.length,\n        rteData: containersToRender.map(c => ({\n          id: c.id,\n          rteBoxValue: c.rteBoxValue\n        }))\n      });\n    } else {\n      console.warn(`RTEsection: No toolTipGuideMetaData found for ${selectedTemplateTour} step ${currentStepIndex}`);\n      containersToRender = [];\n    }\n  } else {\n    // For non-AI content, use rtesContainer\n    containersToRender = rtesContainer;\n  }\n\n  // Initialize content state for all RTEs\n  React.useEffect(() => {\n    containersToRender.forEach(item => {\n      let rteText = \"\";\n      let rteId = \"\";\n      let id = \"\";\n      if (isAIAnnouncement && !isTourAnnouncement || isAITour && (isTourBanner || isTourTooltip || isTourAnnouncement)) {\n        rteText = item.rteBoxValue || \"\";\n        rteId = item.id;\n        id = item.id;\n      } else {\n        var _item$rtes, _item$rtes$, _item$rtes2, _item$rtes2$;\n        rteText = ((_item$rtes = item.rtes) === null || _item$rtes === void 0 ? void 0 : (_item$rtes$ = _item$rtes[0]) === null || _item$rtes$ === void 0 ? void 0 : _item$rtes$.text) || \"\";\n        rteId = (_item$rtes2 = item.rtes) === null || _item$rtes2 === void 0 ? void 0 : (_item$rtes2$ = _item$rtes2[0]) === null || _item$rtes2$ === void 0 ? void 0 : _item$rtes2$.id;\n        id = item.id;\n      }\n      if (id && rteId) {\n        updateContentState(rteText, rteId);\n      }\n    });\n  }, [containersToRender, isAIAnnouncement, isTourAnnouncement, isAITour, isTourBanner, isTourTooltip, updateContentState]);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: containersToRender.map(item => {\n      let rteText = \"\";\n      let rteId = \"\";\n      let id = \"\";\n      if (isAIAnnouncement && !isTourAnnouncement || isAITour && (isTourBanner || isTourTooltip || isTourAnnouncement)) {\n        // For AI announcements and AI tour steps (banner, tooltip, hotspot, announcement), get data from container\n        // Both announcementGuideMetaData and toolTipGuideMetaData use the same structure for RTE containers\n        rteText = item.rteBoxValue || \"\";\n        rteId = item.id;\n        id = item.id;\n      } else {\n        var _item$rtes3, _item$rtes3$, _item$rtes4, _item$rtes4$;\n        // For non-AI content, get data from rtesContainer\n        rteText = ((_item$rtes3 = item.rtes) === null || _item$rtes3 === void 0 ? void 0 : (_item$rtes3$ = _item$rtes3[0]) === null || _item$rtes3$ === void 0 ? void 0 : _item$rtes3$.text) || \"\";\n        rteId = (_item$rtes4 = item.rtes) === null || _item$rtes4 === void 0 ? void 0 : (_item$rtes4$ = _item$rtes4[0]) === null || _item$rtes4$ === void 0 ? void 0 : _item$rtes4$.id;\n        id = item.id;\n      }\n      if (!id) return null;\n      const currentContainerRef = getContainerRef(id);\n      const currentEditorRef = getEditorRef(id);\n\n      // Dynamic CSS for this specific RTE to prevent scrollbar conflicts\n      const dynamicCSS = `\n                        /* Hide the add new line button */\n                        .jodit-add-new-line {\n                            display: none !important;\n                        }\n                        /* Scoped styles for RTE ${id} */\n                        #rte-${id} .jodit-wysiwyg {\n                            color: #000000 !important;\n                            background-color: #ffffff !important;\n                            line-height: 1.4 !important;\n                            padding: 8px !important;\n                        }\n                        /* Height and scrolling behavior - scoped to this specific RTE */\n                        #rte-${id} .jodit-workplace {\n                            min-height: ${toolbarVisibleRTEId === id ? '150px' : '28px'} !important;\n                            max-height: 180px !important;\n                            overflow-y: auto !important;\n                            line-height: 1.4 !important;\n                        }\n                        #rte-${id} .jodit-container {\n                            border: none !important;\n                        }\n                        /* Enhanced scrollbar styling */\n                        #rte-${id} .jodit-workplace::-webkit-scrollbar {\n                            width: 6px !important;\n                        }\n                        #rte-${id} .jodit-workplace::-webkit-scrollbar-track {\n                            background: #f1f1f1 !important;\n                            border-radius: 3px !important;\n                        }\n                        #rte-${id} .jodit-workplace::-webkit-scrollbar-thumb {\n                            background: #c1c1c1 !important;\n                            border-radius: 3px !important;\n                        }\n                        #rte-${id} .jodit-workplace::-webkit-scrollbar-thumb:hover {\n                            background: #a8a8a8 !important;\n                        }\n                            \t#rte-${id} .jodit-wysiwyg p {\n                            color: #000000 !important;\n                            margin: 0 0 4px 0 !important;\n                            padding: 0 !important;\n                            line-height: 1.4 !important;\n\t\t\t}\n                    `;\n      return /*#__PURE__*/_jsxDEV(Box, {\n        ref: currentContainerRef,\n        onMouseEnter: () => setHoveredRTEId(id),\n        onMouseLeave: () => setHoveredRTEId(null),\n        sx: {\n          display: \"flex\",\n          alignItems: \"center\",\n          position: \"relative\",\n          \"&:hover .rte-action-icons\": {\n            opacity: \"1 !important\",\n            visibility: \"visible !important\"\n          },\n          \"&.qadpt-rte:hover .rte-action-icons\": {\n            opacity: \"1 !important\",\n            visibility: \"visible !important\"\n          },\n          \"& .jodit-status-bar-link\": {\n            display: \"none !important\"\n          },\n          \"& .jodit-editor\": {\n            fontFamily: \"'Roboto', sans-serif !important\"\n          },\n          \".jodit-editor span\": {\n            fontFamily: \"'Roboto', sans-serif !important\"\n          },\n          \".jodit-toolbar-button button\": {\n            minWidth: \"29px !important\"\n          },\n          \".jodit-react-container\": {\n            width: \"100%\",\n            whiteSpace: \"pre-wrap\",\n            wordBreak: \"break-word\"\n          },\n          // Fix Jodit dialog positioning - target correct classes\n          \".jodit.jodit-dialog\": {\n            position: \"fixed !important\",\n            zIndex: \"100001 !important\",\n            top: \"50% !important\",\n            left: \"50% !important\",\n            transform: \"translate(-50%, -50%) !important\"\n          },\n          \".jodit-dialog .jodit-dialog__panel\": {\n            position: \"relative !important\",\n            top: \"auto !important\",\n            left: \"auto !important\",\n            transform: \"none !important\",\n            maxWidth: \"400px !important\",\n            background: \"white !important\",\n            border: \"1px solid #ccc !important\",\n            borderRadius: \"4px !important\",\n            boxShadow: \"0 4px 12px rgba(0,0,0,0.15) !important\"\n          },\n          // Fix for link dialog specifically\n          \".jodit-dialog_alert\": {\n            position: \"fixed !important\",\n            zIndex: \"100001 !important\",\n            top: \"50% !important\",\n            left: \"50% !important\",\n            transform: \"translate(-50%, -50%) !important\"\n          }\n        },\n        className: \"qadpt-rte\",\n        children: [!(selectedTemplate === \"Banner\" || selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\") && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: \"absolute\",\n            // Complex positioning logic:\n            // 1. When toolbar visible: top-right of jodit-workplace (below toolbar)\n            // 2. When no content: positioned well to the left of edit icon to avoid overlap\n            // 3. When has content: top-right corner\n            top: toolbarVisibleRTEId === id ? \"43px\" : \"4px\",\n            right: \"calc(50% - 237px)\",\n            left: \"auto\",\n            // transform: toolbarVisibleRTEId === id ? \"none\" : ((contentState[id]?.isEmpty ?? isContentEmpty(rteText, id)) ? \"translateY(-50%)\" : \"none\"),\n\n            display: \"flex\",\n            gap: \"4px\",\n            zIndex: 1003,\n            alignItems: \"center\"\n          },\n          className: \"rte-action-icons\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: hoveredRTEId === id ? \"none\" : \"flex\",\n              alignItems: \"center\",\n              justifyContent: \"center\"\n            },\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              size: \"small\",\n              title: translate(\"More Options\"),\n              sx: {\n                width: \"24px\",\n                height: \"24px\",\n                transition: \"all 0.2s ease-in-out\"\n              },\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: grythreedot\n                },\n                style: {\n                  height: '12px',\n                  width: '12px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 617,\n                columnNumber: 45\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 606,\n              columnNumber: 41\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 599,\n            columnNumber: 37\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: hoveredRTEId === id ? \"flex\" : \"none\",\n              alignItems: \"center\",\n              justifyContent: \"center\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(IconButton, {\n              size: \"small\",\n              onClick: e => {\n                e.stopPropagation();\n                setToolbarVisibleRTEId(toolbarVisibleRTEId === id ? null : id);\n              },\n              title: translate(\"Toggle Toolbar\"),\n              sx: {\n                width: \"24px\",\n                height: \"24px\",\n                \"& span\": {\n                  filter: \"brightness(0) saturate(100%) invert(50%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(50%) contrast(100%)\" // Grey color\n                },\n                \"&:hover\": {\n                  transform: \"scale(1.1)\",\n                  \"& span\": {\n                    filter: \"brightness(0) saturate(100%) invert(27%) sepia(51%) saturate(2878%) hue-rotate(214deg) brightness(104%) contrast(97%)\" // Blue color\n                  }\n                },\n                transition: \"all 0.2s ease-in-out\",\n                \"& svg\": {\n                  height: \"12px\",\n                  width: \"12px\"\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: editicon\n                },\n                style: {\n                  height: '12px',\n                  width: '12px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 662,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 633,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n              size: \"small\",\n              onClick: () => handleCloneContainer(item.id),\n              disabled: isCloneDisabled,\n              title: isCloneDisabled ? translate(\"Maximum limit of 3 Rich Text sections reached\") : translate(\"Clone Section\"),\n              sx: {\n                width: \"24px\",\n                height: \"24px\",\n                backgroundColor: \"rgba(255, 255, 255, 0.95)\",\n                \"& span\": {\n                  filter: \"brightness(0) saturate(100%) invert(50%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(50%) contrast(100%)\" // Grey color\n                },\n                \"&:hover\": {\n                  \"& span\": {\n                    filter: \"brightness(0) saturate(100%) invert(27%) sepia(51%) saturate(2878%) hue-rotate(174deg) brightness(104%) contrast(97%)\" // Primary color\n                  },\n                  transform: \"scale(1.1)\"\n                },\n                \"&:disabled\": {\n                  opacity: 0.5,\n                  cursor: \"not-allowed\"\n                },\n                transition: \"all 0.2s ease-in-out\",\n                svg: {\n                  height: \"12px\",\n                  width: \"12px\"\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: copyicon\n                },\n                style: {\n                  height: '12px',\n                  width: '12px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 700,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 669,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n              size: \"small\",\n              onClick: () => handleDeleteSection(item.id, rteId),\n              title: translate(\"Delete Section\"),\n              sx: {\n                width: \"24px\",\n                height: \"24px\",\n                \"& span\": {\n                  filter: \"brightness(0) saturate(100%) invert(50%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(50%) contrast(100%)\" // Grey color\n                },\n                \"&:hover\": {\n                  transform: \"scale(1.1)\",\n                  \"& span\": {\n                    filter: \"brightness(0) saturate(100%) invert(27%) sepia(51%) saturate(2878%) hue-rotate(346deg) brightness(104%) contrast(97%)\" // Red color\n                  }\n                },\n                transition: \"all 0.2s ease-in-out\",\n                svg: {\n                  height: \"13px\",\n                  width: \"13px\"\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: deletestep\n                },\n                style: {\n                  height: '13px',\n                  width: '13px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 735,\n                columnNumber: 38\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 710,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 625,\n            columnNumber: 37\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 578,\n          columnNumber: 33\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          id: `rte-${id}`,\n          style: {\n            width: \"100%\",\n            position: \"relative\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"style\", {\n            children: dynamicCSS\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 752,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(JoditEditor, {\n            ref: currentEditorRef,\n            value: rteText,\n            config: config,\n            onChange: newContent => handleUpdate(newContent, rteId, id)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 755,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 748,\n          columnNumber: 29\n        }, this)]\n      }, id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 512,\n        columnNumber: 25\n      }, this);\n    })\n  }, void 0, false);\n}, \"0tUa/yyd9+lvs9kK/wp/+PqCt88=\", false, function () {\n  return [useTranslation, useDrawerStore];\n})), \"0tUa/yyd9+lvs9kK/wp/+PqCt88=\", false, function () {\n  return [useTranslation, useDrawerStore];\n});\n_c2 = RTEsection;\nexport default RTEsection;\nvar _c, _c2;\n$RefreshReg$(_c, \"RTEsection$forwardRef\");\n$RefreshReg$(_c2, \"RTEsection\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "forwardRef", "useCallback", "Box", "IconButton", "JoditEditor", "useDrawerStore", "copyicon", "editicon", "g<PERSON><PERSON><PERSON><PERSON>", "deletestep", "useTranslation", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "RTEsection", "_s", "_c", "textBoxRef", "isBanner", "handleDeleteRTESection", "index", "guidePopUpRef", "onClone", "isCloneDisabled", "ref", "t", "translate", "rtesContainer", "updateRTEContainer", "setIsUnSavedChanges", "cloneRTEContainer", "clearRteDetails", "selectedTemplate", "selectedTemplateTour", "announcementGuideMetaData", "toolTipGuideMetaData", "handleAnnouncementRTEValue", "handleTooltipRTEValue", "createWithAI", "currentStep", "ensureAnnouncementRTEContainer", "editingRTEId", "setEditingRTEId", "toolbarVisibleRTEId", "setToolbarVisibleRTEId", "hoveredRTEId", "setHoveredRTEId", "contentState", "setContentState", "contentRef", "editor<PERSON><PERSON><PERSON>", "Map", "containerRefs", "getEditorRef", "rteId", "current", "has", "set", "createRef", "get", "getContainerRef", "isContentEmpty", "content", "textContent", "replace", "trim", "defaultContent", "includes", "isContentScrollable", "editor<PERSON><PERSON>", "_editor", "workplace", "editor", "scrollHeight", "clientHeight", "error", "updateContentState", "isEmpty", "isScrollable", "prev", "handleClickOutside", "event", "_document$querySelect", "_document$querySelect2", "_document$querySelect3", "_document$querySelect4", "isInsideJoditPopupContent", "target", "closest", "isInsideAltTextPopup", "isInsidePopup", "document", "querySelector", "contains", "isInsideJoditPopup", "isInsideWorkplacePopup", "isSelectionMarker", "id", "startsWith", "isLinkPopup", "isInsideToolbarButton", "isInsertButton", "currentContainerRef", "addEventListener", "removeEventListener", "setTimeout", "handleUpdate", "newContent", "containerId", "isAIAnnouncement", "isAITour", "isTourAnnouncement", "isTourBanner", "isTourTooltip", "console", "log", "substring", "currentStepIndex", "_toolTipGuideMetaData", "_toolTipGuideMetaData2", "tooltipContainer", "containers", "find", "container", "type", "_announcementGuideMet", "_announcementGuideMet2", "announcementC<PERSON>r", "_toolTipGuideMetaData3", "_toolTipGuideMetaData4", "_toolTipGuideMetaData5", "_toolTipGuideMetaData6", "warn", "availableContainers", "map", "c", "handleCloneContainer", "handleDeleteSection", "handlePaste", "preventDefault", "clipboardData", "pastedText", "getData", "pastedHtml", "isRTEContent", "insertContent", "selection", "insertHTML", "toggleToolbar", "isRtlDirection", "setIsRtlDirection", "dir", "body", "getAttribute", "toLowerCase", "createConfigForRTE", "readonly", "direction", "language", "placeholder", "toolbarSticky", "toolbarAdaptive", "toolbar", "showCharsCounter", "showWordsCounter", "showXPathInStatusbar", "statusbar", "paste<PERSON>lain", "askBeforePasteHTML", "askBeforePasteFromWord", "height", "minHeight", "maxHeight", "buttons", "name", "iconURL", "list", "autofocus", "popupRoot", "zIndex", "globalFullSize", "link", "followOnDblClick", "processVideoLink", "processPastedLink", "openInNewTabCheckbox", "noFollowCheckbox", "modeClassName", "dialog", "cursorAfterAutofocus", "events", "onPaste", "controls", "font", "containersToRender", "_toolTipGuideMetaData7", "filter", "totalContainers", "length", "rteContainers", "rteData", "rteBoxValue", "for<PERSON>ach", "item", "rteText", "_item$rtes", "_item$rtes$", "_item$rtes2", "_item$rtes2$", "rtes", "text", "children", "_item$rtes3", "_item$rtes3$", "_item$rtes4", "_item$rtes4$", "currentEditorRef", "dynamicCSS", "onMouseEnter", "onMouseLeave", "sx", "display", "alignItems", "position", "opacity", "visibility", "fontFamily", "min<PERSON><PERSON><PERSON>", "width", "whiteSpace", "wordBreak", "top", "left", "transform", "max<PERSON><PERSON><PERSON>", "background", "border", "borderRadius", "boxShadow", "className", "style", "right", "gap", "justifyContent", "size", "title", "transition", "dangerouslySetInnerHTML", "__html", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "e", "stopPropagation", "disabled", "backgroundColor", "cursor", "svg", "value", "config", "onChange", "_c2", "$RefreshReg$"], "sources": ["E:/quixy/newadopt/quickadapt/QuickAdaptExtension/src/components/guideSetting/PopupSections/RTEsection.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef, forwardRef, useCallback } from \"react\";\r\nimport { Box, TextField, IconButton } from \"@mui/material\";\r\nimport JoditEditor from \"jodit-react\";\r\nimport useDrawerStore from \"../../../store/drawerStore\";\r\nimport { copyicon, editicon, grythreedot,deletestep} from \"../../../assets/icons/icons\";\r\nimport { selectedtemp } from \"../../drawer/Drawer\";\r\nimport { useTranslation } from 'react-i18next';\r\n\r\ninterface RTEsectionProps {\r\n    textBoxRef: React.MutableRefObject<HTMLDivElement | null>;\r\n    guidePopUpRef: React.MutableRefObject<HTMLDivElement | null>;\r\n    isBanner: boolean;\r\n    handleDeleteRTESection: (params: number) => void;\r\n    index: number;\r\n    onClone?: () => void;\r\n    isCloneDisabled?: boolean;\r\n}\r\n\r\nconst RTEsection: React.FC<RTEsectionProps> = forwardRef(\r\n    ({ textBoxRef, isBanner, handleDeleteRTESection, index, guidePopUpRef, onClone, isCloneDisabled }, ref) => {\r\n        const { t: translate } = useTranslation();\r\n        const {\r\n            rtesContainer,\r\n            updateRTEContainer,\r\n            setIsUnSavedChanges,\r\n            cloneRTEContainer,\r\n            clearRteDetails,\r\n            selectedTemplate,\r\n            selectedTemplateTour,\r\n            announcementGuideMetaData,\r\n            toolTipGuideMetaData,\r\n            handleAnnouncementRTEValue,\r\n            handleTooltipRTEValue,\r\n            createWithAI,\r\n            currentStep,\r\n            ensureAnnouncementRTEContainer\r\n        } = useDrawerStore();\r\n\r\n        // Individual state management for each RTE\r\n        const [editingRTEId, setEditingRTEId] = useState<string | null>(null);\r\n        const [toolbarVisibleRTEId, setToolbarVisibleRTEId] = useState<string | null>(null);\r\n        const [hoveredRTEId, setHoveredRTEId] = useState<string | null>(null);\r\n        const [contentState, setContentState] = useState<{[key: string]: {isEmpty: boolean, isScrollable: boolean}}>({});\r\n        const contentRef = useRef<string>(\"\");\r\n\r\n\r\n\r\n        // Map to store individual refs for each RTE\r\n        const editorRefs = useRef<Map<string, React.RefObject<any>>>(new Map());\r\n        const containerRefs = useRef<Map<string, React.RefObject<HTMLDivElement>>>(new Map());\r\n\r\n        // Helper function to get or create editor ref for specific RTE\r\n        const getEditorRef = (rteId: string) => {\r\n            if (!editorRefs.current.has(rteId)) {\r\n                editorRefs.current.set(rteId, React.createRef());\r\n            }\r\n            return editorRefs.current.get(rteId);\r\n        };\r\n\r\n        // Helper function to get or create container ref for specific RTE\r\n        const getContainerRef = (rteId: string) => {\r\n            if (!containerRefs.current.has(rteId)) {\r\n                containerRefs.current.set(rteId, React.createRef());\r\n            }\r\n            return containerRefs.current.get(rteId);\r\n        };\r\n\r\n        // Helper functions for content state management\r\n        const isContentEmpty = (content: string, rteId: string) => {\r\n            if (!content) return true;\r\n\r\n            // Remove HTML tags and check if there's actual text content\r\n            const textContent = content.replace(/<[^>]*>/g, '').trim();\r\n            if (!textContent) return true;\r\n\r\n            // Check if it's just default placeholder content\r\n            const defaultContent = ['<p><br></p>', '<p></p>', '<br>', ''];\r\n            if (defaultContent.includes(content.trim())) return true;\r\n\r\n            return false;\r\n        };\r\n\r\n        const isContentScrollable = (rteId: string) => {\r\n            const editorRef = getEditorRef(rteId);\r\n            if (editorRef?.current) {\r\n                try {\r\n                    const workplace = (editorRef.current as any).editor?.workplace;\r\n                    if (workplace) {\r\n                        return workplace.scrollHeight > workplace.clientHeight;\r\n                    }\r\n                } catch (error) {\r\n                    // Silently handle any errors\r\n                }\r\n            }\r\n            return false;\r\n        };\r\n\r\n        // Update content state for dynamic icon positioning\r\n        const updateContentState = React.useCallback((content: string, rteId: string) => {\r\n            const isEmpty = isContentEmpty(content, rteId);\r\n            const isScrollable = isContentScrollable(rteId);\r\n\r\n            setContentState(prev => ({\r\n                ...prev,\r\n                [rteId]: { isEmpty, isScrollable }\r\n            }));\r\n        }, []);\r\n\r\n        // Handle clicks outside the editor - now works with individual RTEs\r\n        useEffect(() => {\r\n            const handleClickOutside = (event: MouseEvent) => {\r\n                if (!editingRTEId) return; // No RTE is currently being edited\r\n\r\n                const isInsideJoditPopupContent = (event.target as HTMLElement).closest(\".jodit-popup__content\") !== null;\r\n                const isInsideAltTextPopup = (event.target as HTMLElement).closest(\".jodit-ui-input\") !== null;\r\n                const isInsidePopup = document.querySelector(\".jodit-popup\")?.contains(event.target as Node);\r\n                const isInsideJoditPopup = document.querySelector(\".jodit-wysiwyg\")?.contains(event.target as Node);\r\n                const isInsideWorkplacePopup = isInsideJoditPopup || document.querySelector(\".jodit-dialog__panel\")?.contains(event.target as Node);\r\n                const isSelectionMarker = (event.target as HTMLElement).id.startsWith(\"jodit-selection_marker_\");\r\n                const isLinkPopup = document.querySelector(\".jodit-ui-input__input\")?.contains(event.target as Node);\r\n                const isInsideToolbarButton = (event.target as HTMLElement).closest(\".jodit-toolbar-button__button\") !== null;\r\n                const isInsertButton = (event.target as HTMLElement).closest(\"button[aria-pressed='false']\") !== null;\r\n\r\n                // Get the container ref for the currently editing RTE\r\n                const currentContainerRef = getContainerRef(editingRTEId);\r\n\r\n                // Check if the target is inside the currently editing RTE or related elements\r\n                if (\r\n                    currentContainerRef?.current &&\r\n                    !currentContainerRef.current.contains(event.target as Node) && // Click outside the current editor container\r\n                    !isInsidePopup && // Click outside the popup\r\n                    !isInsideJoditPopup && // Click outside the WYSIWYG editor\r\n                    !isInsideWorkplacePopup && // Click outside the workplace popup\r\n                    !isSelectionMarker && // Click outside selection markers\r\n                    !isLinkPopup && // Click outside link input popup\r\n                    !isInsideToolbarButton && // Click outside the toolbar button\r\n                    !isInsertButton &&\r\n                    !isInsideJoditPopupContent &&\r\n                    !isInsideAltTextPopup\r\n                ) {\r\n                    setEditingRTEId(null); // Close the currently editing RTE\r\n                    setToolbarVisibleRTEId(null); // Also hide toolbar when clicking outside\r\n                }\r\n            };\r\n\r\n            document.addEventListener(\"mousedown\", handleClickOutside);\r\n            return () => document.removeEventListener(\"mousedown\", handleClickOutside);\r\n        }, [editingRTEId]);\r\n\r\n        useEffect(() => {\r\n            if (editingRTEId) {\r\n                const editorRef = getEditorRef(editingRTEId);\r\n                if (editorRef?.current) {\r\n                    setTimeout(() => {\r\n                        //(editorRef.current as any).editor.focus();\r\n                    }, 50);\r\n                }\r\n            }\r\n        }, [editingRTEId]);\r\n\r\n\r\n\r\n        const handleUpdate = useCallback((newContent: string, rteId: string, containerId: string) => {\r\n            contentRef.current = newContent;\r\n\r\n            // Check if this is an AI-created guide\r\n            const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\r\n            const isAITour = createWithAI && selectedTemplate === \"Tour\";\r\n            const isTourAnnouncement = isAITour && selectedTemplateTour === \"Announcement\";\r\n            const isTourBanner = isAITour && selectedTemplateTour === \"Banner\";\r\n            const isTourTooltip = isAITour && (selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\");\r\n\r\n            console.log(\"RTEsection handleUpdate:\", {\r\n                createWithAI,\r\n                selectedTemplate,\r\n                selectedTemplateTour,\r\n                isAIAnnouncement,\r\n                isAITour,\r\n                isTourBanner,\r\n                containerId,\r\n                newContent: newContent.substring(0, 50) + \"...\"\r\n            });\r\n\r\n            if (isAIAnnouncement) {\r\n                const currentStepIndex = currentStep - 1;\r\n\r\n                if (isTourAnnouncement) {\r\n                    // For Tour+Announcement, use toolTipGuideMetaData\r\n                    const tooltipContainer = toolTipGuideMetaData[currentStepIndex]?.containers?.find(\r\n                        (container: any) => container.id === containerId && container.type === \"rte\"\r\n                    );\r\n\r\n                    if (tooltipContainer) {\r\n                        // Use the tooltip-specific handler for tour announcements\r\n                        handleTooltipRTEValue(containerId, newContent);\r\n                    }\r\n                } else {\r\n                    // For pure Announcements, use announcementGuideMetaData\r\n                    const announcementContainer = announcementGuideMetaData[currentStepIndex]?.containers?.find(\r\n                        (container: any) => container.id === containerId && container.type === \"rte\"\r\n                    );\r\n\r\n                    if (announcementContainer) {\r\n                        // Use the announcement-specific handler\r\n                        handleAnnouncementRTEValue(containerId, newContent);\r\n                    }\r\n                }\r\n            } else if (isAITour && (isTourBanner || isTourTooltip)) {\r\n                // For AI Tour with Banner, Tooltip, or Hotspot steps, use toolTipGuideMetaData\r\n                const currentStepIndex = currentStep - 1;\r\n                const tooltipContainer = toolTipGuideMetaData[currentStepIndex]?.containers?.find(\r\n                    (container: any) => container.id === containerId && container.type === \"rte\"\r\n                );\r\n\r\n                if (tooltipContainer) {\r\n                    // Use the tooltip-specific handler for all tour step types\r\n                    handleTooltipRTEValue(containerId, newContent);\r\n                    console.log(`Used handleTooltipRTEValue for ${selectedTemplateTour} step in AI tour`);\r\n                } else {\r\n                    console.warn(`No tooltip container found for ${selectedTemplateTour} step`, {\r\n                        currentStepIndex,\r\n                        containerId,\r\n                        availableContainers: toolTipGuideMetaData[currentStepIndex]?.containers?.map(c => ({ id: c.id, type: c.type }))\r\n                    });\r\n                }\r\n            } else {\r\n                // For non-AI content or other cases, use the regular RTE container system\r\n                updateRTEContainer(containerId, rteId, newContent);\r\n                console.log(\"Used updateRTEContainer for non-AI content\");\r\n            }\r\n\r\n            setIsUnSavedChanges(true);\r\n\r\n            // Update content state for dynamic icon positioning\r\n            updateContentState(newContent, rteId);\r\n        }, [createWithAI, selectedTemplate, selectedTemplateTour, currentStep, toolTipGuideMetaData, announcementGuideMetaData, handleTooltipRTEValue, handleAnnouncementRTEValue, updateRTEContainer, setIsUnSavedChanges, updateContentState]);\r\n        const handleCloneContainer = (containerId: string) => {\r\n            // Check if cloning is disabled due to section limits\r\n            if (isCloneDisabled) {\r\n                return; // Don't clone if limit is reached\r\n            }\r\n\r\n            // Call the clone function from the store\r\n            cloneRTEContainer(containerId);\r\n\r\n            // Call the onClone callback if provided\r\n            if (onClone) {\r\n                onClone();\r\n            }\r\n        };\r\n        const handleDeleteSection = (containerId: string, rteId:string) => {\r\n            // Check if this is an AI-created announcement\r\n            const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\r\n\r\n            if (isAIAnnouncement) {\r\n                // For AI announcements, we need to remove from announcementGuideMetaData\r\n                // This would require a new function in the store, for now just call the existing one\r\n                clearRteDetails(containerId, rteId);\r\n            } else {\r\n                // For banners and non-AI content, use the regular clear function\r\n                clearRteDetails(containerId, rteId);\r\n            }\r\n\r\n            // Call the handleDeleteRTESection callback to update section counts\r\n            handleDeleteRTESection(index);\r\n        };\r\n        const handlePaste = (event: React.ClipboardEvent<HTMLDivElement>) => {\r\n            event.preventDefault();\r\n\r\n            const clipboardData = event.clipboardData;\r\n            const pastedText = clipboardData.getData(\"text/plain\");\r\n            const pastedHtml = clipboardData.getData(\"text/html\");\r\n\r\n            if (pastedHtml) {\r\n                const isRTEContent = pastedHtml.includes(\"<!--RTE-->\");\r\n                if (isRTEContent) {\r\n                    insertContent(pastedHtml);\r\n                } else {\r\n                    insertContent(pastedHtml);\r\n                }\r\n            } else {\r\n                insertContent(pastedText);\r\n            }\r\n        };\r\n\r\n\r\n        const insertContent = (content: string) => {\r\n            if (editingRTEId) {\r\n                const editorRef = getEditorRef(editingRTEId);\r\n                if (editorRef?.current) {\r\n                    const editor = (editorRef.current as any).editor;\r\n                    editor.selection.insertHTML(content);\r\n                }\r\n            }\r\n        };\r\n\r\n        const toggleToolbar = (rteId: string) => {\r\n            if (toolbarVisibleRTEId === rteId) {\r\n                setToolbarVisibleRTEId(null);\r\n            } else {\r\n                setToolbarVisibleRTEId(rteId);\r\n                // Don't set editing state, just show toolbar\r\n            }\r\n        };\r\n        const [isRtlDirection, setIsRtlDirection] = useState<boolean>(false);\r\n        useEffect(() => {\r\n    const dir = document.body.getAttribute(\"dir\") || \"ltr\";\r\n    setIsRtlDirection(dir.toLowerCase() === \"rtl\");\r\n}, []);\r\n    // Create a function to generate config for each specific RTE instance\r\n    const createConfigForRTE = useCallback((rteId: string) => ({\r\n        readonly: false, // all options from https://xdsoft.net/jodit/docs/,\r\n        direction: isRtlDirection ? 'rtl' as const : 'ltr' as const,\r\n        language:  'en', // Optional: change language as well\r\n        placeholder: 'Enter your text here',\r\n        toolbarSticky: false,\r\n        toolbarAdaptive: false,\r\n        // Hide toolbar by default, will be controlled by toolbarVisibleRTEId state\r\n        // Only show toolbar for this specific RTE instance\r\n        toolbar: toolbarVisibleRTEId === rteId,\r\n        showCharsCounter: false,\r\n        showWordsCounter: false,\r\n        showXPathInStatusbar: false,\r\n        statusbar: false,\r\n        pastePlain: true,\r\n        askBeforePasteHTML: false,\r\n        askBeforePasteFromWord: false,\r\n        // Enable auto-resize behavior to prevent double scrollbars\r\n        height: 'auto',\r\n        minHeight: toolbarVisibleRTEId === rteId ? 150 : 28, // 150px when toolbar visible, 28px when hidden\r\n        maxHeight: 180,\r\n        buttons: [\r\n            'bold', 'italic', 'underline', 'strikethrough', 'ul', 'ol', 'brush',\r\n            'font', 'fontsize', 'link',\r\n            {\r\n                name: 'more',\r\n                iconURL: 'https://cdn.jsdelivr.net/npm/jodit/build/icons/three-dots.svg',\r\n                list: [\r\n                    'source',\r\n                    'image', 'video', 'table',\r\n                    'align', 'undo', 'redo', '|',\r\n                    'hr', 'eraser', 'copyformat',\r\n                    'symbol', 'fullsize', 'print', 'superscript', 'subscript', '|',\r\n                    'outdent', 'indent', 'paragraph',\r\n                ]\r\n            }\r\n        ],\r\n        autofocus: false,\r\n        // Fix dialog positioning by setting popup root to document body\r\n        popupRoot: document.body,\r\n        // Fix popup positioning issues\r\n        zIndex: 100000,\r\n        globalFullSize: false,\r\n        // Fix link dialog positioning\r\n        link: {\r\n            followOnDblClick: false,\r\n            processVideoLink: true,\r\n            processPastedLink: true,\r\n            openInNewTabCheckbox: true,\r\n            noFollowCheckbox: false,\r\n            modeClassName: 'input' as const,\r\n        },\r\n        // Dialog configuration\r\n        dialog: {\r\n            zIndex: 100001,\r\n        },\r\n        cursorAfterAutofocus: 'end' as const,\r\n        events: {\r\n            onPaste: handlePaste, // Attach custom onPaste handler\r\n        },\r\n        controls: {\r\n            font: {\r\n                list: {\r\n                    \"Poppins, sans-serif\": \"Poppins\",\r\n                    \"Roboto, sans-serif\": \"Roboto\",\r\n                    \"Comic Sans MS, sans-serif\": \"Comic Sans MS\",\r\n                    \"Open Sans, sans-serif\": \"Open Sans\",\r\n                    \"Calibri, sans-serif\": \"Calibri\",\r\n                    \"Century Gothic, sans-serif\": \"Century Gothic\",\r\n                }\r\n            }\r\n        }\r\n    }), [isRtlDirection, toolbarVisibleRTEId, handlePaste]);\r\n\r\n        // Determine which containers to use based on guide type\r\n        const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\r\n        const isAITour = createWithAI && selectedTemplate === \"Tour\";\r\n        const isTourAnnouncement = isAITour && selectedTemplateTour === \"Announcement\";\r\n        const isTourBanner = isAITour && selectedTemplateTour === \"Banner\";\r\n        const isTourTooltip = isAITour && (selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\");\r\n        const currentStepIndex = currentStep - 1;\r\n\r\n        let containersToRender: any[] = [];\r\n\r\n        if (isAIAnnouncement && !isTourAnnouncement) {\r\n            // For pure AI announcements (not in tours), use announcementGuideMetaData\r\n            containersToRender = ensureAnnouncementRTEContainer(currentStepIndex, false);\r\n        } else if (isAITour && (isTourBanner || isTourTooltip || isTourAnnouncement)) {\r\n            // For AI Tour with any step type (Banner, Tooltip, Hotspot, or Announcement), use toolTipGuideMetaData\r\n            if (toolTipGuideMetaData[currentStepIndex]?.containers) {\r\n                containersToRender = toolTipGuideMetaData[currentStepIndex].containers.filter(c => c.type === \"rte\");\r\n                console.log(`RTEsection: Using toolTipGuideMetaData containers for ${selectedTemplateTour} step ${currentStepIndex}:`, {\r\n                    totalContainers: toolTipGuideMetaData[currentStepIndex].containers.length,\r\n                    rteContainers: containersToRender.length,\r\n                    rteData: containersToRender.map(c => ({ id: c.id, rteBoxValue: c.rteBoxValue }))\r\n                });\r\n            } else {\r\n                console.warn(`RTEsection: No toolTipGuideMetaData found for ${selectedTemplateTour} step ${currentStepIndex}`);\r\n                containersToRender = [];\r\n            }\r\n        } else {\r\n            // For non-AI content, use rtesContainer\r\n            containersToRender = rtesContainer;\r\n        }\r\n\r\n        // Initialize content state for all RTEs\r\n        React.useEffect(() => {\r\n            containersToRender.forEach((item: any) => {\r\n                let rteText = \"\";\r\n                let rteId = \"\";\r\n                let id = \"\";\r\n\r\n                if ((isAIAnnouncement && !isTourAnnouncement) || (isAITour && (isTourBanner || isTourTooltip || isTourAnnouncement))) {\r\n                    rteText = item.rteBoxValue || \"\";\r\n                    rteId = item.id;\r\n                    id = item.id;\r\n                } else {\r\n                    rteText = item.rtes?.[0]?.text || \"\";\r\n                    rteId = item.rtes?.[0]?.id;\r\n                    id = item.id;\r\n                }\r\n\r\n                if (id && rteId) {\r\n                    updateContentState(rteText, rteId);\r\n                }\r\n            });\r\n        }, [containersToRender, isAIAnnouncement, isTourAnnouncement, isAITour, isTourBanner, isTourTooltip, updateContentState]);\r\n\r\n        return (\r\n            <>\r\n                {containersToRender.map((item: any) => {\r\n                    let rteText = \"\";\r\n                    let rteId = \"\";\r\n                    let id = \"\";\r\n\r\n                    if ((isAIAnnouncement && !isTourAnnouncement) || (isAITour && (isTourBanner || isTourTooltip || isTourAnnouncement))) {\r\n                        // For AI announcements and AI tour steps (banner, tooltip, hotspot, announcement), get data from container\r\n                        // Both announcementGuideMetaData and toolTipGuideMetaData use the same structure for RTE containers\r\n                        rteText = item.rteBoxValue || \"\";\r\n                        rteId = item.id;\r\n                        id = item.id;\r\n                    } else {\r\n                        // For non-AI content, get data from rtesContainer\r\n                        rteText = item.rtes?.[0]?.text || \"\";\r\n                        rteId = item.rtes?.[0]?.id;\r\n                        id = item.id;\r\n                    }\r\n\r\n                    if (!id) return null;\r\n\r\n\r\n                    const currentContainerRef = getContainerRef(id);\r\n                    const currentEditorRef = getEditorRef(id);\r\n\r\n                    // Dynamic CSS for this specific RTE to prevent scrollbar conflicts\r\n                    const dynamicCSS = `\r\n                        /* Hide the add new line button */\r\n                        .jodit-add-new-line {\r\n                            display: none !important;\r\n                        }\r\n                        /* Scoped styles for RTE ${id} */\r\n                        #rte-${id} .jodit-wysiwyg {\r\n                            color: #000000 !important;\r\n                            background-color: #ffffff !important;\r\n                            line-height: 1.4 !important;\r\n                            padding: 8px !important;\r\n                        }\r\n                        /* Height and scrolling behavior - scoped to this specific RTE */\r\n                        #rte-${id} .jodit-workplace {\r\n                            min-height: ${toolbarVisibleRTEId === id ? '150px' : '28px'} !important;\r\n                            max-height: 180px !important;\r\n                            overflow-y: auto !important;\r\n                            line-height: 1.4 !important;\r\n                        }\r\n                        #rte-${id} .jodit-container {\r\n                            border: none !important;\r\n                        }\r\n                        /* Enhanced scrollbar styling */\r\n                        #rte-${id} .jodit-workplace::-webkit-scrollbar {\r\n                            width: 6px !important;\r\n                        }\r\n                        #rte-${id} .jodit-workplace::-webkit-scrollbar-track {\r\n                            background: #f1f1f1 !important;\r\n                            border-radius: 3px !important;\r\n                        }\r\n                        #rte-${id} .jodit-workplace::-webkit-scrollbar-thumb {\r\n                            background: #c1c1c1 !important;\r\n                            border-radius: 3px !important;\r\n                        }\r\n                        #rte-${id} .jodit-workplace::-webkit-scrollbar-thumb:hover {\r\n                            background: #a8a8a8 !important;\r\n                        }\r\n                            \t#rte-${id} .jodit-wysiwyg p {\r\n                            color: #000000 !important;\r\n                            margin: 0 0 4px 0 !important;\r\n                            padding: 0 !important;\r\n                            line-height: 1.4 !important;\r\n\t\t\t}\r\n                    `;\r\n\r\n                    return (\r\n                        <Box\r\n                            key={id}\r\n                            ref={currentContainerRef}\r\n                            onMouseEnter={() => setHoveredRTEId(id)}\r\n                            onMouseLeave={() => setHoveredRTEId(null)}\r\n                            sx={{\r\n                                display: \"flex\",\r\n                                alignItems: \"center\",\r\n                                position: \"relative\",\r\n                                \"&:hover .rte-action-icons\": {\r\n                                    opacity: \"1 !important\",\r\n                                    visibility: \"visible !important\"\r\n                                },\r\n                                \"&.qadpt-rte:hover .rte-action-icons\": {\r\n                                    opacity: \"1 !important\",\r\n                                    visibility: \"visible !important\"\r\n                                },\r\n                                \"& .jodit-status-bar-link\": {\r\n                                    display: \"none !important\",\r\n                                },\r\n                                \"& .jodit-editor\": {\r\n                                    fontFamily: \"'Roboto', sans-serif !important\",\r\n                                },\r\n                                \".jodit-editor span\": {\r\n                                    fontFamily: \"'Roboto', sans-serif !important\",\r\n                                },\r\n                                \".jodit-toolbar-button button\": {\r\n                                    minWidth: \"29px !important\",\r\n                                },\r\n                                \".jodit-react-container\": {\r\n                                    width: \"100%\",\r\n                                    whiteSpace: \"pre-wrap\",\r\n                                    wordBreak: \"break-word\",\r\n                                },\r\n                                // Fix Jodit dialog positioning - target correct classes\r\n                                \".jodit.jodit-dialog\": {\r\n                                    position: \"fixed !important\",\r\n                                    zIndex: \"100001 !important\",\r\n                                    top: \"50% !important\",\r\n                                    left: \"50% !important\",\r\n                                    transform: \"translate(-50%, -50%) !important\"\r\n                                },\r\n                                \".jodit-dialog .jodit-dialog__panel\": {\r\n                                    position: \"relative !important\",\r\n                                    top: \"auto !important\",\r\n                                    left: \"auto !important\",\r\n                                    transform: \"none !important\",\r\n                                    maxWidth: \"400px !important\",\r\n                                    background: \"white !important\",\r\n                                    border: \"1px solid #ccc !important\",\r\n                                    borderRadius: \"4px !important\",\r\n                                    boxShadow: \"0 4px 12px rgba(0,0,0,0.15) !important\"\r\n                                },\r\n                                // Fix for link dialog specifically\r\n                                \".jodit-dialog_alert\": {\r\n                                    position: \"fixed !important\",\r\n                                    zIndex: \"100001 !important\",\r\n                                    top: \"50% !important\",\r\n                                    left: \"50% !important\",\r\n                                    transform: \"translate(-50%, -50%) !important\"\r\n                                }\r\n                            }}\r\n                            className=\"qadpt-rte\"\r\n                        >\r\n                            {/* Three-dot menu and action icons - Only show for non-Banner templates */}\r\n                            {!(selectedTemplate === \"Banner\" || (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\")) && (\r\n                                <div\r\n                                    style={{\r\n                                        position: \"absolute\",\r\n                                        // Complex positioning logic:\r\n                                        // 1. When toolbar visible: top-right of jodit-workplace (below toolbar)\r\n                                        // 2. When no content: positioned well to the left of edit icon to avoid overlap\r\n                                        // 3. When has content: top-right corner\r\n                                        top: toolbarVisibleRTEId === id ? \"43px\"  : \"4px\",\r\n                                        right:  \"calc(50% - 237px)\" ,\r\n                                        left:  \"auto\" ,\r\n                                        // transform: toolbarVisibleRTEId === id ? \"none\" : ((contentState[id]?.isEmpty ?? isContentEmpty(rteText, id)) ? \"translateY(-50%)\" : \"none\"),\r\n                                        \r\n                                        display: \"flex\",\r\n                                        gap: \"4px\",\r\n                                        zIndex: 1003,\r\n                                        alignItems: \"center\",\r\n                                       \r\n                                    }}\r\n                                    className=\"rte-action-icons\"\r\n                                >\r\n                                    {/* Three-dot menu - shown by default */}\r\n                                    <div\r\n                                        style={{\r\n                                            display: hoveredRTEId === id ? \"none\" : \"flex\",\r\n                                            alignItems: \"center\",\r\n                                            justifyContent: \"center\",\r\n                                        }}\r\n                                    >\r\n                                        <IconButton\r\n                                            size=\"small\"\r\n                                            title={translate(\"More Options\")}\r\n                                            sx={{\r\n                                                width: \"24px\",\r\n                                                height: \"24px\",\r\n                                               \r\n                                              \r\n                                                transition: \"all 0.2s ease-in-out\",\r\n                                            }}\r\n                                        >\r\n                                            <span\r\n                                                dangerouslySetInnerHTML={{ __html: grythreedot }}\r\n                                                style={{ height: '12px', width: '12px' }}\r\n                                            />\r\n                                        </IconButton>\r\n                                    </div>\r\n\r\n                                    {/* Individual action icons - shown on hover */}\r\n                                    <div\r\n                                        style={{\r\n                                            display: hoveredRTEId === id ? \"flex\" : \"none\",\r\n                                            alignItems: \"center\",\r\n                                            justifyContent: \"center\",\r\n                                        }}\r\n                                    >\r\n                                {/* Edit Icon */}\r\n                                <IconButton\r\n                                    size=\"small\"\r\n                                    onClick={(e) => {\r\n                                        e.stopPropagation();\r\n                                        setToolbarVisibleRTEId(toolbarVisibleRTEId === id ? null : id);\r\n                                    }}\r\n                                    title={translate(\"Toggle Toolbar\")}\r\n                                    sx={{\r\n                                        width: \"24px\",\r\n                                        height: \"24px\",\r\n                                      \r\n                                        \"& span\": {\r\n                                            filter: \"brightness(0) saturate(100%) invert(50%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(50%) contrast(100%)\", // Grey color\r\n                                        },\r\n                                        \"&:hover\": {\r\n                                          \r\n                                            \r\n                                            transform: \"scale(1.1)\",\r\n                                            \"& span\": {\r\n                                                filter: \"brightness(0) saturate(100%) invert(27%) sepia(51%) saturate(2878%) hue-rotate(214deg) brightness(104%) contrast(97%)\", // Blue color\r\n                                            }\r\n                                        },\r\n                                        transition: \"all 0.2s ease-in-out\",\r\n                                        \"& svg\": {\r\n                                            height: \"12px\",\r\n                                            width: \"12px\",\r\n                                        }\r\n                                    }}\r\n                                >\r\n                                    <span\r\n                                        dangerouslySetInnerHTML={{ __html: editicon }}\r\n                                        style={{ height: '12px', width: '12px' }}\r\n                                    />\r\n                                </IconButton>\r\n\r\n                                {/* Clone Icon */}\r\n                                <IconButton\r\n                                    size=\"small\"\r\n                                    onClick={() => handleCloneContainer(item.id)}\r\n                                    disabled={isCloneDisabled}\r\n                                    title={isCloneDisabled ? translate(\"Maximum limit of 3 Rich Text sections reached\") : translate(\"Clone Section\")}\r\n                                    sx={{\r\n                                        width: \"24px\",\r\n                                        height: \"24px\",\r\n                                        backgroundColor: \"rgba(255, 255, 255, 0.95)\",\r\n                                        \"& span\": {\r\n                                            filter: \"brightness(0) saturate(100%) invert(50%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(50%) contrast(100%)\", // Grey color\r\n                                        },\r\n                                        \"&:hover\": {\r\n                                          \r\n                                           \r\n                                            \"& span\": {\r\n                                                filter: \"brightness(0) saturate(100%) invert(27%) sepia(51%) saturate(2878%) hue-rotate(174deg) brightness(104%) contrast(97%)\", // Primary color\r\n                                            },\r\n                                             transform: \"scale(1.1)\",\r\n                                        },\r\n                                        \"&:disabled\": {\r\n                                            opacity: 0.5,\r\n                                            cursor: \"not-allowed\"\r\n                                        },\r\n                                        transition: \"all 0.2s ease-in-out\",\r\n                                        svg: {\r\n                                            height: \"12px\",\r\n                                            width: \"12px\",\r\n                                        },\r\n                                    }}\r\n                                >\r\n                                    <span\r\n                                        dangerouslySetInnerHTML={{ __html: copyicon }}\r\n                                        style={{\r\n                                            height: '12px',\r\n                                            width: '12px'\r\n                                        }}\r\n                                    />\r\n                                </IconButton>\r\n\r\n                                {/* Delete Icon */}\r\n                                <IconButton\r\n                                    size=\"small\"\r\n                                    onClick={() => handleDeleteSection(item.id, rteId)}\r\n                                    title={translate(\"Delete Section\")}\r\n                                    sx={{\r\n                                        width: \"24px\",\r\n                                        height: \"24px\",\r\n                                       \r\n                                        \"& span\": {\r\n                                            filter: \"brightness(0) saturate(100%) invert(50%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(50%) contrast(100%)\", // Grey color\r\n                                        },\r\n                                        \"&:hover\": {\r\n                                            \r\n                                            transform: \"scale(1.1)\", \r\n                                            \"& span\": {\r\n                                                filter: \"brightness(0) saturate(100%) invert(27%) sepia(51%) saturate(2878%) hue-rotate(346deg) brightness(104%) contrast(97%)\", // Red color\r\n                                            }\r\n                                        },\r\n                                        transition: \"all 0.2s ease-in-out\",\r\n                                        svg: {\r\n                                            height: \"13px\",\r\n                                            width: \"13px\",\r\n                                        },\r\n                                    }}\r\n                                >\r\n                                     <span\r\n                                        dangerouslySetInnerHTML={{ __html: deletestep }}\r\n                                        style={{\r\n                                            height: '13px',\r\n                                            width: '13px'\r\n                                        }}\r\n                                    />\r\n                                        </IconButton>\r\n                            </div>\r\n                        </div>\r\n                        )}\r\n\r\n                            {/* Jodit Editor Container */}\r\n                            <div\r\n                                id={`rte-${id}`}\r\n                                style={{ width: \"100%\", position: \"relative\" }}\r\n                            >\r\n                                <style>\r\n                                    {dynamicCSS}\r\n                                </style>\r\n                                <JoditEditor\r\n                                    ref={currentEditorRef}\r\n                                    value={rteText}\r\n                                    config={config}\r\n                                    onChange={(newContent) => handleUpdate(newContent, rteId, id)}\r\n                                />\r\n\r\n                            </div>\r\n                        </Box>\r\n                    );\r\n                })}\r\n            </>\r\n        );\r\n    }\r\n);\r\n\r\nexport default RTEsection;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,UAAU,EAAEC,WAAW,QAAQ,OAAO;AACnF,SAASC,GAAG,EAAaC,UAAU,QAAQ,eAAe;AAC1D,OAAOC,WAAW,MAAM,aAAa;AACrC,OAAOC,cAAc,MAAM,4BAA4B;AACvD,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,WAAW,EAACC,UAAU,QAAO,6BAA6B;AAEvF,SAASC,cAAc,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAY/C,MAAMC,UAAqC,gBAAAC,EAAA,cAAGhB,UAAU,CAAAiB,EAAA,GAAAD,EAAA,CACpD,CAAC;EAAEE,UAAU;EAAEC,QAAQ;EAAEC,sBAAsB;EAAEC,KAAK;EAAEC,aAAa;EAAEC,OAAO;EAAEC;AAAgB,CAAC,EAAEC,GAAG,KAAK;EAAAT,EAAA;EACvG,MAAM;IAAEU,CAAC,EAAEC;EAAU,CAAC,GAAGjB,cAAc,CAAC,CAAC;EACzC,MAAM;IACFkB,aAAa;IACbC,kBAAkB;IAClBC,mBAAmB;IACnBC,iBAAiB;IACjBC,eAAe;IACfC,gBAAgB;IAChBC,oBAAoB;IACpBC,yBAAyB;IACzBC,oBAAoB;IACpBC,0BAA0B;IAC1BC,qBAAqB;IACrBC,YAAY;IACZC,WAAW;IACXC;EACJ,CAAC,GAAGpC,cAAc,CAAC,CAAC;;EAEpB;EACA,MAAM,CAACqC,YAAY,EAAEC,eAAe,CAAC,GAAG9C,QAAQ,CAAgB,IAAI,CAAC;EACrE,MAAM,CAAC+C,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGhD,QAAQ,CAAgB,IAAI,CAAC;EACnF,MAAM,CAACiD,YAAY,EAAEC,eAAe,CAAC,GAAGlD,QAAQ,CAAgB,IAAI,CAAC;EACrE,MAAM,CAACmD,YAAY,EAAEC,eAAe,CAAC,GAAGpD,QAAQ,CAA6D,CAAC,CAAC,CAAC;EAChH,MAAMqD,UAAU,GAAGnD,MAAM,CAAS,EAAE,CAAC;;EAIrC;EACA,MAAMoD,UAAU,GAAGpD,MAAM,CAAoC,IAAIqD,GAAG,CAAC,CAAC,CAAC;EACvE,MAAMC,aAAa,GAAGtD,MAAM,CAA+C,IAAIqD,GAAG,CAAC,CAAC,CAAC;;EAErF;EACA,MAAME,YAAY,GAAIC,KAAa,IAAK;IACpC,IAAI,CAACJ,UAAU,CAACK,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC,EAAE;MAChCJ,UAAU,CAACK,OAAO,CAACE,GAAG,CAACH,KAAK,eAAE3D,KAAK,CAAC+D,SAAS,CAAC,CAAC,CAAC;IACpD;IACA,OAAOR,UAAU,CAACK,OAAO,CAACI,GAAG,CAACL,KAAK,CAAC;EACxC,CAAC;;EAED;EACA,MAAMM,eAAe,GAAIN,KAAa,IAAK;IACvC,IAAI,CAACF,aAAa,CAACG,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC,EAAE;MACnCF,aAAa,CAACG,OAAO,CAACE,GAAG,CAACH,KAAK,eAAE3D,KAAK,CAAC+D,SAAS,CAAC,CAAC,CAAC;IACvD;IACA,OAAON,aAAa,CAACG,OAAO,CAACI,GAAG,CAACL,KAAK,CAAC;EAC3C,CAAC;;EAED;EACA,MAAMO,cAAc,GAAGA,CAACC,OAAe,EAAER,KAAa,KAAK;IACvD,IAAI,CAACQ,OAAO,EAAE,OAAO,IAAI;;IAEzB;IACA,MAAMC,WAAW,GAAGD,OAAO,CAACE,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAACC,IAAI,CAAC,CAAC;IAC1D,IAAI,CAACF,WAAW,EAAE,OAAO,IAAI;;IAE7B;IACA,MAAMG,cAAc,GAAG,CAAC,aAAa,EAAE,SAAS,EAAE,MAAM,EAAE,EAAE,CAAC;IAC7D,IAAIA,cAAc,CAACC,QAAQ,CAACL,OAAO,CAACG,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,IAAI;IAExD,OAAO,KAAK;EAChB,CAAC;EAED,MAAMG,mBAAmB,GAAId,KAAa,IAAK;IAC3C,MAAMe,SAAS,GAAGhB,YAAY,CAACC,KAAK,CAAC;IACrC,IAAIe,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEd,OAAO,EAAE;MACpB,IAAI;QAAA,IAAAe,OAAA;QACA,MAAMC,SAAS,IAAAD,OAAA,GAAID,SAAS,CAACd,OAAO,CAASiB,MAAM,cAAAF,OAAA,uBAAjCA,OAAA,CAAmCC,SAAS;QAC9D,IAAIA,SAAS,EAAE;UACX,OAAOA,SAAS,CAACE,YAAY,GAAGF,SAAS,CAACG,YAAY;QAC1D;MACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;QACZ;MAAA;IAER;IACA,OAAO,KAAK;EAChB,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAGjF,KAAK,CAACK,WAAW,CAAC,CAAC8D,OAAe,EAAER,KAAa,KAAK;IAC7E,MAAMuB,OAAO,GAAGhB,cAAc,CAACC,OAAO,EAAER,KAAK,CAAC;IAC9C,MAAMwB,YAAY,GAAGV,mBAAmB,CAACd,KAAK,CAAC;IAE/CN,eAAe,CAAC+B,IAAI,KAAK;MACrB,GAAGA,IAAI;MACP,CAACzB,KAAK,GAAG;QAAEuB,OAAO;QAAEC;MAAa;IACrC,CAAC,CAAC,CAAC;EACP,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAjF,SAAS,CAAC,MAAM;IACZ,MAAMmF,kBAAkB,GAAIC,KAAiB,IAAK;MAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MAC9C,IAAI,CAAC5C,YAAY,EAAE,OAAO,CAAC;;MAE3B,MAAM6C,yBAAyB,GAAIL,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,uBAAuB,CAAC,KAAK,IAAI;MACzG,MAAMC,oBAAoB,GAAIR,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,iBAAiB,CAAC,KAAK,IAAI;MAC9F,MAAME,aAAa,IAAAR,qBAAA,GAAGS,QAAQ,CAACC,aAAa,CAAC,cAAc,CAAC,cAAAV,qBAAA,uBAAtCA,qBAAA,CAAwCW,QAAQ,CAACZ,KAAK,CAACM,MAAc,CAAC;MAC5F,MAAMO,kBAAkB,IAAAX,sBAAA,GAAGQ,QAAQ,CAACC,aAAa,CAAC,gBAAgB,CAAC,cAAAT,sBAAA,uBAAxCA,sBAAA,CAA0CU,QAAQ,CAACZ,KAAK,CAACM,MAAc,CAAC;MACnG,MAAMQ,sBAAsB,GAAGD,kBAAkB,MAAAV,sBAAA,GAAIO,QAAQ,CAACC,aAAa,CAAC,sBAAsB,CAAC,cAAAR,sBAAA,uBAA9CA,sBAAA,CAAgDS,QAAQ,CAACZ,KAAK,CAACM,MAAc,CAAC;MACnI,MAAMS,iBAAiB,GAAIf,KAAK,CAACM,MAAM,CAAiBU,EAAE,CAACC,UAAU,CAAC,yBAAyB,CAAC;MAChG,MAAMC,WAAW,IAAAd,sBAAA,GAAGM,QAAQ,CAACC,aAAa,CAAC,wBAAwB,CAAC,cAAAP,sBAAA,uBAAhDA,sBAAA,CAAkDQ,QAAQ,CAACZ,KAAK,CAACM,MAAc,CAAC;MACpG,MAAMa,qBAAqB,GAAInB,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,+BAA+B,CAAC,KAAK,IAAI;MAC7G,MAAMa,cAAc,GAAIpB,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,8BAA8B,CAAC,KAAK,IAAI;;MAErG;MACA,MAAMc,mBAAmB,GAAG1C,eAAe,CAACnB,YAAY,CAAC;;MAEzD;MACA,IACI6D,mBAAmB,aAAnBA,mBAAmB,eAAnBA,mBAAmB,CAAE/C,OAAO,IAC5B,CAAC+C,mBAAmB,CAAC/C,OAAO,CAACsC,QAAQ,CAACZ,KAAK,CAACM,MAAc,CAAC;MAAI;MAC/D,CAACG,aAAa;MAAI;MAClB,CAACI,kBAAkB;MAAI;MACvB,CAACC,sBAAsB;MAAI;MAC3B,CAACC,iBAAiB;MAAI;MACtB,CAACG,WAAW;MAAI;MAChB,CAACC,qBAAqB;MAAI;MAC1B,CAACC,cAAc,IACf,CAACf,yBAAyB,IAC1B,CAACG,oBAAoB,EACvB;QACE/C,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC;QACvBE,sBAAsB,CAAC,IAAI,CAAC,CAAC,CAAC;MAClC;IACJ,CAAC;IAED+C,QAAQ,CAACY,gBAAgB,CAAC,WAAW,EAAEvB,kBAAkB,CAAC;IAC1D,OAAO,MAAMW,QAAQ,CAACa,mBAAmB,CAAC,WAAW,EAAExB,kBAAkB,CAAC;EAC9E,CAAC,EAAE,CAACvC,YAAY,CAAC,CAAC;EAElB5C,SAAS,CAAC,MAAM;IACZ,IAAI4C,YAAY,EAAE;MACd,MAAM4B,SAAS,GAAGhB,YAAY,CAACZ,YAAY,CAAC;MAC5C,IAAI4B,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEd,OAAO,EAAE;QACpBkD,UAAU,CAAC,MAAM;UACb;QAAA,CACH,EAAE,EAAE,CAAC;MACV;IACJ;EACJ,CAAC,EAAE,CAAChE,YAAY,CAAC,CAAC;EAIlB,MAAMiE,YAAY,GAAG1G,WAAW,CAAC,CAAC2G,UAAkB,EAAErD,KAAa,EAAEsD,WAAmB,KAAK;IACzF3D,UAAU,CAACM,OAAO,GAAGoD,UAAU;;IAE/B;IACA,MAAME,gBAAgB,GAAGvE,YAAY,KAAKN,gBAAgB,KAAK,cAAc,IAAIC,oBAAoB,KAAK,cAAc,CAAC;IACzH,MAAM6E,QAAQ,GAAGxE,YAAY,IAAIN,gBAAgB,KAAK,MAAM;IAC5D,MAAM+E,kBAAkB,GAAGD,QAAQ,IAAI7E,oBAAoB,KAAK,cAAc;IAC9E,MAAM+E,YAAY,GAAGF,QAAQ,IAAI7E,oBAAoB,KAAK,QAAQ;IAClE,MAAMgF,aAAa,GAAGH,QAAQ,KAAK7E,oBAAoB,KAAK,SAAS,IAAIA,oBAAoB,KAAK,SAAS,CAAC;IAE5GiF,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE;MACpC7E,YAAY;MACZN,gBAAgB;MAChBC,oBAAoB;MACpB4E,gBAAgB;MAChBC,QAAQ;MACRE,YAAY;MACZJ,WAAW;MACXD,UAAU,EAAEA,UAAU,CAACS,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG;IAC9C,CAAC,CAAC;IAEF,IAAIP,gBAAgB,EAAE;MAClB,MAAMQ,gBAAgB,GAAG9E,WAAW,GAAG,CAAC;MAExC,IAAIwE,kBAAkB,EAAE;QAAA,IAAAO,qBAAA,EAAAC,sBAAA;QACpB;QACA,MAAMC,gBAAgB,IAAAF,qBAAA,GAAGnF,oBAAoB,CAACkF,gBAAgB,CAAC,cAAAC,qBAAA,wBAAAC,sBAAA,GAAtCD,qBAAA,CAAwCG,UAAU,cAAAF,sBAAA,uBAAlDA,sBAAA,CAAoDG,IAAI,CAC5EC,SAAc,IAAKA,SAAS,CAAC1B,EAAE,KAAKW,WAAW,IAAIe,SAAS,CAACC,IAAI,KAAK,KAC3E,CAAC;QAED,IAAIJ,gBAAgB,EAAE;UAClB;UACAnF,qBAAqB,CAACuE,WAAW,EAAED,UAAU,CAAC;QAClD;MACJ,CAAC,MAAM;QAAA,IAAAkB,qBAAA,EAAAC,sBAAA;QACH;QACA,MAAMC,qBAAqB,IAAAF,qBAAA,GAAG3F,yBAAyB,CAACmF,gBAAgB,CAAC,cAAAQ,qBAAA,wBAAAC,sBAAA,GAA3CD,qBAAA,CAA6CJ,UAAU,cAAAK,sBAAA,uBAAvDA,sBAAA,CAAyDJ,IAAI,CACtFC,SAAc,IAAKA,SAAS,CAAC1B,EAAE,KAAKW,WAAW,IAAIe,SAAS,CAACC,IAAI,KAAK,KAC3E,CAAC;QAED,IAAIG,qBAAqB,EAAE;UACvB;UACA3F,0BAA0B,CAACwE,WAAW,EAAED,UAAU,CAAC;QACvD;MACJ;IACJ,CAAC,MAAM,IAAIG,QAAQ,KAAKE,YAAY,IAAIC,aAAa,CAAC,EAAE;MAAA,IAAAe,sBAAA,EAAAC,sBAAA;MACpD;MACA,MAAMZ,gBAAgB,GAAG9E,WAAW,GAAG,CAAC;MACxC,MAAMiF,gBAAgB,IAAAQ,sBAAA,GAAG7F,oBAAoB,CAACkF,gBAAgB,CAAC,cAAAW,sBAAA,wBAAAC,sBAAA,GAAtCD,sBAAA,CAAwCP,UAAU,cAAAQ,sBAAA,uBAAlDA,sBAAA,CAAoDP,IAAI,CAC5EC,SAAc,IAAKA,SAAS,CAAC1B,EAAE,KAAKW,WAAW,IAAIe,SAAS,CAACC,IAAI,KAAK,KAC3E,CAAC;MAED,IAAIJ,gBAAgB,EAAE;QAClB;QACAnF,qBAAqB,CAACuE,WAAW,EAAED,UAAU,CAAC;QAC9CO,OAAO,CAACC,GAAG,CAAC,kCAAkClF,oBAAoB,kBAAkB,CAAC;MACzF,CAAC,MAAM;QAAA,IAAAiG,sBAAA,EAAAC,sBAAA;QACHjB,OAAO,CAACkB,IAAI,CAAC,kCAAkCnG,oBAAoB,OAAO,EAAE;UACxEoF,gBAAgB;UAChBT,WAAW;UACXyB,mBAAmB,GAAAH,sBAAA,GAAE/F,oBAAoB,CAACkF,gBAAgB,CAAC,cAAAa,sBAAA,wBAAAC,sBAAA,GAAtCD,sBAAA,CAAwCT,UAAU,cAAAU,sBAAA,uBAAlDA,sBAAA,CAAoDG,GAAG,CAACC,CAAC,KAAK;YAAEtC,EAAE,EAAEsC,CAAC,CAACtC,EAAE;YAAE2B,IAAI,EAAEW,CAAC,CAACX;UAAK,CAAC,CAAC;QAClH,CAAC,CAAC;MACN;IACJ,CAAC,MAAM;MACH;MACAhG,kBAAkB,CAACgF,WAAW,EAAEtD,KAAK,EAAEqD,UAAU,CAAC;MAClDO,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;IAC7D;IAEAtF,mBAAmB,CAAC,IAAI,CAAC;;IAEzB;IACA+C,kBAAkB,CAAC+B,UAAU,EAAErD,KAAK,CAAC;EACzC,CAAC,EAAE,CAAChB,YAAY,EAAEN,gBAAgB,EAAEC,oBAAoB,EAAEM,WAAW,EAAEJ,oBAAoB,EAAED,yBAAyB,EAAEG,qBAAqB,EAAED,0BAA0B,EAAER,kBAAkB,EAAEC,mBAAmB,EAAE+C,kBAAkB,CAAC,CAAC;EACxO,MAAM4D,oBAAoB,GAAI5B,WAAmB,IAAK;IAClD;IACA,IAAIrF,eAAe,EAAE;MACjB,OAAO,CAAC;IACZ;;IAEA;IACAO,iBAAiB,CAAC8E,WAAW,CAAC;;IAE9B;IACA,IAAItF,OAAO,EAAE;MACTA,OAAO,CAAC,CAAC;IACb;EACJ,CAAC;EACD,MAAMmH,mBAAmB,GAAGA,CAAC7B,WAAmB,EAAEtD,KAAY,KAAK;IAC/D;IACA,MAAMuD,gBAAgB,GAAGvE,YAAY,KAAKN,gBAAgB,KAAK,cAAc,IAAIC,oBAAoB,KAAK,cAAc,CAAC;IAEzH,IAAI4E,gBAAgB,EAAE;MAClB;MACA;MACA9E,eAAe,CAAC6E,WAAW,EAAEtD,KAAK,CAAC;IACvC,CAAC,MAAM;MACH;MACAvB,eAAe,CAAC6E,WAAW,EAAEtD,KAAK,CAAC;IACvC;;IAEA;IACAnC,sBAAsB,CAACC,KAAK,CAAC;EACjC,CAAC;EACD,MAAMsH,WAAW,GAAIzD,KAA2C,IAAK;IACjEA,KAAK,CAAC0D,cAAc,CAAC,CAAC;IAEtB,MAAMC,aAAa,GAAG3D,KAAK,CAAC2D,aAAa;IACzC,MAAMC,UAAU,GAAGD,aAAa,CAACE,OAAO,CAAC,YAAY,CAAC;IACtD,MAAMC,UAAU,GAAGH,aAAa,CAACE,OAAO,CAAC,WAAW,CAAC;IAErD,IAAIC,UAAU,EAAE;MACZ,MAAMC,YAAY,GAAGD,UAAU,CAAC5E,QAAQ,CAAC,YAAY,CAAC;MACtD,IAAI6E,YAAY,EAAE;QACdC,aAAa,CAACF,UAAU,CAAC;MAC7B,CAAC,MAAM;QACHE,aAAa,CAACF,UAAU,CAAC;MAC7B;IACJ,CAAC,MAAM;MACHE,aAAa,CAACJ,UAAU,CAAC;IAC7B;EACJ,CAAC;EAGD,MAAMI,aAAa,GAAInF,OAAe,IAAK;IACvC,IAAIrB,YAAY,EAAE;MACd,MAAM4B,SAAS,GAAGhB,YAAY,CAACZ,YAAY,CAAC;MAC5C,IAAI4B,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEd,OAAO,EAAE;QACpB,MAAMiB,MAAM,GAAIH,SAAS,CAACd,OAAO,CAASiB,MAAM;QAChDA,MAAM,CAAC0E,SAAS,CAACC,UAAU,CAACrF,OAAO,CAAC;MACxC;IACJ;EACJ,CAAC;EAED,MAAMsF,aAAa,GAAI9F,KAAa,IAAK;IACrC,IAAIX,mBAAmB,KAAKW,KAAK,EAAE;MAC/BV,sBAAsB,CAAC,IAAI,CAAC;IAChC,CAAC,MAAM;MACHA,sBAAsB,CAACU,KAAK,CAAC;MAC7B;IACJ;EACJ,CAAC;EACD,MAAM,CAAC+F,cAAc,EAAEC,iBAAiB,CAAC,GAAG1J,QAAQ,CAAU,KAAK,CAAC;EACpEC,SAAS,CAAC,MAAM;IACpB,MAAM0J,GAAG,GAAG5D,QAAQ,CAAC6D,IAAI,CAACC,YAAY,CAAC,KAAK,CAAC,IAAI,KAAK;IACtDH,iBAAiB,CAACC,GAAG,CAACG,WAAW,CAAC,CAAC,KAAK,KAAK,CAAC;EAClD,CAAC,EAAE,EAAE,CAAC;EACF;EACA,MAAMC,kBAAkB,GAAG3J,WAAW,CAAEsD,KAAa,KAAM;IACvDsG,QAAQ,EAAE,KAAK;IAAE;IACjBC,SAAS,EAAER,cAAc,GAAG,KAAK,GAAY,KAAc;IAC3DS,QAAQ,EAAG,IAAI;IAAE;IACjBC,WAAW,EAAE,sBAAsB;IACnCC,aAAa,EAAE,KAAK;IACpBC,eAAe,EAAE,KAAK;IACtB;IACA;IACAC,OAAO,EAAEvH,mBAAmB,KAAKW,KAAK;IACtC6G,gBAAgB,EAAE,KAAK;IACvBC,gBAAgB,EAAE,KAAK;IACvBC,oBAAoB,EAAE,KAAK;IAC3BC,SAAS,EAAE,KAAK;IAChBC,UAAU,EAAE,IAAI;IAChBC,kBAAkB,EAAE,KAAK;IACzBC,sBAAsB,EAAE,KAAK;IAC7B;IACAC,MAAM,EAAE,MAAM;IACdC,SAAS,EAAEhI,mBAAmB,KAAKW,KAAK,GAAG,GAAG,GAAG,EAAE;IAAE;IACrDsH,SAAS,EAAE,GAAG;IACdC,OAAO,EAAE,CACL,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,eAAe,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EACnE,MAAM,EAAE,UAAU,EAAE,MAAM,EAC1B;MACIC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,+DAA+D;MACxEC,IAAI,EAAE,CACF,QAAQ,EACR,OAAO,EAAE,OAAO,EAAE,OAAO,EACzB,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,EAC5B,IAAI,EAAE,QAAQ,EAAE,YAAY,EAC5B,QAAQ,EAAE,UAAU,EAAE,OAAO,EAAE,aAAa,EAAE,WAAW,EAAE,GAAG,EAC9D,SAAS,EAAE,QAAQ,EAAE,WAAW;IAExC,CAAC,CACJ;IACDC,SAAS,EAAE,KAAK;IAChB;IACAC,SAAS,EAAEvF,QAAQ,CAAC6D,IAAI;IACxB;IACA2B,MAAM,EAAE,MAAM;IACdC,cAAc,EAAE,KAAK;IACrB;IACAC,IAAI,EAAE;MACFC,gBAAgB,EAAE,KAAK;MACvBC,gBAAgB,EAAE,IAAI;MACtBC,iBAAiB,EAAE,IAAI;MACvBC,oBAAoB,EAAE,IAAI;MAC1BC,gBAAgB,EAAE,KAAK;MACvBC,aAAa,EAAE;IACnB,CAAC;IACD;IACAC,MAAM,EAAE;MACJT,MAAM,EAAE;IACZ,CAAC;IACDU,oBAAoB,EAAE,KAAc;IACpCC,MAAM,EAAE;MACJC,OAAO,EAAErD,WAAW,CAAE;IAC1B,CAAC;IACDsD,QAAQ,EAAE;MACNC,IAAI,EAAE;QACFjB,IAAI,EAAE;UACF,qBAAqB,EAAE,SAAS;UAChC,oBAAoB,EAAE,QAAQ;UAC9B,2BAA2B,EAAE,eAAe;UAC5C,uBAAuB,EAAE,WAAW;UACpC,qBAAqB,EAAE,SAAS;UAChC,4BAA4B,EAAE;QAClC;MACJ;IACJ;EACJ,CAAC,CAAC,EAAE,CAAC3B,cAAc,EAAE1G,mBAAmB,EAAE+F,WAAW,CAAC,CAAC;;EAEnD;EACA,MAAM7B,gBAAgB,GAAGvE,YAAY,KAAKN,gBAAgB,KAAK,cAAc,IAAIC,oBAAoB,KAAK,cAAc,CAAC;EACzH,MAAM6E,QAAQ,GAAGxE,YAAY,IAAIN,gBAAgB,KAAK,MAAM;EAC5D,MAAM+E,kBAAkB,GAAGD,QAAQ,IAAI7E,oBAAoB,KAAK,cAAc;EAC9E,MAAM+E,YAAY,GAAGF,QAAQ,IAAI7E,oBAAoB,KAAK,QAAQ;EAClE,MAAMgF,aAAa,GAAGH,QAAQ,KAAK7E,oBAAoB,KAAK,SAAS,IAAIA,oBAAoB,KAAK,SAAS,CAAC;EAC5G,MAAMoF,gBAAgB,GAAG9E,WAAW,GAAG,CAAC;EAExC,IAAI2J,kBAAyB,GAAG,EAAE;EAElC,IAAIrF,gBAAgB,IAAI,CAACE,kBAAkB,EAAE;IACzC;IACAmF,kBAAkB,GAAG1J,8BAA8B,CAAC6E,gBAAgB,EAAE,KAAK,CAAC;EAChF,CAAC,MAAM,IAAIP,QAAQ,KAAKE,YAAY,IAAIC,aAAa,IAAIF,kBAAkB,CAAC,EAAE;IAAA,IAAAoF,sBAAA;IAC1E;IACA,KAAAA,sBAAA,GAAIhK,oBAAoB,CAACkF,gBAAgB,CAAC,cAAA8E,sBAAA,eAAtCA,sBAAA,CAAwC1E,UAAU,EAAE;MACpDyE,kBAAkB,GAAG/J,oBAAoB,CAACkF,gBAAgB,CAAC,CAACI,UAAU,CAAC2E,MAAM,CAAC7D,CAAC,IAAIA,CAAC,CAACX,IAAI,KAAK,KAAK,CAAC;MACpGV,OAAO,CAACC,GAAG,CAAC,yDAAyDlF,oBAAoB,SAASoF,gBAAgB,GAAG,EAAE;QACnHgF,eAAe,EAAElK,oBAAoB,CAACkF,gBAAgB,CAAC,CAACI,UAAU,CAAC6E,MAAM;QACzEC,aAAa,EAAEL,kBAAkB,CAACI,MAAM;QACxCE,OAAO,EAAEN,kBAAkB,CAAC5D,GAAG,CAACC,CAAC,KAAK;UAAEtC,EAAE,EAAEsC,CAAC,CAACtC,EAAE;UAAEwG,WAAW,EAAElE,CAAC,CAACkE;QAAY,CAAC,CAAC;MACnF,CAAC,CAAC;IACN,CAAC,MAAM;MACHvF,OAAO,CAACkB,IAAI,CAAC,iDAAiDnG,oBAAoB,SAASoF,gBAAgB,EAAE,CAAC;MAC9G6E,kBAAkB,GAAG,EAAE;IAC3B;EACJ,CAAC,MAAM;IACH;IACAA,kBAAkB,GAAGvK,aAAa;EACtC;;EAEA;EACAhC,KAAK,CAACE,SAAS,CAAC,MAAM;IAClBqM,kBAAkB,CAACQ,OAAO,CAAEC,IAAS,IAAK;MACtC,IAAIC,OAAO,GAAG,EAAE;MAChB,IAAItJ,KAAK,GAAG,EAAE;MACd,IAAI2C,EAAE,GAAG,EAAE;MAEX,IAAKY,gBAAgB,IAAI,CAACE,kBAAkB,IAAMD,QAAQ,KAAKE,YAAY,IAAIC,aAAa,IAAIF,kBAAkB,CAAE,EAAE;QAClH6F,OAAO,GAAGD,IAAI,CAACF,WAAW,IAAI,EAAE;QAChCnJ,KAAK,GAAGqJ,IAAI,CAAC1G,EAAE;QACfA,EAAE,GAAG0G,IAAI,CAAC1G,EAAE;MAChB,CAAC,MAAM;QAAA,IAAA4G,UAAA,EAAAC,WAAA,EAAAC,WAAA,EAAAC,YAAA;QACHJ,OAAO,GAAG,EAAAC,UAAA,GAAAF,IAAI,CAACM,IAAI,cAAAJ,UAAA,wBAAAC,WAAA,GAATD,UAAA,CAAY,CAAC,CAAC,cAAAC,WAAA,uBAAdA,WAAA,CAAgBI,IAAI,KAAI,EAAE;QACpC5J,KAAK,IAAAyJ,WAAA,GAAGJ,IAAI,CAACM,IAAI,cAAAF,WAAA,wBAAAC,YAAA,GAATD,WAAA,CAAY,CAAC,CAAC,cAAAC,YAAA,uBAAdA,YAAA,CAAgB/G,EAAE;QAC1BA,EAAE,GAAG0G,IAAI,CAAC1G,EAAE;MAChB;MAEA,IAAIA,EAAE,IAAI3C,KAAK,EAAE;QACbsB,kBAAkB,CAACgI,OAAO,EAAEtJ,KAAK,CAAC;MACtC;IACJ,CAAC,CAAC;EACN,CAAC,EAAE,CAAC4I,kBAAkB,EAAErF,gBAAgB,EAAEE,kBAAkB,EAAED,QAAQ,EAAEE,YAAY,EAAEC,aAAa,EAAErC,kBAAkB,CAAC,CAAC;EAEzH,oBACIjE,OAAA,CAAAE,SAAA;IAAAsM,QAAA,EACKjB,kBAAkB,CAAC5D,GAAG,CAAEqE,IAAS,IAAK;MACnC,IAAIC,OAAO,GAAG,EAAE;MAChB,IAAItJ,KAAK,GAAG,EAAE;MACd,IAAI2C,EAAE,GAAG,EAAE;MAEX,IAAKY,gBAAgB,IAAI,CAACE,kBAAkB,IAAMD,QAAQ,KAAKE,YAAY,IAAIC,aAAa,IAAIF,kBAAkB,CAAE,EAAE;QAClH;QACA;QACA6F,OAAO,GAAGD,IAAI,CAACF,WAAW,IAAI,EAAE;QAChCnJ,KAAK,GAAGqJ,IAAI,CAAC1G,EAAE;QACfA,EAAE,GAAG0G,IAAI,CAAC1G,EAAE;MAChB,CAAC,MAAM;QAAA,IAAAmH,WAAA,EAAAC,YAAA,EAAAC,WAAA,EAAAC,YAAA;QACH;QACAX,OAAO,GAAG,EAAAQ,WAAA,GAAAT,IAAI,CAACM,IAAI,cAAAG,WAAA,wBAAAC,YAAA,GAATD,WAAA,CAAY,CAAC,CAAC,cAAAC,YAAA,uBAAdA,YAAA,CAAgBH,IAAI,KAAI,EAAE;QACpC5J,KAAK,IAAAgK,WAAA,GAAGX,IAAI,CAACM,IAAI,cAAAK,WAAA,wBAAAC,YAAA,GAATD,WAAA,CAAY,CAAC,CAAC,cAAAC,YAAA,uBAAdA,YAAA,CAAgBtH,EAAE;QAC1BA,EAAE,GAAG0G,IAAI,CAAC1G,EAAE;MAChB;MAEA,IAAI,CAACA,EAAE,EAAE,OAAO,IAAI;MAGpB,MAAMK,mBAAmB,GAAG1C,eAAe,CAACqC,EAAE,CAAC;MAC/C,MAAMuH,gBAAgB,GAAGnK,YAAY,CAAC4C,EAAE,CAAC;;MAEzC;MACA,MAAMwH,UAAU,GAAG;AACvC;AACA;AACA;AACA;AACA,mDAAmDxH,EAAE;AACrD,+BAA+BA,EAAE;AACjC;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+BA,EAAE;AACjC,0CAA0CtD,mBAAmB,KAAKsD,EAAE,GAAG,OAAO,GAAG,MAAM;AACvF;AACA;AACA;AACA;AACA,+BAA+BA,EAAE;AACjC;AACA;AACA;AACA,+BAA+BA,EAAE;AACjC;AACA;AACA,+BAA+BA,EAAE;AACjC;AACA;AACA;AACA,+BAA+BA,EAAE;AACjC;AACA;AACA;AACA,+BAA+BA,EAAE;AACjC;AACA;AACA,oCAAoCA,EAAE;AACtC;AACA;AACA;AACA;AACA;AACA,qBAAqB;MAED,oBACItF,OAAA,CAACV,GAAG;QAEAuB,GAAG,EAAE8E,mBAAoB;QACzBoH,YAAY,EAAEA,CAAA,KAAM5K,eAAe,CAACmD,EAAE,CAAE;QACxC0H,YAAY,EAAEA,CAAA,KAAM7K,eAAe,CAAC,IAAI,CAAE;QAC1C8K,EAAE,EAAE;UACAC,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,QAAQ,EAAE,UAAU;UACpB,2BAA2B,EAAE;YACzBC,OAAO,EAAE,cAAc;YACvBC,UAAU,EAAE;UAChB,CAAC;UACD,qCAAqC,EAAE;YACnCD,OAAO,EAAE,cAAc;YACvBC,UAAU,EAAE;UAChB,CAAC;UACD,0BAA0B,EAAE;YACxBJ,OAAO,EAAE;UACb,CAAC;UACD,iBAAiB,EAAE;YACfK,UAAU,EAAE;UAChB,CAAC;UACD,oBAAoB,EAAE;YAClBA,UAAU,EAAE;UAChB,CAAC;UACD,8BAA8B,EAAE;YAC5BC,QAAQ,EAAE;UACd,CAAC;UACD,wBAAwB,EAAE;YACtBC,KAAK,EAAE,MAAM;YACbC,UAAU,EAAE,UAAU;YACtBC,SAAS,EAAE;UACf,CAAC;UACD;UACA,qBAAqB,EAAE;YACnBP,QAAQ,EAAE,kBAAkB;YAC5B5C,MAAM,EAAE,mBAAmB;YAC3BoD,GAAG,EAAE,gBAAgB;YACrBC,IAAI,EAAE,gBAAgB;YACtBC,SAAS,EAAE;UACf,CAAC;UACD,oCAAoC,EAAE;YAClCV,QAAQ,EAAE,qBAAqB;YAC/BQ,GAAG,EAAE,iBAAiB;YACtBC,IAAI,EAAE,iBAAiB;YACvBC,SAAS,EAAE,iBAAiB;YAC5BC,QAAQ,EAAE,kBAAkB;YAC5BC,UAAU,EAAE,kBAAkB;YAC9BC,MAAM,EAAE,2BAA2B;YACnCC,YAAY,EAAE,gBAAgB;YAC9BC,SAAS,EAAE;UACf,CAAC;UACD;UACA,qBAAqB,EAAE;YACnBf,QAAQ,EAAE,kBAAkB;YAC5B5C,MAAM,EAAE,mBAAmB;YAC3BoD,GAAG,EAAE,gBAAgB;YACrBC,IAAI,EAAE,gBAAgB;YACtBC,SAAS,EAAE;UACf;QACJ,CAAE;QACFM,SAAS,EAAC,WAAW;QAAA5B,QAAA,GAGpB,EAAEnL,gBAAgB,KAAK,QAAQ,IAAKA,gBAAgB,KAAK,MAAM,IAAIC,oBAAoB,KAAK,QAAS,CAAC,iBACnGtB,OAAA;UACIqO,KAAK,EAAE;YACHjB,QAAQ,EAAE,UAAU;YACpB;YACA;YACA;YACA;YACAQ,GAAG,EAAE5L,mBAAmB,KAAKsD,EAAE,GAAG,MAAM,GAAI,KAAK;YACjDgJ,KAAK,EAAG,mBAAmB;YAC3BT,IAAI,EAAG,MAAM;YACb;;YAEAX,OAAO,EAAE,MAAM;YACfqB,GAAG,EAAE,KAAK;YACV/D,MAAM,EAAE,IAAI;YACZ2C,UAAU,EAAE;UAEhB,CAAE;UACFiB,SAAS,EAAC,kBAAkB;UAAA5B,QAAA,gBAG5BxM,OAAA;YACIqO,KAAK,EAAE;cACHnB,OAAO,EAAEhL,YAAY,KAAKoD,EAAE,GAAG,MAAM,GAAG,MAAM;cAC9C6H,UAAU,EAAE,QAAQ;cACpBqB,cAAc,EAAE;YACpB,CAAE;YAAAhC,QAAA,eAEFxM,OAAA,CAACT,UAAU;cACPkP,IAAI,EAAC,OAAO;cACZC,KAAK,EAAE3N,SAAS,CAAC,cAAc,CAAE;cACjCkM,EAAE,EAAE;gBACAQ,KAAK,EAAE,MAAM;gBACb1D,MAAM,EAAE,MAAM;gBAGd4E,UAAU,EAAE;cAChB,CAAE;cAAAnC,QAAA,eAEFxM,OAAA;gBACI4O,uBAAuB,EAAE;kBAAEC,MAAM,EAAEjP;gBAAY,CAAE;gBACjDyO,KAAK,EAAE;kBAAEtE,MAAM,EAAE,MAAM;kBAAE0D,KAAK,EAAE;gBAAO;cAAE;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eAGNjP,OAAA;YACIqO,KAAK,EAAE;cACHnB,OAAO,EAAEhL,YAAY,KAAKoD,EAAE,GAAG,MAAM,GAAG,MAAM;cAC9C6H,UAAU,EAAE,QAAQ;cACpBqB,cAAc,EAAE;YACpB,CAAE;YAAAhC,QAAA,gBAGVxM,OAAA,CAACT,UAAU;cACPkP,IAAI,EAAC,OAAO;cACZS,OAAO,EAAGC,CAAC,IAAK;gBACZA,CAAC,CAACC,eAAe,CAAC,CAAC;gBACnBnN,sBAAsB,CAACD,mBAAmB,KAAKsD,EAAE,GAAG,IAAI,GAAGA,EAAE,CAAC;cAClE,CAAE;cACFoJ,KAAK,EAAE3N,SAAS,CAAC,gBAAgB,CAAE;cACnCkM,EAAE,EAAE;gBACAQ,KAAK,EAAE,MAAM;gBACb1D,MAAM,EAAE,MAAM;gBAEd,QAAQ,EAAE;kBACN0B,MAAM,EAAE,iHAAiH,CAAE;gBAC/H,CAAC;gBACD,SAAS,EAAE;kBAGPqC,SAAS,EAAE,YAAY;kBACvB,QAAQ,EAAE;oBACNrC,MAAM,EAAE,uHAAuH,CAAE;kBACrI;gBACJ,CAAC;gBACDkD,UAAU,EAAE,sBAAsB;gBAClC,OAAO,EAAE;kBACL5E,MAAM,EAAE,MAAM;kBACd0D,KAAK,EAAE;gBACX;cACJ,CAAE;cAAAjB,QAAA,eAEFxM,OAAA;gBACI4O,uBAAuB,EAAE;kBAAEC,MAAM,EAAElP;gBAAS,CAAE;gBAC9C0O,KAAK,EAAE;kBAAEtE,MAAM,EAAE,MAAM;kBAAE0D,KAAK,EAAE;gBAAO;cAAE;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,eAGbjP,OAAA,CAACT,UAAU;cACPkP,IAAI,EAAC,OAAO;cACZS,OAAO,EAAEA,CAAA,KAAMrH,oBAAoB,CAACmE,IAAI,CAAC1G,EAAE,CAAE;cAC7C+J,QAAQ,EAAEzO,eAAgB;cAC1B8N,KAAK,EAAE9N,eAAe,GAAGG,SAAS,CAAC,+CAA+C,CAAC,GAAGA,SAAS,CAAC,eAAe,CAAE;cACjHkM,EAAE,EAAE;gBACAQ,KAAK,EAAE,MAAM;gBACb1D,MAAM,EAAE,MAAM;gBACduF,eAAe,EAAE,2BAA2B;gBAC5C,QAAQ,EAAE;kBACN7D,MAAM,EAAE,iHAAiH,CAAE;gBAC/H,CAAC;gBACD,SAAS,EAAE;kBAGP,QAAQ,EAAE;oBACNA,MAAM,EAAE,uHAAuH,CAAE;kBACrI,CAAC;kBACAqC,SAAS,EAAE;gBAChB,CAAC;gBACD,YAAY,EAAE;kBACVT,OAAO,EAAE,GAAG;kBACZkC,MAAM,EAAE;gBACZ,CAAC;gBACDZ,UAAU,EAAE,sBAAsB;gBAClCa,GAAG,EAAE;kBACDzF,MAAM,EAAE,MAAM;kBACd0D,KAAK,EAAE;gBACX;cACJ,CAAE;cAAAjB,QAAA,eAEFxM,OAAA;gBACI4O,uBAAuB,EAAE;kBAAEC,MAAM,EAAEnP;gBAAS,CAAE;gBAC9C2O,KAAK,EAAE;kBACHtE,MAAM,EAAE,MAAM;kBACd0D,KAAK,EAAE;gBACX;cAAE;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,eAGbjP,OAAA,CAACT,UAAU;cACPkP,IAAI,EAAC,OAAO;cACZS,OAAO,EAAEA,CAAA,KAAMpH,mBAAmB,CAACkE,IAAI,CAAC1G,EAAE,EAAE3C,KAAK,CAAE;cACnD+L,KAAK,EAAE3N,SAAS,CAAC,gBAAgB,CAAE;cACnCkM,EAAE,EAAE;gBACAQ,KAAK,EAAE,MAAM;gBACb1D,MAAM,EAAE,MAAM;gBAEd,QAAQ,EAAE;kBACN0B,MAAM,EAAE,iHAAiH,CAAE;gBAC/H,CAAC;gBACD,SAAS,EAAE;kBAEPqC,SAAS,EAAE,YAAY;kBACvB,QAAQ,EAAE;oBACNrC,MAAM,EAAE,uHAAuH,CAAE;kBACrI;gBACJ,CAAC;gBACDkD,UAAU,EAAE,sBAAsB;gBAClCa,GAAG,EAAE;kBACDzF,MAAM,EAAE,MAAM;kBACd0D,KAAK,EAAE;gBACX;cACJ,CAAE;cAAAjB,QAAA,eAEDxM,OAAA;gBACG4O,uBAAuB,EAAE;kBAAEC,MAAM,EAAEhP;gBAAW,CAAE;gBAChDwO,KAAK,EAAE;kBACHtE,MAAM,EAAE,MAAM;kBACd0D,KAAK,EAAE;gBACX;cAAE;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACc,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACJ,eAGGjP,OAAA;UACIsF,EAAE,EAAE,OAAOA,EAAE,EAAG;UAChB+I,KAAK,EAAE;YAAEZ,KAAK,EAAE,MAAM;YAAEL,QAAQ,EAAE;UAAW,CAAE;UAAAZ,QAAA,gBAE/CxM,OAAA;YAAAwM,QAAA,EACKM;UAAU;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eACRjP,OAAA,CAACR,WAAW;YACRqB,GAAG,EAAEgM,gBAAiB;YACtB4C,KAAK,EAAExD,OAAQ;YACfyD,MAAM,EAAEA,MAAO;YACfC,QAAQ,EAAG3J,UAAU,IAAKD,YAAY,CAACC,UAAU,EAAErD,KAAK,EAAE2C,EAAE;UAAE;YAAAwJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAED,CAAC;MAAA,GAzPD3J,EAAE;QAAAwJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA0PN,CAAC;IAEd,CAAC;EAAC,gBACJ,CAAC;AAEX,CAAC;EAAA,QA3uB4BnP,cAAc,EAgBnCL,cAAc;AAAA,EA4tB1B,CAAC;EAAA,QA5uBgCK,cAAc,EAgBnCL,cAAc;AAAA,EA4tBzB;AAACmQ,GAAA,GA9uBIzP,UAAqC;AAgvB3C,eAAeA,UAAU;AAAC,IAAAE,EAAA,EAAAuP,GAAA;AAAAC,YAAA,CAAAxP,EAAA;AAAAwP,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}