import React, { useState, useMemo } from 'react';
import {
  Select,
  MenuItem,
  FormControl,
  Box,
  Typography,
  SelectChangeEvent,
  Tooltip,
  IconButton,
  TextField,
  InputAdornment,
} from '@mui/material';
import { Language as LanguageIcon, Search as SearchIcon } from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import useInfoStore from '../../store/UserInfoStore';
import { useTranslationContext } from '../../contexts/TranslationContext';


interface LanguageSelectorProps {
  variant?: 'select' | 'icon';
  size?: 'small' | 'medium';
  showLabel?: boolean;
  className?: string;
}

const LanguageSelector: React.FC<LanguageSelectorProps> = ({
  variant = 'select',
  size = 'small',
  showLabel = false,
  className = '',
}) => {
  const { t: translate } = useTranslation();
  const { availableLanguages, currentLanguage, changeLanguage, isLoading, isInitialized } = useTranslationContext();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [localLoading, setLocalLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  const orgId = useInfoStore((state) => state.orgDetails?.OrganizationId);

  // Get native language names
  const getLanguageDisplayName = (langCode: string, fallbackName: string) => {
    try {
      const displayNames = new Intl.DisplayNames([langCode], { type: 'language' });
      return displayNames.of(langCode) || fallbackName;
    } catch (error) {
      return fallbackName;
    }
  };

  // Sort languages alphabetically by their display name
  const sortedLanguages = useMemo(() => {
    return [...availableLanguages].sort((a, b) => {
      const nameA = getLanguageDisplayName(a.LanguageCode, a.Language);
      const nameB = getLanguageDisplayName(b.LanguageCode, b.Language);
      return nameA.localeCompare(nameB);
    });
  }, [availableLanguages]);

  // Filter languages based on search query
  const filteredLanguages = useMemo(() => {
    if (!searchQuery.trim()) {
      return sortedLanguages;
    }
    return sortedLanguages.filter(lang => {
      const nativeName = getLanguageDisplayName(lang.LanguageCode, lang.Language);
      return nativeName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        lang.Language.toLowerCase().includes(searchQuery.toLowerCase()) ||
        lang.LanguageCode.toLowerCase().includes(searchQuery.toLowerCase());
    });
  }, [sortedLanguages, searchQuery]);

  // Don't render if not initialized or no languages available
  if (!isInitialized || availableLanguages.length === 0) {
    return null;
  }

  // Ensure we have a valid current language
  const validCurrentLanguage = sortedLanguages.find(
    lang => lang.LanguageCode.toLowerCase() === currentLanguage.toLowerCase()
  ) ? currentLanguage : sortedLanguages[0]?.LanguageCode || 'en';

  const handleLanguageChange = async (event: SelectChangeEvent<string>) => {

    const newLanguageCode = event.target.value;
    if (newLanguageCode === validCurrentLanguage) return;

    setLocalLoading(true);
    try {
      await changeLanguage(newLanguageCode);
      // Language saving is now handled in the i18n module
    } catch (error) {
      console.error('Language change failed:', error);
    } finally {
      setLocalLoading(false);
    }
  };

  const handleIconClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
    setSearchQuery(''); // Clear search when closing
  };

  const handleMenuItemClick = async (languageCode: string) => {
    if (languageCode === validCurrentLanguage) {
      handleClose();
      return;
    }

    setLocalLoading(true);
    try {
      await changeLanguage(languageCode);
      // Language saving is now handled in the i18n module
    } catch (error) {
      console.error('Language change failed:', error);
    } finally {
      setLocalLoading(false);
      handleClose();
    }
  };

  if (variant === 'icon') {
    return (
      <>
        <Tooltip arrow title={translate('Change Language')}>
          <IconButton
            onClick={handleIconClick}
            size={size}
            className={className}
            disabled={isLoading || localLoading}
          >
            <LanguageIcon sx={{height :"22px" , width : "22px"}} />
          </IconButton>
        </Tooltip>
        <Select
          open={Boolean(anchorEl)}
          onClose={handleClose}
          value={validCurrentLanguage}
          MenuProps={{
            anchorEl,
            open: Boolean(anchorEl),
            onClose: handleClose,
            anchorOrigin: { vertical: 'bottom', horizontal: 'right' },
            transformOrigin: { vertical: 'top', horizontal: 'right' },
            PaperProps: {
              sx: { maxHeight: 300, minWidth: 250 }
            }
          }}
          sx={{ display: 'none' }}
        >
          {/* Sticky search bar at the top */}
          <Box sx={{
            position: 'sticky',
            top: 0,
            zIndex: 1000,
            backgroundColor: 'background.paper',
            p: 1,
            borderBottom: '1px solid #e0e0e0',
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
          }}>
            <TextField
              size="small"
              placeholder={translate('Search languages...')}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onClick={(e) => e.stopPropagation()}
              onKeyDown={(e) => e.stopPropagation()}
              fullWidth
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon fontSize="small" />
                  </InputAdornment>
                ),
              }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  '& fieldset': {
                    borderColor: '#e0e0e0',
                  },
                  '&:hover fieldset': {
                    borderColor: '#b0b0b0',
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: 'primary.main',
                  },
                },
              }}
            />
          </Box>

          {filteredLanguages.length === 0 ? (
            <MenuItem disabled>
              <Typography variant="body2" color="text.secondary">
                {translate('No languages found')}
              </Typography>
            </MenuItem>
          ) : (
            filteredLanguages.map((lang) => (
              <MenuItem
                key={lang.LanguageId}
                value={lang.LanguageCode}
                onClick={() => handleMenuItemClick(lang.LanguageCode)}
                selected={lang.LanguageCode === validCurrentLanguage}
              >
                <Box display="flex" alignItems="center" gap={1}>
                  <Typography variant="body2">
                    {getLanguageDisplayName(lang.LanguageCode, lang.Language)}
                  </Typography>
                </Box>
              </MenuItem>
            ))
          )}
        </Select>
      </>
    );
  }

  return (
    <FormControl size={size} className={className}>
      {showLabel && (
        <Typography variant="caption" sx={{ mb: 0.5 }}>
          {translate('Language')}
        </Typography>
      )}
      <Select
        value={validCurrentLanguage}
        onChange={handleLanguageChange}
        onClose={() => setSearchQuery('')}
        disabled={isLoading || localLoading}
        MenuProps={{
          PaperProps: {
            sx: { maxHeight: 300, minWidth: 250 }
          }
        }}
        sx={{
          minWidth: 120,
          '& .MuiSelect-select': {
            display: 'flex',
            alignItems: 'center',
            gap: 1,
          },
        }}
      >
        {/* Sticky search bar at the top */}
        <Box sx={{
          position: 'sticky',
          top: 0,
          zIndex: 1000,
          backgroundColor: 'background.paper',
          p: 1,
          borderBottom: '1px solid #e0e0e0',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
        }}>
          <TextField
            size="small"
            placeholder={translate('Search languages...')}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onClick={(e) => e.stopPropagation()}
            onKeyDown={(e) => e.stopPropagation()}
            fullWidth
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon fontSize="small" />
                </InputAdornment>
              ),
            }}
            sx={{
              '& .MuiOutlinedInput-root': {
                '& fieldset': {
                  borderColor: '#e0e0e0',
                },
                '&:hover fieldset': {
                  borderColor: '#b0b0b0',
                },
                '&.Mui-focused fieldset': {
                  borderColor: 'primary.main',
                },
              },
            }}
          />
        </Box>

        {filteredLanguages.length === 0 ? (
          <MenuItem disabled>
            <Typography variant="body2" color="text.secondary">
              {translate('No languages found')}
            </Typography>
          </MenuItem>
        ) : (
          filteredLanguages.map((lang) => (
            <MenuItem key={lang.LanguageId} value={lang.LanguageCode}>
              <Box display="flex" alignItems="center" gap={1}>
                <Typography variant="body2">
                  {getLanguageDisplayName(lang.LanguageCode, lang.Language)}
                </Typography>
              </Box>
            </MenuItem>
          ))
        )}
      </Select>
    </FormControl>
  );
};

export default LanguageSelector;
