{"ast": null, "code": "\"use client\";\n\nvar _jsxFileName = \"E:\\\\quixy\\\\newadopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\guideSetting\\\\PopupSections\\\\RTEsection.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef, forwardRef, useMemo, useCallback } from \"react\";\nimport { Box, IconButton } from \"@mui/material\";\nimport JoditEditor from \"jodit-react\";\nimport MoreHorizIcon from \"@mui/icons-material/MoreHoriz\";\nimport useDrawerStore from \"../../../store/drawerStore\";\nimport { copyicon, editicon } from \"../../../assets/icons/icons\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst RTEsection = /*#__PURE__*/_s(/*#__PURE__*/forwardRef(_c = _s(({\n  textBoxRef,\n  isBanner,\n  handleDeleteRTESection,\n  index,\n  guidePopUpRef,\n  onClone,\n  isCloneDisabled\n}, ref) => {\n  _s();\n  const {\n    t: translate\n  } = useTranslation();\n  const {\n    rtesContainer,\n    updateRTEContainer,\n    setIsUnSavedChanges,\n    cloneRTEContainer,\n    clearRteDetails,\n    selectedTemplate,\n    selectedTemplateTour,\n    announcementGuideMetaData,\n    toolTipGuideMetaData,\n    handleAnnouncementRTEValue,\n    handleTooltipRTEValue,\n    createWithAI,\n    currentStep,\n    ensureAnnouncementRTEContainer\n  } = useDrawerStore();\n\n  // Individual state management for each RTE\n  const [editingRTEId, setEditingRTEId] = useState(null);\n  const [toolbarVisibleRTEId, setToolbarVisibleRTEId] = useState(null);\n  const [hoveredRTEId, setHoveredRTEId] = useState(null);\n  const [expandedActionRTEId, setExpandedActionRTEId] = useState(null);\n  const [contentState, setContentState] = useState({});\n  const contentRef = useRef(\"\");\n\n  // Map to store individual refs for each RTE\n  const editorRefs = useRef(new Map());\n  const containerRefs = useRef(new Map());\n\n  // Helper function to get or create editor ref for specific RTE\n  const getEditorRef = rteId => {\n    if (!editorRefs.current.has(rteId)) {\n      editorRefs.current.set(rteId, /*#__PURE__*/React.createRef());\n    }\n    return editorRefs.current.get(rteId);\n  };\n\n  // Helper function to get or create container ref for specific RTE\n  const getContainerRef = rteId => {\n    if (!containerRefs.current.has(rteId)) {\n      containerRefs.current.set(rteId, /*#__PURE__*/React.createRef());\n    }\n    return containerRefs.current.get(rteId);\n  };\n\n  // Helper functions for content state management\n  const isContentEmpty = (content, rteId) => {\n    if (!content) return true;\n    const textContent = content.replace(/<[^>]*>/g, \"\").trim();\n    if (!textContent) return true;\n    const defaultContent = [\"<p><br></p>\", \"<p></p>\", \"<br>\", \"\"];\n    if (defaultContent.includes(content.trim())) return true;\n    return false;\n  };\n  const isContentScrollable = rteId => {\n    const editorRef = getEditorRef(rteId);\n    if (editorRef !== null && editorRef !== void 0 && editorRef.current) {\n      try {\n        var _editor;\n        const workplace = (_editor = editorRef.current.editor) === null || _editor === void 0 ? void 0 : _editor.workplace;\n        if (workplace) {\n          return workplace.scrollHeight > workplace.clientHeight;\n        }\n      } catch (error) {\n        // Silently handle any errors\n      }\n    }\n    return false;\n  };\n\n  // Update content state for dynamic icon positioning\n  const updateContentState = React.useCallback((content, rteId) => {\n    const isEmpty = isContentEmpty(content, rteId);\n    const isScrollable = isContentScrollable(rteId);\n    setContentState(prev => ({\n      ...prev,\n      [rteId]: {\n        isEmpty,\n        isScrollable\n      }\n    }));\n  }, []);\n\n  // Handle clicks outside the editor\n  useEffect(() => {\n    const handleClickOutside = event => {\n      var _document$querySelect, _document$querySelect2, _document$querySelect3, _document$querySelect4;\n      if (!editingRTEId) return;\n      const isInsideJoditPopupContent = event.target.closest(\".jodit-popup__content\") !== null;\n      const isInsideAltTextPopup = event.target.closest(\".jodit-ui-input\") !== null;\n      const isInsidePopup = (_document$querySelect = document.querySelector(\".jodit-popup\")) === null || _document$querySelect === void 0 ? void 0 : _document$querySelect.contains(event.target);\n      const isInsideJoditPopup = (_document$querySelect2 = document.querySelector(\".jodit-wysiwyg\")) === null || _document$querySelect2 === void 0 ? void 0 : _document$querySelect2.contains(event.target);\n      const isInsideWorkplacePopup = isInsideJoditPopup || ((_document$querySelect3 = document.querySelector(\".jodit-dialog__panel\")) === null || _document$querySelect3 === void 0 ? void 0 : _document$querySelect3.contains(event.target));\n      const isSelectionMarker = event.target.id.startsWith(\"jodit-selection_marker_\");\n      const isLinkPopup = (_document$querySelect4 = document.querySelector(\".jodit-ui-input__input\")) === null || _document$querySelect4 === void 0 ? void 0 : _document$querySelect4.contains(event.target);\n      const isInsideToolbarButton = event.target.closest(\".jodit-toolbar-button__button\") !== null;\n      const isInsertButton = event.target.closest(\"button[aria-pressed='false']\") !== null;\n      const currentContainerRef = getContainerRef(editingRTEId);\n      if (currentContainerRef !== null && currentContainerRef !== void 0 && currentContainerRef.current && !currentContainerRef.current.contains(event.target) && !isInsidePopup && !isInsideJoditPopup && !isInsideWorkplacePopup && !isSelectionMarker && !isLinkPopup && !isInsideToolbarButton && !isInsertButton && !isInsideJoditPopupContent && !isInsideAltTextPopup) {\n        setEditingRTEId(null);\n        setToolbarVisibleRTEId(null);\n      }\n    };\n    document.addEventListener(\"mousedown\", handleClickOutside);\n    return () => document.removeEventListener(\"mousedown\", handleClickOutside);\n  }, [editingRTEId]);\n  useEffect(() => {\n    if (editingRTEId) {\n      const editorRef = getEditorRef(editingRTEId);\n      if (editorRef !== null && editorRef !== void 0 && editorRef.current) {\n        setTimeout(() => {\n          //(editorRef.current as any).editor.focus();\n        }, 50);\n      }\n    }\n  }, [editingRTEId]);\n  const handleUpdate = useCallback((newContent, rteId, containerId) => {\n    contentRef.current = newContent;\n    const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\n    const isAITour = createWithAI && selectedTemplate === \"Tour\";\n    const isTourAnnouncement = isAITour && selectedTemplateTour === \"Announcement\";\n    const isTourBanner = isAITour && selectedTemplateTour === \"Banner\";\n    const isTourTooltip = isAITour && (selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\");\n    if (isAIAnnouncement) {\n      const currentStepIndex = currentStep - 1;\n      if (isTourAnnouncement) {\n        var _toolTipGuideMetaData, _toolTipGuideMetaData2;\n        const tooltipContainer = (_toolTipGuideMetaData = toolTipGuideMetaData[currentStepIndex]) === null || _toolTipGuideMetaData === void 0 ? void 0 : (_toolTipGuideMetaData2 = _toolTipGuideMetaData.containers) === null || _toolTipGuideMetaData2 === void 0 ? void 0 : _toolTipGuideMetaData2.find(container => container.id === containerId && container.type === \"rte\");\n        if (tooltipContainer) {\n          handleTooltipRTEValue(containerId, newContent);\n        }\n      } else {\n        var _announcementGuideMet, _announcementGuideMet2;\n        const announcementContainer = (_announcementGuideMet = announcementGuideMetaData[currentStepIndex]) === null || _announcementGuideMet === void 0 ? void 0 : (_announcementGuideMet2 = _announcementGuideMet.containers) === null || _announcementGuideMet2 === void 0 ? void 0 : _announcementGuideMet2.find(container => container.id === containerId && container.type === \"rte\");\n        if (announcementContainer) {\n          handleAnnouncementRTEValue(containerId, newContent);\n        }\n      }\n    } else if (isAITour && (isTourBanner || isTourTooltip)) {\n      var _toolTipGuideMetaData3, _toolTipGuideMetaData4;\n      const currentStepIndex = currentStep - 1;\n      const tooltipContainer = (_toolTipGuideMetaData3 = toolTipGuideMetaData[currentStepIndex]) === null || _toolTipGuideMetaData3 === void 0 ? void 0 : (_toolTipGuideMetaData4 = _toolTipGuideMetaData3.containers) === null || _toolTipGuideMetaData4 === void 0 ? void 0 : _toolTipGuideMetaData4.find(container => container.id === containerId && container.type === \"rte\");\n      if (tooltipContainer) {\n        handleTooltipRTEValue(containerId, newContent);\n      } else {\n        console.warn(`No tooltip container found for ${selectedTemplateTour} step`);\n      }\n    } else {\n      updateRTEContainer(containerId, rteId, newContent);\n    }\n    setIsUnSavedChanges(true);\n    updateContentState(newContent, rteId);\n  }, [createWithAI, selectedTemplate, selectedTemplateTour, currentStep, toolTipGuideMetaData, announcementGuideMetaData, handleTooltipRTEValue, handleAnnouncementRTEValue, updateRTEContainer, setIsUnSavedChanges, updateContentState]);\n  const handleCloneContainer = containerId => {\n    if (isCloneDisabled) {\n      return;\n    }\n    cloneRTEContainer(containerId);\n    if (onClone) {\n      onClone();\n    }\n  };\n  const handleDeleteSection = (containerId, rteId) => {\n    const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\n    if (isAIAnnouncement) {\n      clearRteDetails(containerId, rteId);\n    } else {\n      clearRteDetails(containerId, rteId);\n    }\n    handleDeleteRTESection(index);\n  };\n  const handlePaste = event => {\n    event.preventDefault();\n    const clipboardData = event.clipboardData;\n    const pastedText = clipboardData.getData(\"text/plain\");\n    const pastedHtml = clipboardData.getData(\"text/html\");\n    if (pastedHtml) {\n      const isRTEContent = pastedHtml.includes(\"<!--RTE-->\");\n      if (isRTEContent) {\n        insertContent(pastedHtml);\n      } else {\n        insertContent(pastedHtml);\n      }\n    } else {\n      insertContent(pastedText);\n    }\n  };\n  const insertContent = content => {\n    if (editingRTEId) {\n      const editorRef = getEditorRef(editingRTEId);\n      if (editorRef !== null && editorRef !== void 0 && editorRef.current) {\n        const editor = editorRef.current.editor;\n        editor.selection.insertHTML(content);\n      }\n    }\n  };\n  const toggleToolbar = rteId => {\n    if (toolbarVisibleRTEId === rteId) {\n      setToolbarVisibleRTEId(null);\n    } else {\n      setToolbarVisibleRTEId(rteId);\n    }\n  };\n  const [isRtlDirection, setIsRtlDirection] = useState(false);\n  useEffect(() => {\n    const dir = document.body.getAttribute(\"dir\") || \"ltr\";\n    setIsRtlDirection(dir.toLowerCase() === \"rtl\");\n  }, []);\n  const config = useMemo(() => ({\n    readonly: false,\n    direction: isRtlDirection ? \"rtl\" : \"ltr\",\n    language: \"en\",\n    placeholder: \"Enter your text here\",\n    toolbarSticky: false,\n    toolbarAdaptive: false,\n    toolbar: toolbarVisibleRTEId !== null,\n    showCharsCounter: false,\n    showWordsCounter: false,\n    showXPathInStatusbar: false,\n    statusbar: false,\n    pastePlain: true,\n    askBeforePasteHTML: false,\n    askBeforePasteFromWord: false,\n    height: \"auto\",\n    minHeight: toolbarVisibleRTEId !== null ? 100 : 28,\n    maxHeight: 180,\n    buttons: [\"bold\", \"italic\", \"underline\", \"strikethrough\", \"ul\", \"ol\", \"brush\", \"font\", \"fontsize\", \"link\", {\n      name: \"more\",\n      iconURL: \"https://cdn.jsdelivr.net/npm/jodit/build/icons/three-dots.svg\",\n      list: [\"source\", \"image\", \"video\", \"table\", \"align\", \"undo\", \"redo\", \"|\", \"hr\", \"eraser\", \"copyformat\", \"symbol\", \"fullsize\", \"print\", \"superscript\", \"subscript\", \"|\", \"outdent\", \"indent\", \"paragraph\"]\n    }],\n    autofocus: false,\n    popupRoot: document.body,\n    zIndex: 100000,\n    globalFullSize: false,\n    link: {\n      followOnDblClick: false,\n      processVideoLink: true,\n      processPastedLink: true,\n      openInNewTabCheckbox: true,\n      noFollowCheckbox: false,\n      modeClassName: \"input\"\n    },\n    dialog: {\n      zIndex: 100001\n    },\n    cursorAfterAutofocus: \"end\",\n    events: {\n      onPaste: handlePaste\n    },\n    controls: {\n      font: {\n        list: {\n          \"Poppins, sans-serif\": \"Poppins\",\n          \"Roboto, sans-serif\": \"Roboto\",\n          \"Comic Sans MS, sans-serif\": \"Comic Sans MS\",\n          \"Open Sans, sans-serif\": \"Open Sans\",\n          \"Calibri, sans-serif\": \"Calibri\",\n          \"Century Gothic, sans-serif\": \"Century Gothic\"\n        }\n      }\n    }\n  }), [isRtlDirection, toolbarVisibleRTEId]);\n\n  // Determine which containers to use based on guide type\n  const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\n  const isAITour = createWithAI && selectedTemplate === \"Tour\";\n  const isTourAnnouncement = isAITour && selectedTemplateTour === \"Announcement\";\n  const isTourBanner = isAITour && selectedTemplateTour === \"Banner\";\n  const isTourTooltip = isAITour && (selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\");\n  const currentStepIndex = currentStep - 1;\n  let containersToRender = [];\n  if (isAIAnnouncement && !isTourAnnouncement) {\n    containersToRender = ensureAnnouncementRTEContainer(currentStepIndex, false);\n  } else if (isAITour && (isTourBanner || isTourTooltip || isTourAnnouncement)) {\n    var _toolTipGuideMetaData5;\n    if ((_toolTipGuideMetaData5 = toolTipGuideMetaData[currentStepIndex]) !== null && _toolTipGuideMetaData5 !== void 0 && _toolTipGuideMetaData5.containers) {\n      containersToRender = toolTipGuideMetaData[currentStepIndex].containers.filter(c => c.type === \"rte\");\n    } else {\n      containersToRender = [];\n    }\n  } else {\n    containersToRender = rtesContainer;\n  }\n\n  // Initialize content state for all RTEs\n  React.useEffect(() => {\n    containersToRender.forEach(item => {\n      let rteText = \"\";\n      let rteId = \"\";\n      let id = \"\";\n      if (isAIAnnouncement && !isTourAnnouncement || isAITour && (isTourBanner || isTourTooltip || isTourAnnouncement)) {\n        rteText = item.rteBoxValue || \"\";\n        rteId = item.id;\n        id = item.id;\n      } else {\n        var _item$rtes, _item$rtes$, _item$rtes2, _item$rtes2$;\n        rteText = ((_item$rtes = item.rtes) === null || _item$rtes === void 0 ? void 0 : (_item$rtes$ = _item$rtes[0]) === null || _item$rtes$ === void 0 ? void 0 : _item$rtes$.text) || \"\";\n        rteId = (_item$rtes2 = item.rtes) === null || _item$rtes2 === void 0 ? void 0 : (_item$rtes2$ = _item$rtes2[0]) === null || _item$rtes2$ === void 0 ? void 0 : _item$rtes2$.id;\n        id = item.id;\n      }\n      if (id && rteId) {\n        updateContentState(rteText, rteId);\n      }\n    });\n  }, [isAIAnnouncement, isTourAnnouncement, isAITour, isTourBanner, isTourTooltip, updateContentState]);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: containersToRender.map(item => {\n      var _contentState$id$isEm, _contentState$id, _contentState$id$isEm2, _contentState$id2;\n      let rteText = \"\";\n      let rteId = \"\";\n      let id = \"\";\n      if (isAIAnnouncement && !isTourAnnouncement || isAITour && (isTourBanner || isTourTooltip || isTourAnnouncement)) {\n        rteText = item.rteBoxValue || \"\";\n        rteId = item.id;\n        id = item.id;\n      } else {\n        var _item$rtes3, _item$rtes3$, _item$rtes4, _item$rtes4$;\n        rteText = ((_item$rtes3 = item.rtes) === null || _item$rtes3 === void 0 ? void 0 : (_item$rtes3$ = _item$rtes3[0]) === null || _item$rtes3$ === void 0 ? void 0 : _item$rtes3$.text) || \"\";\n        rteId = (_item$rtes4 = item.rtes) === null || _item$rtes4 === void 0 ? void 0 : (_item$rtes4$ = _item$rtes4[0]) === null || _item$rtes4$ === void 0 ? void 0 : _item$rtes4$.id;\n        id = item.id;\n      }\n      if (!id) return null;\n      const currentContainerRef = getContainerRef(id);\n      const currentEditorRef = getEditorRef(id);\n\n      // Dynamic CSS for this specific RTE\n      const dynamicCSS = `\n                        .jodit-add-new-line {\n                            display: none !important;\n                        }\n                        #rte-${id} .jodit-wysiwyg {\n                            color: #000000 !important;\n                            background-color: #ffffff !important;\n                            line-height: 1.4 !important;\n                            padding: 8px !important;\n                        }\n                        #rte-${id} .jodit-workplace {\n                            min-height: ${toolbarVisibleRTEId === id ? \"100px\" : \"28px\"} !important;\n                            max-height: 180px !important;\n                            overflow-y: auto !important;\n                            line-height: 1.4 !important;\n                        }\n                        #rte-${id} .jodit-container {\n                            border: none !important;\n                        }\n                        #rte-${id} .jodit-workplace::-webkit-scrollbar {\n                            width: 6px !important;\n                        }\n                        #rte-${id} .jodit-workplace::-webkit-scrollbar-track {\n                            background: #f1f1f1 !important;\n                            border-radius: 3px !important;\n                        }\n                        #rte-${id} .jodit-workplace::-webkit-scrollbar-thumb {\n                            background: #c1c1c1 !important;\n                            border-radius: 3px !important;\n                        }\n                        #rte-${id} .jodit-workplace::-webkit-scrollbar-thumb:hover {\n                            background: #a8a8a8 !important;\n                        }\n                        #rte-${id} .jodit-wysiwyg p {\n                            color: #000000 !important;\n                            margin: 0 0 4px 0 !important;\n                            padding: 0 !important;\n                            line-height: 1.4 !important;\n                        }\n                        @keyframes expandIn {\n                            from {\n                                opacity: 0;\n                                transform: scale(0.8);\n                            }\n                            to {\n                                opacity: 1;\n                                transform: scale(1);\n                            }\n                        }\n                    `;\n      return /*#__PURE__*/_jsxDEV(Box, {\n        ref: currentContainerRef,\n        onMouseEnter: () => setHoveredRTEId(id),\n        onMouseLeave: () => {\n          setHoveredRTEId(null);\n          setExpandedActionRTEId(null);\n        },\n        sx: {\n          display: \"flex\",\n          alignItems: \"center\",\n          position: \"relative\",\n          \"&:hover .rte-expanding-button\": {\n            opacity: \"1 !important\",\n            visibility: \"visible !important\"\n          },\n          \"&.qadpt-rte:hover .rte-expanding-button\": {\n            opacity: \"1 !important\",\n            visibility: \"visible !important\"\n          },\n          \"& .jodit-status-bar-link\": {\n            display: \"none !important\"\n          },\n          \"& .jodit-editor\": {\n            fontFamily: \"'Roboto', sans-serif !important\"\n          },\n          \".jodit-editor span\": {\n            fontFamily: \"'Roboto', sans-serif !important\"\n          },\n          \".jodit-toolbar-button button\": {\n            minWidth: \"29px !important\"\n          },\n          \".jodit-react-container\": {\n            width: \"100%\",\n            whiteSpace: \"pre-wrap\",\n            wordBreak: \"break-word\"\n          },\n          \".jodit.jodit-dialog\": {\n            position: \"fixed !important\",\n            zIndex: \"100001 !important\",\n            top: \"50% !important\",\n            left: \"50% !important\",\n            transform: \"translate(-50%, -50%) !important\"\n          },\n          \".jodit-dialog .jodit-dialog__panel\": {\n            position: \"relative !important\",\n            top: \"auto !important\",\n            left: \"auto !important\",\n            transform: \"none !important\",\n            maxWidth: \"400px !important\",\n            background: \"white !important\",\n            border: \"1px solid #ccc !important\",\n            borderRadius: \"4px !important\",\n            boxShadow: \"0 4px 12px rgba(0,0,0,0.15) !important\"\n          },\n          \".jodit-dialog_alert\": {\n            position: \"fixed !important\",\n            zIndex: \"100001 !important\",\n            top: \"50% !important\",\n            left: \"50% !important\",\n            transform: \"translate(-50%, -50%) !important\"\n          }\n        },\n        className: \"qadpt-rte\",\n        children: [!(selectedTemplate === \"Banner\" || selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\") && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: \"absolute\",\n            top: toolbarVisibleRTEId === id ? \"45px\" : ((_contentState$id$isEm = (_contentState$id = contentState[id]) === null || _contentState$id === void 0 ? void 0 : _contentState$id.isEmpty) !== null && _contentState$id$isEm !== void 0 ? _contentState$id$isEm : isContentEmpty(rteText, id)) ? \"50%\" : \"8px\",\n            right: \"8px\",\n            transform: toolbarVisibleRTEId === id ? \"none\" : ((_contentState$id$isEm2 = (_contentState$id2 = contentState[id]) === null || _contentState$id2 === void 0 ? void 0 : _contentState$id2.isEmpty) !== null && _contentState$id$isEm2 !== void 0 ? _contentState$id$isEm2 : isContentEmpty(rteText, id)) ? \"translateY(-50%)\" : \"none\",\n            zIndex: 1003,\n            opacity: hoveredRTEId === id ? 1 : 0,\n            visibility: hoveredRTEId === id ? \"visible\" : \"hidden\",\n            transition: \"opacity 0.2s ease-in-out, visibility 0.2s ease-in-out\",\n            pointerEvents: \"auto\"\n          },\n          className: \"rte-expanding-button\",\n          onMouseEnter: () => setExpandedActionRTEId(id),\n          onMouseLeave: () => setExpandedActionRTEId(null),\n          children: expandedActionRTEId === id ?\n          /*#__PURE__*/\n          // Expanded state - show all three buttons\n          _jsxDEV(\"div\", {\n            style: {\n              display: \"flex\",\n              gap: \"4px\",\n              backgroundColor: \"rgba(255, 255, 255, 0.95)\",\n              border: \"1px solid #e0e0e0\",\n              borderRadius: \"6px\",\n              padding: \"4px\",\n              boxShadow: \"0 4px 12px rgba(0, 0, 0, 0.15)\",\n              animation: \"expandIn 0.2s ease-out forwards\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(IconButton, {\n              size: \"small\",\n              onClick: () => handleCloneContainer(item.id),\n              disabled: isCloneDisabled,\n              title: isCloneDisabled ? translate(\"Maximum limit of 3 Rich Text sections reached\") : translate(\"Clone Section\"),\n              sx: {\n                width: \"24px\",\n                height: \"24px\",\n                backgroundColor: \"transparent\",\n                \"&:hover\": {\n                  backgroundColor: \"rgba(59, 130, 246, 0.1)\",\n                  transform: \"scale(1.05)\"\n                },\n                \"&:disabled\": {\n                  opacity: 0.5,\n                  cursor: \"not-allowed\"\n                },\n                transition: \"all 0.15s ease-in-out\",\n                svg: {\n                  height: \"16px\",\n                  width: \"16px\",\n                  path: {\n                    fill: \"var(--primarycolor)\"\n                  }\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: copyicon\n                },\n                style: {\n                  height: \"16px\",\n                  width: \"16px\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 647,\n                columnNumber: 25\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 616,\n              columnNumber: 23\n            }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n              size: \"small\",\n              onClick: () => handleDeleteSection(item.id, rteId),\n              title: translate(\"Delete Section\"),\n              sx: {\n                width: \"24px\",\n                height: \"24px\",\n                backgroundColor: \"transparent\",\n                \"&:hover\": {\n                  backgroundColor: \"rgba(239, 68, 68, 0.1)\",\n                  transform: \"scale(1.05)\"\n                },\n                \"&:hover span\": {\n                  filter: \"brightness(0) saturate(100%) invert(27%) sepia(51%) saturate(2878%) hue-rotate(346deg) brightness(104%) contrast(97%) !important\"\n                },\n                transition: \"all 0.15s ease-in-out\"\n              },\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                width: \"16\",\n                height: \"16\",\n                viewBox: \"0 0 16 16\",\n                fill: \"none\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M5 2V1.5C5 1.22386 5.22386 1 5.5 1H10.5C10.7761 1 11 1.22386 11 1.5V2H13.5C13.7761 2 14 2.22386 14 2.5C14 2.77614 13.7761 3 13.5 3H13V12.5C13 13.3284 12.3284 14 11.5 14H4.5C3.67157 14 3 13.3284 3 12.5V3H2.5C2.22386 3 2 2.77614 2 2.5C2 2.22386 2.22386 2 2.5 2H5ZM4 3V12.5C4 12.7761 4.22386 13 4.5 13H11.5C11.7761 13 12 12.7761 12 12.5V3H4ZM6 5.5C6 5.22386 6.22386 5 6.5 5C6.77614 5 7 5.22386 7 5.5V10.5C7 10.7761 6.77614 11 6.5 11C6.22386 11 6 10.7761 6 10.5V5.5ZM9 5.5C9 5.22386 9.22386 5 9.5 5C9.77614 5 10 5.22386 10 5.5V10.5C10 10.7761 9.77614 11 9.5 11C9.22386 11 9 10.7761 9 10.5V5.5Z\",\n                  fill: \"#ef4444\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 677,\n                  columnNumber: 27\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 676,\n                columnNumber: 25\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 657,\n              columnNumber: 23\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: \"1px\",\n                height: \"20px\",\n                backgroundColor: \"#e0e0e0\",\n                margin: \"2px 2px\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 685,\n              columnNumber: 23\n            }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n              size: \"small\",\n              onClick: e => {\n                e.stopPropagation();\n                toggleToolbar(id);\n              },\n              title: translate(\"Toggle Toolbar\"),\n              sx: {\n                width: \"24px\",\n                height: \"24px\",\n                backgroundColor: \"transparent\",\n                \"&:hover\": {\n                  backgroundColor: \"rgba(107, 114, 128, 0.1)\",\n                  transform: \"scale(1.05)\"\n                },\n                transition: \"all 0.15s ease-in-out\"\n              },\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: editicon\n                },\n                style: {\n                  height: \"16px\",\n                  width: \"16px\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 706,\n                columnNumber: 25\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 688,\n              columnNumber: 23\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 603,\n            columnNumber: 21\n          }, this) :\n          /*#__PURE__*/\n          // Collapsed state - show single more button\n          _jsxDEV(IconButton, {\n            size: \"small\",\n            sx: {\n              width: \"28px\",\n              height: \"28px\",\n              backgroundColor: \"rgba(255, 255, 255, 0.95)\",\n              border: \"1px solid #e0e0e0\",\n              borderRadius: \"6px\",\n              boxShadow: \"0 2px 6px rgba(0, 0, 0, 0.1)\",\n              \"&:hover\": {\n                backgroundColor: \"rgba(255, 255, 255, 1)\",\n                boxShadow: \"0 4px 12px rgba(0, 0, 0, 0.15)\",\n                transform: \"scale(1.05)\"\n              },\n              transition: \"all 0.2s ease-in-out\"\n            },\n            title: translate(\"More Actions\"),\n            children: /*#__PURE__*/_jsxDEV(MoreHorizIcon, {\n              sx: {\n                fontSize: \"18px\",\n                color: \"#6b7280\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 732,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 714,\n            columnNumber: 21\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 575,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          id: `rte-${id}`,\n          style: {\n            width: \"100%\",\n            position: \"relative\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"style\", {\n            children: dynamicCSS\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 740,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(JoditEditor, {\n            ref: currentEditorRef,\n            value: rteText,\n            config: config,\n            onChange: newContent => handleUpdate(newContent, rteId, id)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 741,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 739,\n          columnNumber: 15\n        }, this)]\n      }, id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 505,\n        columnNumber: 13\n      }, this);\n    })\n  }, void 0, false);\n}, \"H0bWCy8DDzOKJMvn2NZbWEM4hz4=\", false, function () {\n  return [useTranslation, useDrawerStore];\n})), \"H0bWCy8DDzOKJMvn2NZbWEM4hz4=\", false, function () {\n  return [useTranslation, useDrawerStore];\n});\n_c2 = RTEsection;\nexport default RTEsection;\nvar _c, _c2;\n$RefreshReg$(_c, \"RTEsection$forwardRef\");\n$RefreshReg$(_c2, \"RTEsection\");", "map": {"version": 3, "names": ["_jsxFileName", "_s", "$RefreshSig$", "React", "useState", "useEffect", "useRef", "forwardRef", "useMemo", "useCallback", "Box", "IconButton", "JoditEditor", "MoreHorizIcon", "useDrawerStore", "copyicon", "editicon", "useTranslation", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "RTEsection", "_c", "textBoxRef", "isBanner", "handleDeleteRTESection", "index", "guidePopUpRef", "onClone", "isCloneDisabled", "ref", "t", "translate", "rtesContainer", "updateRTEContainer", "setIsUnSavedChanges", "cloneRTEContainer", "clearRteDetails", "selectedTemplate", "selectedTemplateTour", "announcementGuideMetaData", "toolTipGuideMetaData", "handleAnnouncementRTEValue", "handleTooltipRTEValue", "createWithAI", "currentStep", "ensureAnnouncementRTEContainer", "editingRTEId", "setEditingRTEId", "toolbarVisibleRTEId", "setToolbarVisibleRTEId", "hoveredRTEId", "setHoveredRTEId", "expandedActionRTEId", "setExpandedActionRTEId", "contentState", "setContentState", "contentRef", "editor<PERSON><PERSON><PERSON>", "Map", "containerRefs", "getEditorRef", "rteId", "current", "has", "set", "createRef", "get", "getContainerRef", "isContentEmpty", "content", "textContent", "replace", "trim", "defaultContent", "includes", "isContentScrollable", "editor<PERSON><PERSON>", "_editor", "workplace", "editor", "scrollHeight", "clientHeight", "error", "updateContentState", "isEmpty", "isScrollable", "prev", "handleClickOutside", "event", "_document$querySelect", "_document$querySelect2", "_document$querySelect3", "_document$querySelect4", "isInsideJoditPopupContent", "target", "closest", "isInsideAltTextPopup", "isInsidePopup", "document", "querySelector", "contains", "isInsideJoditPopup", "isInsideWorkplacePopup", "isSelectionMarker", "id", "startsWith", "isLinkPopup", "isInsideToolbarButton", "isInsertButton", "currentContainerRef", "addEventListener", "removeEventListener", "setTimeout", "handleUpdate", "newContent", "containerId", "isAIAnnouncement", "isAITour", "isTourAnnouncement", "isTourBanner", "isTourTooltip", "currentStepIndex", "_toolTipGuideMetaData", "_toolTipGuideMetaData2", "tooltipContainer", "containers", "find", "container", "type", "_announcementGuideMet", "_announcementGuideMet2", "announcementC<PERSON>r", "_toolTipGuideMetaData3", "_toolTipGuideMetaData4", "console", "warn", "handleCloneContainer", "handleDeleteSection", "handlePaste", "preventDefault", "clipboardData", "pastedText", "getData", "pastedHtml", "isRTEContent", "insertContent", "selection", "insertHTML", "toggleToolbar", "isRtlDirection", "setIsRtlDirection", "dir", "body", "getAttribute", "toLowerCase", "config", "readonly", "direction", "language", "placeholder", "toolbarSticky", "toolbarAdaptive", "toolbar", "showCharsCounter", "showWordsCounter", "showXPathInStatusbar", "statusbar", "paste<PERSON>lain", "askBeforePasteHTML", "askBeforePasteFromWord", "height", "minHeight", "maxHeight", "buttons", "name", "iconURL", "list", "autofocus", "popupRoot", "zIndex", "globalFullSize", "link", "followOnDblClick", "processVideoLink", "processPastedLink", "openInNewTabCheckbox", "noFollowCheckbox", "modeClassName", "dialog", "cursorAfterAutofocus", "events", "onPaste", "controls", "font", "containersToRender", "_toolTipGuideMetaData5", "filter", "c", "for<PERSON>ach", "item", "rteText", "rteBoxValue", "_item$rtes", "_item$rtes$", "_item$rtes2", "_item$rtes2$", "rtes", "text", "children", "map", "_contentState$id$isEm", "_contentState$id", "_contentState$id$isEm2", "_contentState$id2", "_item$rtes3", "_item$rtes3$", "_item$rtes4", "_item$rtes4$", "currentEditorRef", "dynamicCSS", "onMouseEnter", "onMouseLeave", "sx", "display", "alignItems", "position", "opacity", "visibility", "fontFamily", "min<PERSON><PERSON><PERSON>", "width", "whiteSpace", "wordBreak", "top", "left", "transform", "max<PERSON><PERSON><PERSON>", "background", "border", "borderRadius", "boxShadow", "className", "style", "right", "transition", "pointerEvents", "gap", "backgroundColor", "padding", "animation", "size", "onClick", "disabled", "title", "cursor", "svg", "path", "fill", "dangerouslySetInnerHTML", "__html", "fileName", "lineNumber", "columnNumber", "viewBox", "xmlns", "d", "margin", "e", "stopPropagation", "fontSize", "color", "value", "onChange", "_c2", "$RefreshReg$"], "sources": ["E:/quixy/newadopt/quickadapt/QuickAdaptExtension/src/components/guideSetting/PopupSections/RTEsection.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport React, { useState, useEffect, useRef, forwardRef, useMemo, useCallback } from \"react\"\r\nimport { Box, IconButton } from \"@mui/material\"\r\nimport JoditEditor from \"jodit-react\"\r\nimport MoreHorizIcon from \"@mui/icons-material/MoreHoriz\"\r\nimport useDrawerStore from \"../../../store/drawerStore\"\r\nimport { copyicon, editicon } from \"../../../assets/icons/icons\"\r\nimport { useTranslation } from \"react-i18next\"\r\n\r\ninterface RTEsectionProps {\r\n  textBoxRef: React.MutableRefObject<HTMLDivElement | null>\r\n  guidePopUpRef: React.MutableRefObject<HTMLDivElement | null>\r\n  isBanner: boolean\r\n  handleDeleteRTESection: (params: number) => void\r\n  index: number\r\n  onClone?: () => void\r\n  isCloneDisabled?: boolean\r\n}\r\n\r\nconst RTEsection: React.FC<RTEsectionProps> = forwardRef(\r\n  ({ textBoxRef, isBanner, handleDeleteRTESection, index, guidePopUpRef, onClone, isCloneDisabled }, ref) => {\r\n    const { t: translate } = useTranslation()\r\n    const {\r\n      rtesContainer,\r\n      updateRTEContainer,\r\n      setIsUnSavedChanges,\r\n      cloneRTEContainer,\r\n      clearRteDetails,\r\n      selectedTemplate,\r\n      selectedTemplateTour,\r\n      announcementGuideMetaData,\r\n      toolTipGuideMetaData,\r\n      handleAnnouncementRTEValue,\r\n      handleTooltipRTEValue,\r\n      createWithAI,\r\n      currentStep,\r\n      ensureAnnouncementRTEContainer,\r\n    } = useDrawerStore()\r\n\r\n    // Individual state management for each RTE\r\n    const [editingRTEId, setEditingRTEId] = useState<string | null>(null)\r\n    const [toolbarVisibleRTEId, setToolbarVisibleRTEId] = useState<string | null>(null)\r\n    const [hoveredRTEId, setHoveredRTEId] = useState<string | null>(null)\r\n    const [expandedActionRTEId, setExpandedActionRTEId] = useState<string | null>(null)\r\n    const [contentState, setContentState] = useState<{ [key: string]: { isEmpty: boolean; isScrollable: boolean } }>({})\r\n    const contentRef = useRef<string>(\"\")\r\n\r\n    // Map to store individual refs for each RTE\r\n    const editorRefs = useRef<Map<string, React.RefObject<any>>>(new Map())\r\n    const containerRefs = useRef<Map<string, React.RefObject<HTMLDivElement>>>(new Map())\r\n\r\n    // Helper function to get or create editor ref for specific RTE\r\n    const getEditorRef = (rteId: string) => {\r\n      if (!editorRefs.current.has(rteId)) {\r\n        editorRefs.current.set(rteId, React.createRef())\r\n      }\r\n      return editorRefs.current.get(rteId)\r\n    }\r\n\r\n    // Helper function to get or create container ref for specific RTE\r\n    const getContainerRef = (rteId: string) => {\r\n      if (!containerRefs.current.has(rteId)) {\r\n        containerRefs.current.set(rteId, React.createRef())\r\n      }\r\n      return containerRefs.current.get(rteId)\r\n    }\r\n\r\n    // Helper functions for content state management\r\n    const isContentEmpty = (content: string, rteId: string) => {\r\n      if (!content) return true\r\n      const textContent = content.replace(/<[^>]*>/g, \"\").trim()\r\n      if (!textContent) return true\r\n      const defaultContent = [\"<p><br></p>\", \"<p></p>\", \"<br>\", \"\"]\r\n      if (defaultContent.includes(content.trim())) return true\r\n      return false\r\n    }\r\n\r\n    const isContentScrollable = (rteId: string) => {\r\n      const editorRef = getEditorRef(rteId)\r\n      if (editorRef?.current) {\r\n        try {\r\n          const workplace = (editorRef.current as any).editor?.workplace\r\n          if (workplace) {\r\n            return workplace.scrollHeight > workplace.clientHeight\r\n          }\r\n        } catch (error) {\r\n          // Silently handle any errors\r\n        }\r\n      }\r\n      return false\r\n    }\r\n\r\n    // Update content state for dynamic icon positioning\r\n    const updateContentState = React.useCallback((content: string, rteId: string) => {\r\n      const isEmpty = isContentEmpty(content, rteId)\r\n      const isScrollable = isContentScrollable(rteId)\r\n\r\n      setContentState((prev) => ({\r\n        ...prev,\r\n        [rteId]: { isEmpty, isScrollable },\r\n      }))\r\n    }, [])\r\n\r\n    // Handle clicks outside the editor\r\n    useEffect(() => {\r\n      const handleClickOutside = (event: MouseEvent) => {\r\n        if (!editingRTEId) return\r\n\r\n        const isInsideJoditPopupContent = (event.target as HTMLElement).closest(\".jodit-popup__content\") !== null\r\n        const isInsideAltTextPopup = (event.target as HTMLElement).closest(\".jodit-ui-input\") !== null\r\n        const isInsidePopup = document.querySelector(\".jodit-popup\")?.contains(event.target as Node)\r\n        const isInsideJoditPopup = document.querySelector(\".jodit-wysiwyg\")?.contains(event.target as Node)\r\n        const isInsideWorkplacePopup =\r\n          isInsideJoditPopup || document.querySelector(\".jodit-dialog__panel\")?.contains(event.target as Node)\r\n        const isSelectionMarker = (event.target as HTMLElement).id.startsWith(\"jodit-selection_marker_\")\r\n        const isLinkPopup = document.querySelector(\".jodit-ui-input__input\")?.contains(event.target as Node)\r\n        const isInsideToolbarButton = (event.target as HTMLElement).closest(\".jodit-toolbar-button__button\") !== null\r\n        const isInsertButton = (event.target as HTMLElement).closest(\"button[aria-pressed='false']\") !== null\r\n\r\n        const currentContainerRef = getContainerRef(editingRTEId)\r\n\r\n        if (\r\n          currentContainerRef?.current &&\r\n          !currentContainerRef.current.contains(event.target as Node) &&\r\n          !isInsidePopup &&\r\n          !isInsideJoditPopup &&\r\n          !isInsideWorkplacePopup &&\r\n          !isSelectionMarker &&\r\n          !isLinkPopup &&\r\n          !isInsideToolbarButton &&\r\n          !isInsertButton &&\r\n          !isInsideJoditPopupContent &&\r\n          !isInsideAltTextPopup\r\n        ) {\r\n          setEditingRTEId(null)\r\n          setToolbarVisibleRTEId(null)\r\n        }\r\n      }\r\n\r\n      document.addEventListener(\"mousedown\", handleClickOutside)\r\n      return () => document.removeEventListener(\"mousedown\", handleClickOutside)\r\n    }, [editingRTEId])\r\n\r\n    useEffect(() => {\r\n      if (editingRTEId) {\r\n        const editorRef = getEditorRef(editingRTEId)\r\n        if (editorRef?.current) {\r\n          setTimeout(() => {\r\n            //(editorRef.current as any).editor.focus();\r\n          }, 50)\r\n        }\r\n      }\r\n    }, [editingRTEId])\r\n\r\n    const handleUpdate = useCallback(\r\n      (newContent: string, rteId: string, containerId: string) => {\r\n        contentRef.current = newContent\r\n\r\n        const isAIAnnouncement =\r\n          createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\")\r\n        const isAITour = createWithAI && selectedTemplate === \"Tour\"\r\n        const isTourAnnouncement = isAITour && selectedTemplateTour === \"Announcement\"\r\n        const isTourBanner = isAITour && selectedTemplateTour === \"Banner\"\r\n        const isTourTooltip = isAITour && (selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\")\r\n\r\n        if (isAIAnnouncement) {\r\n          const currentStepIndex = currentStep - 1\r\n\r\n          if (isTourAnnouncement) {\r\n            const tooltipContainer = toolTipGuideMetaData[currentStepIndex]?.containers?.find(\r\n              (container: any) => container.id === containerId && container.type === \"rte\",\r\n            )\r\n\r\n            if (tooltipContainer) {\r\n              handleTooltipRTEValue(containerId, newContent)\r\n            }\r\n          } else {\r\n            const announcementContainer = announcementGuideMetaData[currentStepIndex]?.containers?.find(\r\n              (container: any) => container.id === containerId && container.type === \"rte\",\r\n            )\r\n\r\n            if (announcementContainer) {\r\n              handleAnnouncementRTEValue(containerId, newContent)\r\n            }\r\n          }\r\n        } else if (isAITour && (isTourBanner || isTourTooltip)) {\r\n          const currentStepIndex = currentStep - 1\r\n          const tooltipContainer = toolTipGuideMetaData[currentStepIndex]?.containers?.find(\r\n            (container: any) => container.id === containerId && container.type === \"rte\",\r\n          )\r\n\r\n          if (tooltipContainer) {\r\n            handleTooltipRTEValue(containerId, newContent)\r\n          } else {\r\n            console.warn(`No tooltip container found for ${selectedTemplateTour} step`)\r\n          }\r\n        } else {\r\n          updateRTEContainer(containerId, rteId, newContent)\r\n        }\r\n\r\n        setIsUnSavedChanges(true)\r\n        updateContentState(newContent, rteId)\r\n      },\r\n      [\r\n        createWithAI,\r\n        selectedTemplate,\r\n        selectedTemplateTour,\r\n        currentStep,\r\n        toolTipGuideMetaData,\r\n        announcementGuideMetaData,\r\n        handleTooltipRTEValue,\r\n        handleAnnouncementRTEValue,\r\n        updateRTEContainer,\r\n        setIsUnSavedChanges,\r\n        updateContentState,\r\n      ],\r\n    )\r\n\r\n    const handleCloneContainer = (containerId: string) => {\r\n      if (isCloneDisabled) {\r\n        return\r\n      }\r\n      cloneRTEContainer(containerId)\r\n      if (onClone) {\r\n        onClone()\r\n      }\r\n    }\r\n\r\n    const handleDeleteSection = (containerId: string, rteId: string) => {\r\n      const isAIAnnouncement =\r\n        createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\")\r\n\r\n      if (isAIAnnouncement) {\r\n        clearRteDetails(containerId, rteId)\r\n      } else {\r\n        clearRteDetails(containerId, rteId)\r\n      }\r\n\r\n      handleDeleteRTESection(index)\r\n    }\r\n\r\n    const handlePaste = (event: React.ClipboardEvent<HTMLDivElement>) => {\r\n      event.preventDefault()\r\n\r\n      const clipboardData = event.clipboardData\r\n      const pastedText = clipboardData.getData(\"text/plain\")\r\n      const pastedHtml = clipboardData.getData(\"text/html\")\r\n\r\n      if (pastedHtml) {\r\n        const isRTEContent = pastedHtml.includes(\"<!--RTE-->\")\r\n        if (isRTEContent) {\r\n          insertContent(pastedHtml)\r\n        } else {\r\n          insertContent(pastedHtml)\r\n        }\r\n      } else {\r\n        insertContent(pastedText)\r\n      }\r\n    }\r\n\r\n    const insertContent = (content: string) => {\r\n      if (editingRTEId) {\r\n        const editorRef = getEditorRef(editingRTEId)\r\n        if (editorRef?.current) {\r\n          const editor = (editorRef.current as any).editor\r\n          editor.selection.insertHTML(content)\r\n        }\r\n      }\r\n    }\r\n\r\n    const toggleToolbar = (rteId: string) => {\r\n      if (toolbarVisibleRTEId === rteId) {\r\n        setToolbarVisibleRTEId(null)\r\n      } else {\r\n        setToolbarVisibleRTEId(rteId)\r\n      }\r\n    }\r\n\r\n    const [isRtlDirection, setIsRtlDirection] = useState<boolean>(false)\r\n    useEffect(() => {\r\n      const dir = document.body.getAttribute(\"dir\") || \"ltr\"\r\n      setIsRtlDirection(dir.toLowerCase() === \"rtl\")\r\n    }, [])\r\n\r\n    const config = useMemo(\r\n      (): any => ({\r\n        readonly: false,\r\n        direction: isRtlDirection ? (\"rtl\" as const) : (\"ltr\" as const),\r\n        language: \"en\",\r\n        placeholder: \"Enter your text here\",\r\n        toolbarSticky: false,\r\n        toolbarAdaptive: false,\r\n        toolbar: toolbarVisibleRTEId !== null,\r\n        showCharsCounter: false,\r\n        showWordsCounter: false,\r\n        showXPathInStatusbar: false,\r\n        statusbar: false,\r\n        pastePlain: true,\r\n        askBeforePasteHTML: false,\r\n        askBeforePasteFromWord: false,\r\n        height: \"auto\",\r\n        minHeight: toolbarVisibleRTEId !== null ? 100 : 28,\r\n        maxHeight: 180,\r\n        buttons: [\r\n          \"bold\",\r\n          \"italic\",\r\n          \"underline\",\r\n          \"strikethrough\",\r\n          \"ul\",\r\n          \"ol\",\r\n          \"brush\",\r\n          \"font\",\r\n          \"fontsize\",\r\n          \"link\",\r\n          {\r\n            name: \"more\",\r\n            iconURL: \"https://cdn.jsdelivr.net/npm/jodit/build/icons/three-dots.svg\",\r\n            list: [\r\n              \"source\",\r\n              \"image\",\r\n              \"video\",\r\n              \"table\",\r\n              \"align\",\r\n              \"undo\",\r\n              \"redo\",\r\n              \"|\",\r\n              \"hr\",\r\n              \"eraser\",\r\n              \"copyformat\",\r\n              \"symbol\",\r\n              \"fullsize\",\r\n              \"print\",\r\n              \"superscript\",\r\n              \"subscript\",\r\n              \"|\",\r\n              \"outdent\",\r\n              \"indent\",\r\n              \"paragraph\",\r\n            ],\r\n          },\r\n        ],\r\n        autofocus: false,\r\n        popupRoot: document.body,\r\n        zIndex: 100000,\r\n        globalFullSize: false,\r\n        link: {\r\n          followOnDblClick: false,\r\n          processVideoLink: true,\r\n          processPastedLink: true,\r\n          openInNewTabCheckbox: true,\r\n          noFollowCheckbox: false,\r\n          modeClassName: \"input\" as const,\r\n        },\r\n        dialog: {\r\n          zIndex: 100001,\r\n        },\r\n        cursorAfterAutofocus: \"end\" as const,\r\n        events: {\r\n          onPaste: handlePaste,\r\n        },\r\n        controls: {\r\n          font: {\r\n            list: {\r\n              \"Poppins, sans-serif\": \"Poppins\",\r\n              \"Roboto, sans-serif\": \"Roboto\",\r\n              \"Comic Sans MS, sans-serif\": \"Comic Sans MS\",\r\n              \"Open Sans, sans-serif\": \"Open Sans\",\r\n              \"Calibri, sans-serif\": \"Calibri\",\r\n              \"Century Gothic, sans-serif\": \"Century Gothic\",\r\n            },\r\n          },\r\n        },\r\n      }),\r\n      [isRtlDirection, toolbarVisibleRTEId],\r\n    )\r\n\r\n    // Determine which containers to use based on guide type\r\n    const isAIAnnouncement =\r\n      createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\")\r\n    const isAITour = createWithAI && selectedTemplate === \"Tour\"\r\n    const isTourAnnouncement = isAITour && selectedTemplateTour === \"Announcement\"\r\n    const isTourBanner = isAITour && selectedTemplateTour === \"Banner\"\r\n    const isTourTooltip = isAITour && (selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\")\r\n    const currentStepIndex = currentStep - 1\r\n\r\n    let containersToRender: any[] = []\r\n\r\n    if (isAIAnnouncement && !isTourAnnouncement) {\r\n      containersToRender = ensureAnnouncementRTEContainer(currentStepIndex, false)\r\n    } else if (isAITour && (isTourBanner || isTourTooltip || isTourAnnouncement)) {\r\n      if (toolTipGuideMetaData[currentStepIndex]?.containers) {\r\n        containersToRender = toolTipGuideMetaData[currentStepIndex].containers.filter((c) => c.type === \"rte\")\r\n      } else {\r\n        containersToRender = []\r\n      }\r\n    } else {\r\n      containersToRender = rtesContainer\r\n    }\r\n\r\n    // Initialize content state for all RTEs\r\n    React.useEffect(() => {\r\n      containersToRender.forEach((item: any) => {\r\n        let rteText = \"\"\r\n        let rteId = \"\"\r\n        let id = \"\"\r\n\r\n        if (\r\n          (isAIAnnouncement && !isTourAnnouncement) ||\r\n          (isAITour && (isTourBanner || isTourTooltip || isTourAnnouncement))\r\n        ) {\r\n          rteText = item.rteBoxValue || \"\"\r\n          rteId = item.id\r\n          id = item.id\r\n        } else {\r\n          rteText = item.rtes?.[0]?.text || \"\"\r\n          rteId = item.rtes?.[0]?.id\r\n          id = item.id\r\n        }\r\n\r\n        if (id && rteId) {\r\n          updateContentState(rteText, rteId)\r\n        }\r\n      })\r\n    }, [isAIAnnouncement, isTourAnnouncement, isAITour, isTourBanner, isTourTooltip, updateContentState])\r\n\r\n    return (\r\n      <>\r\n        {containersToRender.map((item: any) => {\r\n          let rteText = \"\"\r\n          let rteId = \"\"\r\n          let id = \"\"\r\n\r\n          if (\r\n            (isAIAnnouncement && !isTourAnnouncement) ||\r\n            (isAITour && (isTourBanner || isTourTooltip || isTourAnnouncement))\r\n          ) {\r\n            rteText = item.rteBoxValue || \"\"\r\n            rteId = item.id\r\n            id = item.id\r\n          } else {\r\n            rteText = item.rtes?.[0]?.text || \"\"\r\n            rteId = item.rtes?.[0]?.id\r\n            id = item.id\r\n          }\r\n\r\n          if (!id) return null\r\n\r\n          const currentContainerRef = getContainerRef(id)\r\n          const currentEditorRef = getEditorRef(id)\r\n\r\n          // Dynamic CSS for this specific RTE\r\n          const dynamicCSS = `\r\n                        .jodit-add-new-line {\r\n                            display: none !important;\r\n                        }\r\n                        #rte-${id} .jodit-wysiwyg {\r\n                            color: #000000 !important;\r\n                            background-color: #ffffff !important;\r\n                            line-height: 1.4 !important;\r\n                            padding: 8px !important;\r\n                        }\r\n                        #rte-${id} .jodit-workplace {\r\n                            min-height: ${toolbarVisibleRTEId === id ? \"100px\" : \"28px\"} !important;\r\n                            max-height: 180px !important;\r\n                            overflow-y: auto !important;\r\n                            line-height: 1.4 !important;\r\n                        }\r\n                        #rte-${id} .jodit-container {\r\n                            border: none !important;\r\n                        }\r\n                        #rte-${id} .jodit-workplace::-webkit-scrollbar {\r\n                            width: 6px !important;\r\n                        }\r\n                        #rte-${id} .jodit-workplace::-webkit-scrollbar-track {\r\n                            background: #f1f1f1 !important;\r\n                            border-radius: 3px !important;\r\n                        }\r\n                        #rte-${id} .jodit-workplace::-webkit-scrollbar-thumb {\r\n                            background: #c1c1c1 !important;\r\n                            border-radius: 3px !important;\r\n                        }\r\n                        #rte-${id} .jodit-workplace::-webkit-scrollbar-thumb:hover {\r\n                            background: #a8a8a8 !important;\r\n                        }\r\n                        #rte-${id} .jodit-wysiwyg p {\r\n                            color: #000000 !important;\r\n                            margin: 0 0 4px 0 !important;\r\n                            padding: 0 !important;\r\n                            line-height: 1.4 !important;\r\n                        }\r\n                        @keyframes expandIn {\r\n                            from {\r\n                                opacity: 0;\r\n                                transform: scale(0.8);\r\n                            }\r\n                            to {\r\n                                opacity: 1;\r\n                                transform: scale(1);\r\n                            }\r\n                        }\r\n                    `\r\n\r\n          return (\r\n            <Box\r\n              key={id}\r\n              ref={currentContainerRef}\r\n              onMouseEnter={() => setHoveredRTEId(id)}\r\n              onMouseLeave={() => {\r\n                setHoveredRTEId(null)\r\n                setExpandedActionRTEId(null)\r\n              }}\r\n              sx={{\r\n                display: \"flex\",\r\n                alignItems: \"center\",\r\n                position: \"relative\",\r\n                \"&:hover .rte-expanding-button\": {\r\n                  opacity: \"1 !important\",\r\n                  visibility: \"visible !important\",\r\n                },\r\n                \"&.qadpt-rte:hover .rte-expanding-button\": {\r\n                  opacity: \"1 !important\",\r\n                  visibility: \"visible !important\",\r\n                },\r\n                \"& .jodit-status-bar-link\": {\r\n                  display: \"none !important\",\r\n                },\r\n                \"& .jodit-editor\": {\r\n                  fontFamily: \"'Roboto', sans-serif !important\",\r\n                },\r\n                \".jodit-editor span\": {\r\n                  fontFamily: \"'Roboto', sans-serif !important\",\r\n                },\r\n                \".jodit-toolbar-button button\": {\r\n                  minWidth: \"29px !important\",\r\n                },\r\n                \".jodit-react-container\": {\r\n                  width: \"100%\",\r\n                  whiteSpace: \"pre-wrap\",\r\n                  wordBreak: \"break-word\",\r\n                },\r\n                \".jodit.jodit-dialog\": {\r\n                  position: \"fixed !important\",\r\n                  zIndex: \"100001 !important\",\r\n                  top: \"50% !important\",\r\n                  left: \"50% !important\",\r\n                  transform: \"translate(-50%, -50%) !important\",\r\n                },\r\n                \".jodit-dialog .jodit-dialog__panel\": {\r\n                  position: \"relative !important\",\r\n                  top: \"auto !important\",\r\n                  left: \"auto !important\",\r\n                  transform: \"none !important\",\r\n                  maxWidth: \"400px !important\",\r\n                  background: \"white !important\",\r\n                  border: \"1px solid #ccc !important\",\r\n                  borderRadius: \"4px !important\",\r\n                  boxShadow: \"0 4px 12px rgba(0,0,0,0.15) !important\",\r\n                },\r\n                \".jodit-dialog_alert\": {\r\n                  position: \"fixed !important\",\r\n                  zIndex: \"100001 !important\",\r\n                  top: \"50% !important\",\r\n                  left: \"50% !important\",\r\n                  transform: \"translate(-50%, -50%) !important\",\r\n                },\r\n              }}\r\n              className=\"qadpt-rte\"\r\n            >\r\n              {/* Expanding Action Button - Only show for non-Banner templates */}\r\n              {!(\r\n                selectedTemplate === \"Banner\" ||\r\n                (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\")\r\n              ) && (\r\n                <div\r\n                  style={{\r\n                    position: \"absolute\",\r\n                    top:\r\n                      toolbarVisibleRTEId === id\r\n                        ? \"45px\"\r\n                        : (contentState[id]?.isEmpty ?? isContentEmpty(rteText, id))\r\n                          ? \"50%\"\r\n                          : \"8px\",\r\n                    right: \"8px\",\r\n                    transform:\r\n                      toolbarVisibleRTEId === id\r\n                        ? \"none\"\r\n                        : (contentState[id]?.isEmpty ?? isContentEmpty(rteText, id))\r\n                          ? \"translateY(-50%)\"\r\n                          : \"none\",\r\n                    zIndex: 1003,\r\n                    opacity: hoveredRTEId === id ? 1 : 0,\r\n                    visibility: hoveredRTEId === id ? \"visible\" : \"hidden\",\r\n                    transition: \"opacity 0.2s ease-in-out, visibility 0.2s ease-in-out\",\r\n                    pointerEvents: \"auto\",\r\n                  }}\r\n                  className=\"rte-expanding-button\"\r\n                  onMouseEnter={() => setExpandedActionRTEId(id)}\r\n                  onMouseLeave={() => setExpandedActionRTEId(null)}\r\n                >\r\n                  {expandedActionRTEId === id ? (\r\n                    // Expanded state - show all three buttons\r\n                    <div\r\n                      style={{\r\n                        display: \"flex\",\r\n                        gap: \"4px\",\r\n                        backgroundColor: \"rgba(255, 255, 255, 0.95)\",\r\n                        border: \"1px solid #e0e0e0\",\r\n                        borderRadius: \"6px\",\r\n                        padding: \"4px\",\r\n                        boxShadow: \"0 4px 12px rgba(0, 0, 0, 0.15)\",\r\n                        animation: \"expandIn 0.2s ease-out forwards\",\r\n                      }}\r\n                    >\r\n                      {/* Clone Icon */}\r\n                      <IconButton\r\n                        size=\"small\"\r\n                        onClick={() => handleCloneContainer(item.id)}\r\n                        disabled={isCloneDisabled}\r\n                        title={\r\n                          isCloneDisabled\r\n                            ? translate(\"Maximum limit of 3 Rich Text sections reached\")\r\n                            : translate(\"Clone Section\")\r\n                        }\r\n                        sx={{\r\n                          width: \"24px\",\r\n                          height: \"24px\",\r\n                          backgroundColor: \"transparent\",\r\n                          \"&:hover\": {\r\n                            backgroundColor: \"rgba(59, 130, 246, 0.1)\",\r\n                            transform: \"scale(1.05)\",\r\n                          },\r\n                          \"&:disabled\": {\r\n                            opacity: 0.5,\r\n                            cursor: \"not-allowed\",\r\n                          },\r\n                          transition: \"all 0.15s ease-in-out\",\r\n                          svg: {\r\n                            height: \"16px\",\r\n                            width: \"16px\",\r\n                            path: {\r\n                              fill: \"var(--primarycolor)\",\r\n                            },\r\n                          },\r\n                        }}\r\n                      >\r\n                        <span\r\n                          dangerouslySetInnerHTML={{ __html: copyicon }}\r\n                          style={{\r\n                            height: \"16px\",\r\n                            width: \"16px\",\r\n                          }}\r\n                        />\r\n                      </IconButton>\r\n\r\n                      {/* Delete Icon */}\r\n                      <IconButton\r\n                        size=\"small\"\r\n                        onClick={() => handleDeleteSection(item.id, rteId)}\r\n                        title={translate(\"Delete Section\")}\r\n                        sx={{\r\n                          width: \"24px\",\r\n                          height: \"24px\",\r\n                          backgroundColor: \"transparent\",\r\n                          \"&:hover\": {\r\n                            backgroundColor: \"rgba(239, 68, 68, 0.1)\",\r\n                            transform: \"scale(1.05)\",\r\n                          },\r\n                          \"&:hover span\": {\r\n                            filter:\r\n                              \"brightness(0) saturate(100%) invert(27%) sepia(51%) saturate(2878%) hue-rotate(346deg) brightness(104%) contrast(97%) !important\",\r\n                          },\r\n                          transition: \"all 0.15s ease-in-out\",\r\n                        }}\r\n                      >\r\n                        <svg width=\"16\" height=\"16\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                          <path\r\n                            d=\"M5 2V1.5C5 1.22386 5.22386 1 5.5 1H10.5C10.7761 1 11 1.22386 11 1.5V2H13.5C13.7761 2 14 2.22386 14 2.5C14 2.77614 13.7761 3 13.5 3H13V12.5C13 13.3284 12.3284 14 11.5 14H4.5C3.67157 14 3 13.3284 3 12.5V3H2.5C2.22386 3 2 2.77614 2 2.5C2 2.22386 2.22386 2 2.5 2H5ZM4 3V12.5C4 12.7761 4.22386 13 4.5 13H11.5C11.7761 13 12 12.7761 12 12.5V3H4ZM6 5.5C6 5.22386 6.22386 5 6.5 5C6.77614 5 7 5.22386 7 5.5V10.5C7 10.7761 6.77614 11 6.5 11C6.22386 11 6 10.7761 6 10.5V5.5ZM9 5.5C9 5.22386 9.22386 5 9.5 5C9.77614 5 10 5.22386 10 5.5V10.5C10 10.7761 9.77614 11 9.5 11C9.22386 11 9 10.7761 9 10.5V5.5Z\"\r\n                            fill=\"#ef4444\"\r\n                          />\r\n                        </svg>\r\n                      </IconButton>\r\n\r\n                      {/* Separator */}\r\n                      <div style={{ width: \"1px\", height: \"20px\", backgroundColor: \"#e0e0e0\", margin: \"2px 2px\" }} />\r\n\r\n                      {/* Edit Icon */}\r\n                      <IconButton\r\n                        size=\"small\"\r\n                        onClick={(e) => {\r\n                          e.stopPropagation()\r\n                          toggleToolbar(id)\r\n                        }}\r\n                        title={translate(\"Toggle Toolbar\")}\r\n                        sx={{\r\n                          width: \"24px\",\r\n                          height: \"24px\",\r\n                          backgroundColor: \"transparent\",\r\n                          \"&:hover\": {\r\n                            backgroundColor: \"rgba(107, 114, 128, 0.1)\",\r\n                            transform: \"scale(1.05)\",\r\n                          },\r\n                          transition: \"all 0.15s ease-in-out\",\r\n                        }}\r\n                      >\r\n                        <span\r\n                          dangerouslySetInnerHTML={{ __html: editicon }}\r\n                          style={{ height: \"16px\", width: \"16px\" }}\r\n                        />\r\n                      </IconButton>\r\n                    </div>\r\n                  ) : (\r\n                    // Collapsed state - show single more button\r\n                    <IconButton\r\n                      size=\"small\"\r\n                      sx={{\r\n                        width: \"28px\",\r\n                        height: \"28px\",\r\n                        backgroundColor: \"rgba(255, 255, 255, 0.95)\",\r\n                        border: \"1px solid #e0e0e0\",\r\n                        borderRadius: \"6px\",\r\n                        boxShadow: \"0 2px 6px rgba(0, 0, 0, 0.1)\",\r\n                        \"&:hover\": {\r\n                          backgroundColor: \"rgba(255, 255, 255, 1)\",\r\n                          boxShadow: \"0 4px 12px rgba(0, 0, 0, 0.15)\",\r\n                          transform: \"scale(1.05)\",\r\n                        },\r\n                        transition: \"all 0.2s ease-in-out\",\r\n                      }}\r\n                      title={translate(\"More Actions\")}\r\n                    >\r\n                      <MoreHorizIcon sx={{ fontSize: \"18px\", color: \"#6b7280\" }} />\r\n                    </IconButton>\r\n                  )}\r\n                </div>\r\n              )}\r\n\r\n              {/* Jodit Editor Container */}\r\n              <div id={`rte-${id}`} style={{ width: \"100%\", position: \"relative\" }}>\r\n                <style>{dynamicCSS}</style>\r\n                <JoditEditor\r\n                  ref={currentEditorRef}\r\n                  value={rteText}\r\n                  config={config}\r\n                  onChange={(newContent) => handleUpdate(newContent, rteId, id)}\r\n                />\r\n              </div>\r\n            </Box>\r\n          )\r\n        })}\r\n      </>\r\n    )\r\n  },\r\n)\r\n\r\nexport default RTEsection\r\n"], "mappings": "AAAA,YAAY;;AAAA,IAAAA,YAAA;EAAAC,EAAA,GAAAC,YAAA;AAEZ,OAAOC,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,UAAU,EAAEC,OAAO,EAAEC,WAAW,QAAQ,OAAO;AAC5F,SAASC,GAAG,EAAEC,UAAU,QAAQ,eAAe;AAC/C,OAAOC,WAAW,MAAM,aAAa;AACrC,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,cAAc,MAAM,4BAA4B;AACvD,SAASC,QAAQ,EAAEC,QAAQ,QAAQ,6BAA6B;AAChE,SAASC,cAAc,QAAQ,eAAe;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAY9C,MAAMC,UAAqC,gBAAArB,EAAA,cAAGM,UAAU,CAAAgB,EAAA,GAAAtB,EAAA,CACtD,CAAC;EAAEuB,UAAU;EAAEC,QAAQ;EAAEC,sBAAsB;EAAEC,KAAK;EAAEC,aAAa;EAAEC,OAAO;EAAEC;AAAgB,CAAC,EAAEC,GAAG,KAAK;EAAA9B,EAAA;EACzG,MAAM;IAAE+B,CAAC,EAAEC;EAAU,CAAC,GAAGhB,cAAc,CAAC,CAAC;EACzC,MAAM;IACJiB,aAAa;IACbC,kBAAkB;IAClBC,mBAAmB;IACnBC,iBAAiB;IACjBC,eAAe;IACfC,gBAAgB;IAChBC,oBAAoB;IACpBC,yBAAyB;IACzBC,oBAAoB;IACpBC,0BAA0B;IAC1BC,qBAAqB;IACrBC,YAAY;IACZC,WAAW;IACXC;EACF,CAAC,GAAGjC,cAAc,CAAC,CAAC;;EAEpB;EACA,MAAM,CAACkC,YAAY,EAAEC,eAAe,CAAC,GAAG7C,QAAQ,CAAgB,IAAI,CAAC;EACrE,MAAM,CAAC8C,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG/C,QAAQ,CAAgB,IAAI,CAAC;EACnF,MAAM,CAACgD,YAAY,EAAEC,eAAe,CAAC,GAAGjD,QAAQ,CAAgB,IAAI,CAAC;EACrE,MAAM,CAACkD,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGnD,QAAQ,CAAgB,IAAI,CAAC;EACnF,MAAM,CAACoD,YAAY,EAAEC,eAAe,CAAC,GAAGrD,QAAQ,CAAiE,CAAC,CAAC,CAAC;EACpH,MAAMsD,UAAU,GAAGpD,MAAM,CAAS,EAAE,CAAC;;EAErC;EACA,MAAMqD,UAAU,GAAGrD,MAAM,CAAoC,IAAIsD,GAAG,CAAC,CAAC,CAAC;EACvE,MAAMC,aAAa,GAAGvD,MAAM,CAA+C,IAAIsD,GAAG,CAAC,CAAC,CAAC;;EAErF;EACA,MAAME,YAAY,GAAIC,KAAa,IAAK;IACtC,IAAI,CAACJ,UAAU,CAACK,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC,EAAE;MAClCJ,UAAU,CAACK,OAAO,CAACE,GAAG,CAACH,KAAK,eAAE5D,KAAK,CAACgE,SAAS,CAAC,CAAC,CAAC;IAClD;IACA,OAAOR,UAAU,CAACK,OAAO,CAACI,GAAG,CAACL,KAAK,CAAC;EACtC,CAAC;;EAED;EACA,MAAMM,eAAe,GAAIN,KAAa,IAAK;IACzC,IAAI,CAACF,aAAa,CAACG,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC,EAAE;MACrCF,aAAa,CAACG,OAAO,CAACE,GAAG,CAACH,KAAK,eAAE5D,KAAK,CAACgE,SAAS,CAAC,CAAC,CAAC;IACrD;IACA,OAAON,aAAa,CAACG,OAAO,CAACI,GAAG,CAACL,KAAK,CAAC;EACzC,CAAC;;EAED;EACA,MAAMO,cAAc,GAAGA,CAACC,OAAe,EAAER,KAAa,KAAK;IACzD,IAAI,CAACQ,OAAO,EAAE,OAAO,IAAI;IACzB,MAAMC,WAAW,GAAGD,OAAO,CAACE,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAACC,IAAI,CAAC,CAAC;IAC1D,IAAI,CAACF,WAAW,EAAE,OAAO,IAAI;IAC7B,MAAMG,cAAc,GAAG,CAAC,aAAa,EAAE,SAAS,EAAE,MAAM,EAAE,EAAE,CAAC;IAC7D,IAAIA,cAAc,CAACC,QAAQ,CAACL,OAAO,CAACG,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,IAAI;IACxD,OAAO,KAAK;EACd,CAAC;EAED,MAAMG,mBAAmB,GAAId,KAAa,IAAK;IAC7C,MAAMe,SAAS,GAAGhB,YAAY,CAACC,KAAK,CAAC;IACrC,IAAIe,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEd,OAAO,EAAE;MACtB,IAAI;QAAA,IAAAe,OAAA;QACF,MAAMC,SAAS,IAAAD,OAAA,GAAID,SAAS,CAACd,OAAO,CAASiB,MAAM,cAAAF,OAAA,uBAAjCA,OAAA,CAAmCC,SAAS;QAC9D,IAAIA,SAAS,EAAE;UACb,OAAOA,SAAS,CAACE,YAAY,GAAGF,SAAS,CAACG,YAAY;QACxD;MACF,CAAC,CAAC,OAAOC,KAAK,EAAE;QACd;MAAA;IAEJ;IACA,OAAO,KAAK;EACd,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAGlF,KAAK,CAACM,WAAW,CAAC,CAAC8D,OAAe,EAAER,KAAa,KAAK;IAC/E,MAAMuB,OAAO,GAAGhB,cAAc,CAACC,OAAO,EAAER,KAAK,CAAC;IAC9C,MAAMwB,YAAY,GAAGV,mBAAmB,CAACd,KAAK,CAAC;IAE/CN,eAAe,CAAE+B,IAAI,KAAM;MACzB,GAAGA,IAAI;MACP,CAACzB,KAAK,GAAG;QAAEuB,OAAO;QAAEC;MAAa;IACnC,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAlF,SAAS,CAAC,MAAM;IACd,MAAMoF,kBAAkB,GAAIC,KAAiB,IAAK;MAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MAChD,IAAI,CAAC9C,YAAY,EAAE;MAEnB,MAAM+C,yBAAyB,GAAIL,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,uBAAuB,CAAC,KAAK,IAAI;MACzG,MAAMC,oBAAoB,GAAIR,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,iBAAiB,CAAC,KAAK,IAAI;MAC9F,MAAME,aAAa,IAAAR,qBAAA,GAAGS,QAAQ,CAACC,aAAa,CAAC,cAAc,CAAC,cAAAV,qBAAA,uBAAtCA,qBAAA,CAAwCW,QAAQ,CAACZ,KAAK,CAACM,MAAc,CAAC;MAC5F,MAAMO,kBAAkB,IAAAX,sBAAA,GAAGQ,QAAQ,CAACC,aAAa,CAAC,gBAAgB,CAAC,cAAAT,sBAAA,uBAAxCA,sBAAA,CAA0CU,QAAQ,CAACZ,KAAK,CAACM,MAAc,CAAC;MACnG,MAAMQ,sBAAsB,GAC1BD,kBAAkB,MAAAV,sBAAA,GAAIO,QAAQ,CAACC,aAAa,CAAC,sBAAsB,CAAC,cAAAR,sBAAA,uBAA9CA,sBAAA,CAAgDS,QAAQ,CAACZ,KAAK,CAACM,MAAc,CAAC;MACtG,MAAMS,iBAAiB,GAAIf,KAAK,CAACM,MAAM,CAAiBU,EAAE,CAACC,UAAU,CAAC,yBAAyB,CAAC;MAChG,MAAMC,WAAW,IAAAd,sBAAA,GAAGM,QAAQ,CAACC,aAAa,CAAC,wBAAwB,CAAC,cAAAP,sBAAA,uBAAhDA,sBAAA,CAAkDQ,QAAQ,CAACZ,KAAK,CAACM,MAAc,CAAC;MACpG,MAAMa,qBAAqB,GAAInB,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,+BAA+B,CAAC,KAAK,IAAI;MAC7G,MAAMa,cAAc,GAAIpB,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,8BAA8B,CAAC,KAAK,IAAI;MAErG,MAAMc,mBAAmB,GAAG1C,eAAe,CAACrB,YAAY,CAAC;MAEzD,IACE+D,mBAAmB,aAAnBA,mBAAmB,eAAnBA,mBAAmB,CAAE/C,OAAO,IAC5B,CAAC+C,mBAAmB,CAAC/C,OAAO,CAACsC,QAAQ,CAACZ,KAAK,CAACM,MAAc,CAAC,IAC3D,CAACG,aAAa,IACd,CAACI,kBAAkB,IACnB,CAACC,sBAAsB,IACvB,CAACC,iBAAiB,IAClB,CAACG,WAAW,IACZ,CAACC,qBAAqB,IACtB,CAACC,cAAc,IACf,CAACf,yBAAyB,IAC1B,CAACG,oBAAoB,EACrB;QACAjD,eAAe,CAAC,IAAI,CAAC;QACrBE,sBAAsB,CAAC,IAAI,CAAC;MAC9B;IACF,CAAC;IAEDiD,QAAQ,CAACY,gBAAgB,CAAC,WAAW,EAAEvB,kBAAkB,CAAC;IAC1D,OAAO,MAAMW,QAAQ,CAACa,mBAAmB,CAAC,WAAW,EAAExB,kBAAkB,CAAC;EAC5E,CAAC,EAAE,CAACzC,YAAY,CAAC,CAAC;EAElB3C,SAAS,CAAC,MAAM;IACd,IAAI2C,YAAY,EAAE;MAChB,MAAM8B,SAAS,GAAGhB,YAAY,CAACd,YAAY,CAAC;MAC5C,IAAI8B,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEd,OAAO,EAAE;QACtBkD,UAAU,CAAC,MAAM;UACf;QAAA,CACD,EAAE,EAAE,CAAC;MACR;IACF;EACF,CAAC,EAAE,CAAClE,YAAY,CAAC,CAAC;EAElB,MAAMmE,YAAY,GAAG1G,WAAW,CAC9B,CAAC2G,UAAkB,EAAErD,KAAa,EAAEsD,WAAmB,KAAK;IAC1D3D,UAAU,CAACM,OAAO,GAAGoD,UAAU;IAE/B,MAAME,gBAAgB,GACpBzE,YAAY,KAAKN,gBAAgB,KAAK,cAAc,IAAIC,oBAAoB,KAAK,cAAc,CAAC;IAClG,MAAM+E,QAAQ,GAAG1E,YAAY,IAAIN,gBAAgB,KAAK,MAAM;IAC5D,MAAMiF,kBAAkB,GAAGD,QAAQ,IAAI/E,oBAAoB,KAAK,cAAc;IAC9E,MAAMiF,YAAY,GAAGF,QAAQ,IAAI/E,oBAAoB,KAAK,QAAQ;IAClE,MAAMkF,aAAa,GAAGH,QAAQ,KAAK/E,oBAAoB,KAAK,SAAS,IAAIA,oBAAoB,KAAK,SAAS,CAAC;IAE5G,IAAI8E,gBAAgB,EAAE;MACpB,MAAMK,gBAAgB,GAAG7E,WAAW,GAAG,CAAC;MAExC,IAAI0E,kBAAkB,EAAE;QAAA,IAAAI,qBAAA,EAAAC,sBAAA;QACtB,MAAMC,gBAAgB,IAAAF,qBAAA,GAAGlF,oBAAoB,CAACiF,gBAAgB,CAAC,cAAAC,qBAAA,wBAAAC,sBAAA,GAAtCD,qBAAA,CAAwCG,UAAU,cAAAF,sBAAA,uBAAlDA,sBAAA,CAAoDG,IAAI,CAC9EC,SAAc,IAAKA,SAAS,CAACvB,EAAE,KAAKW,WAAW,IAAIY,SAAS,CAACC,IAAI,KAAK,KACzE,CAAC;QAED,IAAIJ,gBAAgB,EAAE;UACpBlF,qBAAqB,CAACyE,WAAW,EAAED,UAAU,CAAC;QAChD;MACF,CAAC,MAAM;QAAA,IAAAe,qBAAA,EAAAC,sBAAA;QACL,MAAMC,qBAAqB,IAAAF,qBAAA,GAAG1F,yBAAyB,CAACkF,gBAAgB,CAAC,cAAAQ,qBAAA,wBAAAC,sBAAA,GAA3CD,qBAAA,CAA6CJ,UAAU,cAAAK,sBAAA,uBAAvDA,sBAAA,CAAyDJ,IAAI,CACxFC,SAAc,IAAKA,SAAS,CAACvB,EAAE,KAAKW,WAAW,IAAIY,SAAS,CAACC,IAAI,KAAK,KACzE,CAAC;QAED,IAAIG,qBAAqB,EAAE;UACzB1F,0BAA0B,CAAC0E,WAAW,EAAED,UAAU,CAAC;QACrD;MACF;IACF,CAAC,MAAM,IAAIG,QAAQ,KAAKE,YAAY,IAAIC,aAAa,CAAC,EAAE;MAAA,IAAAY,sBAAA,EAAAC,sBAAA;MACtD,MAAMZ,gBAAgB,GAAG7E,WAAW,GAAG,CAAC;MACxC,MAAMgF,gBAAgB,IAAAQ,sBAAA,GAAG5F,oBAAoB,CAACiF,gBAAgB,CAAC,cAAAW,sBAAA,wBAAAC,sBAAA,GAAtCD,sBAAA,CAAwCP,UAAU,cAAAQ,sBAAA,uBAAlDA,sBAAA,CAAoDP,IAAI,CAC9EC,SAAc,IAAKA,SAAS,CAACvB,EAAE,KAAKW,WAAW,IAAIY,SAAS,CAACC,IAAI,KAAK,KACzE,CAAC;MAED,IAAIJ,gBAAgB,EAAE;QACpBlF,qBAAqB,CAACyE,WAAW,EAAED,UAAU,CAAC;MAChD,CAAC,MAAM;QACLoB,OAAO,CAACC,IAAI,CAAC,kCAAkCjG,oBAAoB,OAAO,CAAC;MAC7E;IACF,CAAC,MAAM;MACLL,kBAAkB,CAACkF,WAAW,EAAEtD,KAAK,EAAEqD,UAAU,CAAC;IACpD;IAEAhF,mBAAmB,CAAC,IAAI,CAAC;IACzBiD,kBAAkB,CAAC+B,UAAU,EAAErD,KAAK,CAAC;EACvC,CAAC,EACD,CACElB,YAAY,EACZN,gBAAgB,EAChBC,oBAAoB,EACpBM,WAAW,EACXJ,oBAAoB,EACpBD,yBAAyB,EACzBG,qBAAqB,EACrBD,0BAA0B,EAC1BR,kBAAkB,EAClBC,mBAAmB,EACnBiD,kBAAkB,CAEtB,CAAC;EAED,MAAMqD,oBAAoB,GAAIrB,WAAmB,IAAK;IACpD,IAAIvF,eAAe,EAAE;MACnB;IACF;IACAO,iBAAiB,CAACgF,WAAW,CAAC;IAC9B,IAAIxF,OAAO,EAAE;MACXA,OAAO,CAAC,CAAC;IACX;EACF,CAAC;EAED,MAAM8G,mBAAmB,GAAGA,CAACtB,WAAmB,EAAEtD,KAAa,KAAK;IAClE,MAAMuD,gBAAgB,GACpBzE,YAAY,KAAKN,gBAAgB,KAAK,cAAc,IAAIC,oBAAoB,KAAK,cAAc,CAAC;IAElG,IAAI8E,gBAAgB,EAAE;MACpBhF,eAAe,CAAC+E,WAAW,EAAEtD,KAAK,CAAC;IACrC,CAAC,MAAM;MACLzB,eAAe,CAAC+E,WAAW,EAAEtD,KAAK,CAAC;IACrC;IAEArC,sBAAsB,CAACC,KAAK,CAAC;EAC/B,CAAC;EAED,MAAMiH,WAAW,GAAIlD,KAA2C,IAAK;IACnEA,KAAK,CAACmD,cAAc,CAAC,CAAC;IAEtB,MAAMC,aAAa,GAAGpD,KAAK,CAACoD,aAAa;IACzC,MAAMC,UAAU,GAAGD,aAAa,CAACE,OAAO,CAAC,YAAY,CAAC;IACtD,MAAMC,UAAU,GAAGH,aAAa,CAACE,OAAO,CAAC,WAAW,CAAC;IAErD,IAAIC,UAAU,EAAE;MACd,MAAMC,YAAY,GAAGD,UAAU,CAACrE,QAAQ,CAAC,YAAY,CAAC;MACtD,IAAIsE,YAAY,EAAE;QAChBC,aAAa,CAACF,UAAU,CAAC;MAC3B,CAAC,MAAM;QACLE,aAAa,CAACF,UAAU,CAAC;MAC3B;IACF,CAAC,MAAM;MACLE,aAAa,CAACJ,UAAU,CAAC;IAC3B;EACF,CAAC;EAED,MAAMI,aAAa,GAAI5E,OAAe,IAAK;IACzC,IAAIvB,YAAY,EAAE;MAChB,MAAM8B,SAAS,GAAGhB,YAAY,CAACd,YAAY,CAAC;MAC5C,IAAI8B,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEd,OAAO,EAAE;QACtB,MAAMiB,MAAM,GAAIH,SAAS,CAACd,OAAO,CAASiB,MAAM;QAChDA,MAAM,CAACmE,SAAS,CAACC,UAAU,CAAC9E,OAAO,CAAC;MACtC;IACF;EACF,CAAC;EAED,MAAM+E,aAAa,GAAIvF,KAAa,IAAK;IACvC,IAAIb,mBAAmB,KAAKa,KAAK,EAAE;MACjCZ,sBAAsB,CAAC,IAAI,CAAC;IAC9B,CAAC,MAAM;MACLA,sBAAsB,CAACY,KAAK,CAAC;IAC/B;EACF,CAAC;EAED,MAAM,CAACwF,cAAc,EAAEC,iBAAiB,CAAC,GAAGpJ,QAAQ,CAAU,KAAK,CAAC;EACpEC,SAAS,CAAC,MAAM;IACd,MAAMoJ,GAAG,GAAGrD,QAAQ,CAACsD,IAAI,CAACC,YAAY,CAAC,KAAK,CAAC,IAAI,KAAK;IACtDH,iBAAiB,CAACC,GAAG,CAACG,WAAW,CAAC,CAAC,KAAK,KAAK,CAAC;EAChD,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,MAAM,GAAGrJ,OAAO,CACpB,OAAY;IACVsJ,QAAQ,EAAE,KAAK;IACfC,SAAS,EAAER,cAAc,GAAI,KAAK,GAAc,KAAe;IAC/DS,QAAQ,EAAE,IAAI;IACdC,WAAW,EAAE,sBAAsB;IACnCC,aAAa,EAAE,KAAK;IACpBC,eAAe,EAAE,KAAK;IACtBC,OAAO,EAAElH,mBAAmB,KAAK,IAAI;IACrCmH,gBAAgB,EAAE,KAAK;IACvBC,gBAAgB,EAAE,KAAK;IACvBC,oBAAoB,EAAE,KAAK;IAC3BC,SAAS,EAAE,KAAK;IAChBC,UAAU,EAAE,IAAI;IAChBC,kBAAkB,EAAE,KAAK;IACzBC,sBAAsB,EAAE,KAAK;IAC7BC,MAAM,EAAE,MAAM;IACdC,SAAS,EAAE3H,mBAAmB,KAAK,IAAI,GAAG,GAAG,GAAG,EAAE;IAClD4H,SAAS,EAAE,GAAG;IACdC,OAAO,EAAE,CACP,MAAM,EACN,QAAQ,EACR,WAAW,EACX,eAAe,EACf,IAAI,EACJ,IAAI,EACJ,OAAO,EACP,MAAM,EACN,UAAU,EACV,MAAM,EACN;MACEC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,+DAA+D;MACxEC,IAAI,EAAE,CACJ,QAAQ,EACR,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,MAAM,EACN,MAAM,EACN,GAAG,EACH,IAAI,EACJ,QAAQ,EACR,YAAY,EACZ,QAAQ,EACR,UAAU,EACV,OAAO,EACP,aAAa,EACb,WAAW,EACX,GAAG,EACH,SAAS,EACT,QAAQ,EACR,WAAW;IAEf,CAAC,CACF;IACDC,SAAS,EAAE,KAAK;IAChBC,SAAS,EAAEhF,QAAQ,CAACsD,IAAI;IACxB2B,MAAM,EAAE,MAAM;IACdC,cAAc,EAAE,KAAK;IACrBC,IAAI,EAAE;MACJC,gBAAgB,EAAE,KAAK;MACvBC,gBAAgB,EAAE,IAAI;MACtBC,iBAAiB,EAAE,IAAI;MACvBC,oBAAoB,EAAE,IAAI;MAC1BC,gBAAgB,EAAE,KAAK;MACvBC,aAAa,EAAE;IACjB,CAAC;IACDC,MAAM,EAAE;MACNT,MAAM,EAAE;IACV,CAAC;IACDU,oBAAoB,EAAE,KAAc;IACpCC,MAAM,EAAE;MACNC,OAAO,EAAErD;IACX,CAAC;IACDsD,QAAQ,EAAE;MACRC,IAAI,EAAE;QACJjB,IAAI,EAAE;UACJ,qBAAqB,EAAE,SAAS;UAChC,oBAAoB,EAAE,QAAQ;UAC9B,2BAA2B,EAAE,eAAe;UAC5C,uBAAuB,EAAE,WAAW;UACpC,qBAAqB,EAAE,SAAS;UAChC,4BAA4B,EAAE;QAChC;MACF;IACF;EACF,CAAC,CAAC,EACF,CAAC3B,cAAc,EAAErG,mBAAmB,CACtC,CAAC;;EAED;EACA,MAAMoE,gBAAgB,GACpBzE,YAAY,KAAKN,gBAAgB,KAAK,cAAc,IAAIC,oBAAoB,KAAK,cAAc,CAAC;EAClG,MAAM+E,QAAQ,GAAG1E,YAAY,IAAIN,gBAAgB,KAAK,MAAM;EAC5D,MAAMiF,kBAAkB,GAAGD,QAAQ,IAAI/E,oBAAoB,KAAK,cAAc;EAC9E,MAAMiF,YAAY,GAAGF,QAAQ,IAAI/E,oBAAoB,KAAK,QAAQ;EAClE,MAAMkF,aAAa,GAAGH,QAAQ,KAAK/E,oBAAoB,KAAK,SAAS,IAAIA,oBAAoB,KAAK,SAAS,CAAC;EAC5G,MAAMmF,gBAAgB,GAAG7E,WAAW,GAAG,CAAC;EAExC,IAAIsJ,kBAAyB,GAAG,EAAE;EAElC,IAAI9E,gBAAgB,IAAI,CAACE,kBAAkB,EAAE;IAC3C4E,kBAAkB,GAAGrJ,8BAA8B,CAAC4E,gBAAgB,EAAE,KAAK,CAAC;EAC9E,CAAC,MAAM,IAAIJ,QAAQ,KAAKE,YAAY,IAAIC,aAAa,IAAIF,kBAAkB,CAAC,EAAE;IAAA,IAAA6E,sBAAA;IAC5E,KAAAA,sBAAA,GAAI3J,oBAAoB,CAACiF,gBAAgB,CAAC,cAAA0E,sBAAA,eAAtCA,sBAAA,CAAwCtE,UAAU,EAAE;MACtDqE,kBAAkB,GAAG1J,oBAAoB,CAACiF,gBAAgB,CAAC,CAACI,UAAU,CAACuE,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAACrE,IAAI,KAAK,KAAK,CAAC;IACxG,CAAC,MAAM;MACLkE,kBAAkB,GAAG,EAAE;IACzB;EACF,CAAC,MAAM;IACLA,kBAAkB,GAAGlK,aAAa;EACpC;;EAEA;EACA/B,KAAK,CAACE,SAAS,CAAC,MAAM;IACpB+L,kBAAkB,CAACI,OAAO,CAAEC,IAAS,IAAK;MACxC,IAAIC,OAAO,GAAG,EAAE;MAChB,IAAI3I,KAAK,GAAG,EAAE;MACd,IAAI2C,EAAE,GAAG,EAAE;MAEX,IACGY,gBAAgB,IAAI,CAACE,kBAAkB,IACvCD,QAAQ,KAAKE,YAAY,IAAIC,aAAa,IAAIF,kBAAkB,CAAE,EACnE;QACAkF,OAAO,GAAGD,IAAI,CAACE,WAAW,IAAI,EAAE;QAChC5I,KAAK,GAAG0I,IAAI,CAAC/F,EAAE;QACfA,EAAE,GAAG+F,IAAI,CAAC/F,EAAE;MACd,CAAC,MAAM;QAAA,IAAAkG,UAAA,EAAAC,WAAA,EAAAC,WAAA,EAAAC,YAAA;QACLL,OAAO,GAAG,EAAAE,UAAA,GAAAH,IAAI,CAACO,IAAI,cAAAJ,UAAA,wBAAAC,WAAA,GAATD,UAAA,CAAY,CAAC,CAAC,cAAAC,WAAA,uBAAdA,WAAA,CAAgBI,IAAI,KAAI,EAAE;QACpClJ,KAAK,IAAA+I,WAAA,GAAGL,IAAI,CAACO,IAAI,cAAAF,WAAA,wBAAAC,YAAA,GAATD,WAAA,CAAY,CAAC,CAAC,cAAAC,YAAA,uBAAdA,YAAA,CAAgBrG,EAAE;QAC1BA,EAAE,GAAG+F,IAAI,CAAC/F,EAAE;MACd;MAEA,IAAIA,EAAE,IAAI3C,KAAK,EAAE;QACfsB,kBAAkB,CAACqH,OAAO,EAAE3I,KAAK,CAAC;MACpC;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACuD,gBAAgB,EAAEE,kBAAkB,EAAED,QAAQ,EAAEE,YAAY,EAAEC,aAAa,EAAErC,kBAAkB,CAAC,CAAC;EAErG,oBACElE,OAAA,CAAAE,SAAA;IAAA6L,QAAA,EACGd,kBAAkB,CAACe,GAAG,CAAEV,IAAS,IAAK;MAAA,IAAAW,qBAAA,EAAAC,gBAAA,EAAAC,sBAAA,EAAAC,iBAAA;MACrC,IAAIb,OAAO,GAAG,EAAE;MAChB,IAAI3I,KAAK,GAAG,EAAE;MACd,IAAI2C,EAAE,GAAG,EAAE;MAEX,IACGY,gBAAgB,IAAI,CAACE,kBAAkB,IACvCD,QAAQ,KAAKE,YAAY,IAAIC,aAAa,IAAIF,kBAAkB,CAAE,EACnE;QACAkF,OAAO,GAAGD,IAAI,CAACE,WAAW,IAAI,EAAE;QAChC5I,KAAK,GAAG0I,IAAI,CAAC/F,EAAE;QACfA,EAAE,GAAG+F,IAAI,CAAC/F,EAAE;MACd,CAAC,MAAM;QAAA,IAAA8G,WAAA,EAAAC,YAAA,EAAAC,WAAA,EAAAC,YAAA;QACLjB,OAAO,GAAG,EAAAc,WAAA,GAAAf,IAAI,CAACO,IAAI,cAAAQ,WAAA,wBAAAC,YAAA,GAATD,WAAA,CAAY,CAAC,CAAC,cAAAC,YAAA,uBAAdA,YAAA,CAAgBR,IAAI,KAAI,EAAE;QACpClJ,KAAK,IAAA2J,WAAA,GAAGjB,IAAI,CAACO,IAAI,cAAAU,WAAA,wBAAAC,YAAA,GAATD,WAAA,CAAY,CAAC,CAAC,cAAAC,YAAA,uBAAdA,YAAA,CAAgBjH,EAAE;QAC1BA,EAAE,GAAG+F,IAAI,CAAC/F,EAAE;MACd;MAEA,IAAI,CAACA,EAAE,EAAE,OAAO,IAAI;MAEpB,MAAMK,mBAAmB,GAAG1C,eAAe,CAACqC,EAAE,CAAC;MAC/C,MAAMkH,gBAAgB,GAAG9J,YAAY,CAAC4C,EAAE,CAAC;;MAEzC;MACA,MAAMmH,UAAU,GAAG;AAC7B;AACA;AACA;AACA,+BAA+BnH,EAAE;AACjC;AACA;AACA;AACA;AACA;AACA,+BAA+BA,EAAE;AACjC,0CAA0CxD,mBAAmB,KAAKwD,EAAE,GAAG,OAAO,GAAG,MAAM;AACvF;AACA;AACA;AACA;AACA,+BAA+BA,EAAE;AACjC;AACA;AACA,+BAA+BA,EAAE;AACjC;AACA;AACA,+BAA+BA,EAAE;AACjC;AACA;AACA;AACA,+BAA+BA,EAAE;AACjC;AACA;AACA;AACA,+BAA+BA,EAAE;AACjC;AACA;AACA,+BAA+BA,EAAE;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;MAEX,oBACEvF,OAAA,CAACT,GAAG;QAEFqB,GAAG,EAAEgF,mBAAoB;QACzB+G,YAAY,EAAEA,CAAA,KAAMzK,eAAe,CAACqD,EAAE,CAAE;QACxCqH,YAAY,EAAEA,CAAA,KAAM;UAClB1K,eAAe,CAAC,IAAI,CAAC;UACrBE,sBAAsB,CAAC,IAAI,CAAC;QAC9B,CAAE;QACFyK,EAAE,EAAE;UACFC,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,QAAQ,EAAE,UAAU;UACpB,+BAA+B,EAAE;YAC/BC,OAAO,EAAE,cAAc;YACvBC,UAAU,EAAE;UACd,CAAC;UACD,yCAAyC,EAAE;YACzCD,OAAO,EAAE,cAAc;YACvBC,UAAU,EAAE;UACd,CAAC;UACD,0BAA0B,EAAE;YAC1BJ,OAAO,EAAE;UACX,CAAC;UACD,iBAAiB,EAAE;YACjBK,UAAU,EAAE;UACd,CAAC;UACD,oBAAoB,EAAE;YACpBA,UAAU,EAAE;UACd,CAAC;UACD,8BAA8B,EAAE;YAC9BC,QAAQ,EAAE;UACZ,CAAC;UACD,wBAAwB,EAAE;YACxBC,KAAK,EAAE,MAAM;YACbC,UAAU,EAAE,UAAU;YACtBC,SAAS,EAAE;UACb,CAAC;UACD,qBAAqB,EAAE;YACrBP,QAAQ,EAAE,kBAAkB;YAC5B9C,MAAM,EAAE,mBAAmB;YAC3BsD,GAAG,EAAE,gBAAgB;YACrBC,IAAI,EAAE,gBAAgB;YACtBC,SAAS,EAAE;UACb,CAAC;UACD,oCAAoC,EAAE;YACpCV,QAAQ,EAAE,qBAAqB;YAC/BQ,GAAG,EAAE,iBAAiB;YACtBC,IAAI,EAAE,iBAAiB;YACvBC,SAAS,EAAE,iBAAiB;YAC5BC,QAAQ,EAAE,kBAAkB;YAC5BC,UAAU,EAAE,kBAAkB;YAC9BC,MAAM,EAAE,2BAA2B;YACnCC,YAAY,EAAE,gBAAgB;YAC9BC,SAAS,EAAE;UACb,CAAC;UACD,qBAAqB,EAAE;YACrBf,QAAQ,EAAE,kBAAkB;YAC5B9C,MAAM,EAAE,mBAAmB;YAC3BsD,GAAG,EAAE,gBAAgB;YACrBC,IAAI,EAAE,gBAAgB;YACtBC,SAAS,EAAE;UACb;QACF,CAAE;QACFM,SAAS,EAAC,WAAW;QAAAjC,QAAA,GAGpB,EACC3K,gBAAgB,KAAK,QAAQ,IAC5BA,gBAAgB,KAAK,MAAM,IAAIC,oBAAoB,KAAK,QAAS,CACnE,iBACCrB,OAAA;UACEiO,KAAK,EAAE;YACLjB,QAAQ,EAAE,UAAU;YACpBQ,GAAG,EACDzL,mBAAmB,KAAKwD,EAAE,GACtB,MAAM,GACN,EAAA0G,qBAAA,IAAAC,gBAAA,GAAC7J,YAAY,CAACkD,EAAE,CAAC,cAAA2G,gBAAA,uBAAhBA,gBAAA,CAAkB/H,OAAO,cAAA8H,qBAAA,cAAAA,qBAAA,GAAI9I,cAAc,CAACoI,OAAO,EAAEhG,EAAE,CAAC,IACvD,KAAK,GACL,KAAK;YACb2I,KAAK,EAAE,KAAK;YACZR,SAAS,EACP3L,mBAAmB,KAAKwD,EAAE,GACtB,MAAM,GACN,EAAA4G,sBAAA,IAAAC,iBAAA,GAAC/J,YAAY,CAACkD,EAAE,CAAC,cAAA6G,iBAAA,uBAAhBA,iBAAA,CAAkBjI,OAAO,cAAAgI,sBAAA,cAAAA,sBAAA,GAAIhJ,cAAc,CAACoI,OAAO,EAAEhG,EAAE,CAAC,IACvD,kBAAkB,GAClB,MAAM;YACd2E,MAAM,EAAE,IAAI;YACZ+C,OAAO,EAAEhL,YAAY,KAAKsD,EAAE,GAAG,CAAC,GAAG,CAAC;YACpC2H,UAAU,EAAEjL,YAAY,KAAKsD,EAAE,GAAG,SAAS,GAAG,QAAQ;YACtD4I,UAAU,EAAE,uDAAuD;YACnEC,aAAa,EAAE;UACjB,CAAE;UACFJ,SAAS,EAAC,sBAAsB;UAChCrB,YAAY,EAAEA,CAAA,KAAMvK,sBAAsB,CAACmD,EAAE,CAAE;UAC/CqH,YAAY,EAAEA,CAAA,KAAMxK,sBAAsB,CAAC,IAAI,CAAE;UAAA2J,QAAA,EAEhD5J,mBAAmB,KAAKoD,EAAE;UAAA;UACzB;UACAvF,OAAA;YACEiO,KAAK,EAAE;cACLnB,OAAO,EAAE,MAAM;cACfuB,GAAG,EAAE,KAAK;cACVC,eAAe,EAAE,2BAA2B;cAC5CT,MAAM,EAAE,mBAAmB;cAC3BC,YAAY,EAAE,KAAK;cACnBS,OAAO,EAAE,KAAK;cACdR,SAAS,EAAE,gCAAgC;cAC3CS,SAAS,EAAE;YACb,CAAE;YAAAzC,QAAA,gBAGF/L,OAAA,CAACR,UAAU;cACTiP,IAAI,EAAC,OAAO;cACZC,OAAO,EAAEA,CAAA,KAAMnH,oBAAoB,CAAC+D,IAAI,CAAC/F,EAAE,CAAE;cAC7CoJ,QAAQ,EAAEhO,eAAgB;cAC1BiO,KAAK,EACHjO,eAAe,GACXG,SAAS,CAAC,+CAA+C,CAAC,GAC1DA,SAAS,CAAC,eAAe,CAC9B;cACD+L,EAAE,EAAE;gBACFQ,KAAK,EAAE,MAAM;gBACb5D,MAAM,EAAE,MAAM;gBACd6E,eAAe,EAAE,aAAa;gBAC9B,SAAS,EAAE;kBACTA,eAAe,EAAE,yBAAyB;kBAC1CZ,SAAS,EAAE;gBACb,CAAC;gBACD,YAAY,EAAE;kBACZT,OAAO,EAAE,GAAG;kBACZ4B,MAAM,EAAE;gBACV,CAAC;gBACDV,UAAU,EAAE,uBAAuB;gBACnCW,GAAG,EAAE;kBACHrF,MAAM,EAAE,MAAM;kBACd4D,KAAK,EAAE,MAAM;kBACb0B,IAAI,EAAE;oBACJC,IAAI,EAAE;kBACR;gBACF;cACF,CAAE;cAAAjD,QAAA,eAEF/L,OAAA;gBACEiP,uBAAuB,EAAE;kBAAEC,MAAM,EAAEtP;gBAAS,CAAE;gBAC9CqO,KAAK,EAAE;kBACLxE,MAAM,EAAE,MAAM;kBACd4D,KAAK,EAAE;gBACT;cAAE;gBAAA8B,QAAA,EAAAtQ,YAAA;gBAAAuQ,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAF,QAAA,EAAAtQ,YAAA;cAAAuQ,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC,eAGbrP,OAAA,CAACR,UAAU;cACTiP,IAAI,EAAC,OAAO;cACZC,OAAO,EAAEA,CAAA,KAAMlH,mBAAmB,CAAC8D,IAAI,CAAC/F,EAAE,EAAE3C,KAAK,CAAE;cACnDgM,KAAK,EAAE9N,SAAS,CAAC,gBAAgB,CAAE;cACnC+L,EAAE,EAAE;gBACFQ,KAAK,EAAE,MAAM;gBACb5D,MAAM,EAAE,MAAM;gBACd6E,eAAe,EAAE,aAAa;gBAC9B,SAAS,EAAE;kBACTA,eAAe,EAAE,wBAAwB;kBACzCZ,SAAS,EAAE;gBACb,CAAC;gBACD,cAAc,EAAE;kBACdvC,MAAM,EACJ;gBACJ,CAAC;gBACDgD,UAAU,EAAE;cACd,CAAE;cAAApC,QAAA,eAEF/L,OAAA;gBAAKqN,KAAK,EAAC,IAAI;gBAAC5D,MAAM,EAAC,IAAI;gBAAC6F,OAAO,EAAC,WAAW;gBAACN,IAAI,EAAC,MAAM;gBAACO,KAAK,EAAC,4BAA4B;gBAAAxD,QAAA,eAC5F/L,OAAA;kBACEwP,CAAC,EAAC,+kBAA+kB;kBACjlBR,IAAI,EAAC;gBAAS;kBAAAG,QAAA,EAAAtQ,YAAA;kBAAAuQ,UAAA;kBAAAC,YAAA;gBAAA,OACf;cAAC;gBAAAF,QAAA,EAAAtQ,YAAA;gBAAAuQ,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAF,QAAA,EAAAtQ,YAAA;cAAAuQ,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eAGbrP,OAAA;cAAKiO,KAAK,EAAE;gBAAEZ,KAAK,EAAE,KAAK;gBAAE5D,MAAM,EAAE,MAAM;gBAAE6E,eAAe,EAAE,SAAS;gBAAEmB,MAAM,EAAE;cAAU;YAAE;cAAAN,QAAA,EAAAtQ,YAAA;cAAAuQ,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAG/FrP,OAAA,CAACR,UAAU;cACTiP,IAAI,EAAC,OAAO;cACZC,OAAO,EAAGgB,CAAC,IAAK;gBACdA,CAAC,CAACC,eAAe,CAAC,CAAC;gBACnBxH,aAAa,CAAC5C,EAAE,CAAC;cACnB,CAAE;cACFqJ,KAAK,EAAE9N,SAAS,CAAC,gBAAgB,CAAE;cACnC+L,EAAE,EAAE;gBACFQ,KAAK,EAAE,MAAM;gBACb5D,MAAM,EAAE,MAAM;gBACd6E,eAAe,EAAE,aAAa;gBAC9B,SAAS,EAAE;kBACTA,eAAe,EAAE,0BAA0B;kBAC3CZ,SAAS,EAAE;gBACb,CAAC;gBACDS,UAAU,EAAE;cACd,CAAE;cAAApC,QAAA,eAEF/L,OAAA;gBACEiP,uBAAuB,EAAE;kBAAEC,MAAM,EAAErP;gBAAS,CAAE;gBAC9CoO,KAAK,EAAE;kBAAExE,MAAM,EAAE,MAAM;kBAAE4D,KAAK,EAAE;gBAAO;cAAE;gBAAA8B,QAAA,EAAAtQ,YAAA;gBAAAuQ,UAAA;gBAAAC,YAAA;cAAA,OAC1C;YAAC;cAAAF,QAAA,EAAAtQ,YAAA;cAAAuQ,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC;UAAA;YAAAF,QAAA,EAAAtQ,YAAA;YAAAuQ,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;UAAA;UAEN;UACArP,OAAA,CAACR,UAAU;YACTiP,IAAI,EAAC,OAAO;YACZ5B,EAAE,EAAE;cACFQ,KAAK,EAAE,MAAM;cACb5D,MAAM,EAAE,MAAM;cACd6E,eAAe,EAAE,2BAA2B;cAC5CT,MAAM,EAAE,mBAAmB;cAC3BC,YAAY,EAAE,KAAK;cACnBC,SAAS,EAAE,8BAA8B;cACzC,SAAS,EAAE;gBACTO,eAAe,EAAE,wBAAwB;gBACzCP,SAAS,EAAE,gCAAgC;gBAC3CL,SAAS,EAAE;cACb,CAAC;cACDS,UAAU,EAAE;YACd,CAAE;YACFS,KAAK,EAAE9N,SAAS,CAAC,cAAc,CAAE;YAAAiL,QAAA,eAEjC/L,OAAA,CAACN,aAAa;cAACmN,EAAE,EAAE;gBAAE+C,QAAQ,EAAE,MAAM;gBAAEC,KAAK,EAAE;cAAU;YAAE;cAAAV,QAAA,EAAAtQ,YAAA;cAAAuQ,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAF,QAAA,EAAAtQ,YAAA;YAAAuQ,UAAA;YAAAC,YAAA;UAAA,OACnD;QACb;UAAAF,QAAA,EAAAtQ,YAAA;UAAAuQ,UAAA;UAAAC,YAAA;QAAA,OACE,CACN,eAGDrP,OAAA;UAAKuF,EAAE,EAAE,OAAOA,EAAE,EAAG;UAAC0I,KAAK,EAAE;YAAEZ,KAAK,EAAE,MAAM;YAAEL,QAAQ,EAAE;UAAW,CAAE;UAAAjB,QAAA,gBACnE/L,OAAA;YAAA+L,QAAA,EAAQW;UAAU;YAAAyC,QAAA,EAAAtQ,YAAA;YAAAuQ,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC3BrP,OAAA,CAACP,WAAW;YACVmB,GAAG,EAAE6L,gBAAiB;YACtBqD,KAAK,EAAEvE,OAAQ;YACf7C,MAAM,EAAEA,MAAO;YACfqH,QAAQ,EAAG9J,UAAU,IAAKD,YAAY,CAACC,UAAU,EAAErD,KAAK,EAAE2C,EAAE;UAAE;YAAA4J,QAAA,EAAAtQ,YAAA;YAAAuQ,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC;QAAA;UAAAF,QAAA,EAAAtQ,YAAA;UAAAuQ,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA,GAjPD9J,EAAE;QAAA4J,QAAA,EAAAtQ,YAAA;QAAAuQ,UAAA;QAAAC,YAAA;MAAA,OAkPJ,CAAC;IAEV,CAAC;EAAC,gBACF,CAAC;AAEP,CAAC;EAAA,QA1tB0BvP,cAAc,EAgBnCH,cAAc;AAAA,EA2sBtB,CAAC;EAAA,QA3tB4BG,cAAc,EAgBnCH,cAAc;AAAA,EA2sBrB;AAAAqQ,GAAA,GA7tBK7P,UAAqC;AA+tB3C,eAAeA,UAAU;AAAA,IAAAC,EAAA,EAAA4P,GAAA;AAAAC,YAAA,CAAA7P,EAAA;AAAA6P,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}